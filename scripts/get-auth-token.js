const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';



async function registerTestUser() {
  console.log('👤 Creating test user...');

  const testUser = {
    first_name: 'Test',
    last_name: 'User',
    email: '<EMAIL>',
    password: 'TestPass123!@',
    phone: '+265999123456'
  };

  try {
    const response = await axios.post(`${API_BASE_URL}/auth/register`, testUser);
    console.log('✅ Test user created successfully!');
    return testUser;
  } catch (error) {
    console.log('⚠️ User creation failed:', error.response?.data?.message || error.message);
    if (error.response?.data?.message?.includes('already exists')) {
      console.log('💡 User already exists, proceeding with login...');
      return testUser;
    }
    return null;
  }
}

async function getAuthToken() {
  console.log('🔐 Getting Authentication Token for Swagger Testing\n');

  // First try to create a test user
  const testUser = await registerTestUser();

  if (testUser) {
    testCredentials.unshift({ email: testUser.email, password: testUser.password });
  }

  for (const credentials of testCredentials) {
    try {
      console.log(`🔄 Trying login with: ${credentials.email}`);

      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: credentials.email,
        password: credentials.password
      });

      if (response.data && response.data.access_token) {
        console.log('✅ Login successful!');
        console.log('📋 JWT Token:', response.data.access_token);
        console.log('\n🎯 How to use in Swagger:');
        console.log('1. Open Swagger UI: http://localhost:3001/api');
        console.log('2. Click the "Authorize" button (🔒 icon)');
        console.log('3. Enter this in the value field:');
        console.log(`   Bearer ${response.data.access_token}`);
        console.log('4. Click "Authorize"');
        console.log('5. Now you can test the Application Status API endpoints!');

        return response.data.access_token;
      }
    } catch (error) {
      console.log(`❌ Failed with ${credentials.email}:`, error.response?.data?.message || error.message);
    }
  }

  console.log('\n💡 If all login attempts failed, you may need to:');
  console.log('1. Check if there are users in the database');
  console.log('2. Create a test user first');
  console.log('3. Use the correct credentials');
  console.log('\n📝 You can also try these endpoints in Swagger:');
  console.log('- POST /auth/register (if available) - to create a new user');
  console.log('- POST /users (if available) - to create a new user');
}

async function testApplicationStatusAPI(token) {
  if (!token) {
    console.log('❌ No token available for testing');
    return;
  }

  console.log('\n🧪 Testing Application Status API with token...');

  const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  try {
    // Test getting available statuses
    console.log('📊 Testing GET /application-status/statuses');
    const statusesResponse = await apiClient.get('/application-status/statuses');
    console.log('✅ Available statuses:', statusesResponse.data.data.length);
    statusesResponse.data.data.forEach(status => {
      console.log(`   - ${status.value}: ${status.description}`);
    });

  } catch (error) {
    console.log('❌ API test failed:', error.response?.data?.message || error.message);
  }
}

// Run the authentication
getAuthToken().then(token => {
  if (token) {
    testApplicationStatusAPI(token);
  }
}).catch(error => {
  console.error('💥 Error:', error.message);
});

