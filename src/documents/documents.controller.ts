import {
  Controller,
  Get,
  Post,
  Put,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseEnumPipe,
  UseInterceptors,
  UploadedFile,
  Res,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { extname } from 'path';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DocumentsService } from './documents.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { Documents } from '../entities/documents.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { DataSource } from 'typeorm';

@ApiTags('Documents')
@Controller('documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DocumentsController {
  constructor(
    private readonly documentsService: DocumentsService,
    private readonly dataSource: DataSource
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new document' })
  @ApiResponse({
    status: 201,
    description: 'Document created successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Created new document',
  })
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    @Request() req: any,
  ): Promise<Documents> {
    return this.documentsService.create(createDocumentDto, req.user.userId);
  }

  @Post('upload')
  @ApiOperation({ summary: 'Upload a document file' })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
    type: Documents,
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(), // Use memory storage for MinIO
      fileFilter: (req, file, callback) => {
        // Allow common document types
        const allowedMimes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'image/jpeg',
          'image/png',
          'image/gif',
          'text/plain',
        ];

        if (allowedMimes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new Error('Invalid file type'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
    }),
  )
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Uploaded document file',
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadData: any,
    @Request() req: any,
  ): Promise<any> {
    if (!file) {
      throw new Error('No file uploaded');
    }

    // Validate UUID fields
    const isValidUUID = (value: string): boolean => {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(value);
    };

    // Handle entity_id - if it's "temp" or invalid UUID, set to null
    let entityId = uploadData.entity_id;
    if (entityId && (entityId === 'temp' || !isValidUUID(entityId))) {
      entityId = null;
    }

    // Handle application_id - if it's "temp" or invalid UUID, set to null
    let applicationId = uploadData.application_id;
    if (applicationId && (applicationId === 'temp' || !isValidUUID(applicationId))) {
      applicationId = null;
    }

    const createDocumentDto: CreateDocumentDto = {
      document_type: uploadData.document_type || 'OTHER',
      file_name: uploadData.file_name || file.originalname,
      entity_type: uploadData.entity_type,
      entity_id: entityId,
      is_required: uploadData.is_required === 'true',
      file_path: '',
      file_size: 0,
      mime_type: ''
    };

    const document = await this.documentsService.uploadFile(file, createDocumentDto, req.user.userId);

    return {
      success: true,
      message: 'Document uploaded successfully',
      data: document,
    };
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approve a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({
    status: 200,
    description: 'Document approved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async approveDocument(
    @Param('id') id: string,
    @Body() approvalData: { comment?: string },
    @Request() req: any,
  ): Promise<any> {
    const document = await this.documentsService.approveDocument(
      id,
      req.user.userId,
      approvalData.comment
    );

    return {
      success: true,
      message: 'Document approved successfully',
      data: document,
    };
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({
    status: 200,
    description: 'Document rejected successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async rejectDocument(
    @Param('id') id: string,
    @Body() rejectionData: { comment?: string },
    @Request() req: any,
  ): Promise<any> {
    const document = await this.documentsService.rejectDocument(
      id,
      req.user.userId,
      rejectionData.comment
    );

    return {
      success: true,
      message: 'Document rejected successfully',
      data: document,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all documents with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents list',
  })
  async findAll(@Paginate() query: PaginateQuery, @Request() req: any): Promise<PaginatedResult<Documents>> {
    // Extract user roles and userId from the request
    const userRoles = req.user?.roles || [];
    const userId = req.user?.userId;
    const result = await this.documentsService.findAll(query, userRoles, userId);
    return PaginationTransformer.transform<Documents>(result);
  }

  @Get(':id/url')
  @ApiOperation({ summary: 'Get a presigned URL for document access' })
  @ApiResponse({
    status: 200,
    description: 'Presigned URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        url: { type: 'string' },
        expiresIn: { type: 'number' },
      },
    },
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Generated presigned URL for document',
  })
  async getFileUrl(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('expiry') expiry?: string
  ): Promise<any> {
    const document = await this.documentsService.findOne(id);

    if (!document) {
      throw new Error('Document not found');
    }

    const expirySeconds = expiry ? parseInt(expiry) : 24 * 60 * 60; // Default 24 hours
    const url = await this.documentsService.getFileUrl(document.file_path, expirySeconds);

    return {
      success: true,
      data: {
        url,
        expiresIn: expirySeconds,
        fileName: document.file_name,
        mimeType: document.mime_type,
      },
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get document statistics' })
  @ApiResponse({
    status: 200,
    description: 'Document statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed document statistics',
  })
  async getStats(): Promise<any> {
    return this.documentsService.getDocumentStats();
  }

  @Get('total-file-size')
  @ApiOperation({ summary: 'Get total file size of all documents' })
  @ApiResponse({
    status: 200,
    description: 'Total file size retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed total file size',
  })
  async getTotalFileSize(): Promise<{ totalSize: number }> {
    const totalSize = await this.documentsService.getTotalFileSize();
    return { totalSize };
  }

  @Get('by-application/:applicationId')
  @ApiOperation({ summary: 'Get documents by application' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by application',
  })
  async findByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<Documents[]> {
    return this.documentsService.findByApplication(applicationId);
  }

  @Get('by-entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get documents by entity' })
  @ApiParam({ name: 'entityType', description: 'Entity type' })
  @ApiParam({ name: 'entityId', description: 'Entity UUID' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by entity',
  })
  async findByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
  ): Promise<Documents[]> {
    return this.documentsService.findByEntity(entityType, entityId);
  }

  @Get('by-document-type/:documentType')
  @ApiOperation({ summary: 'Get documents by document type' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by type',
  })
  async findByDocumentType(
    @Param('documentType') documentType: string,
  ): Promise<Documents[]> {
    return this.documentsService.findByDocumentType(documentType);
  }

  @Get('by-mime-type/:mimeType')
  @ApiOperation({ summary: 'Get documents by MIME type' })
  @ApiParam({ name: 'mimeType', description: 'MIME type' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by MIME type',
  })
  async findByMimeType(@Param('mimeType') mimeType: string): Promise<Documents[]> {
    return this.documentsService.getDocumentsByMimeType(mimeType);
  }

  @Get('required')
  @ApiOperation({ summary: 'Get required documents' })
  @ApiResponse({
    status: 200,
    description: 'Required documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed required documents',
  })
  async findRequiredDocuments(): Promise<Documents[]> {
    return this.documentsService.findRequiredDocuments();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get document by ID' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document retrieved successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed document details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Documents> {
    return this.documentsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document updated successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Updated document',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
    @Request() req: any,
  ): Promise<Documents> {
    return this.documentsService.update(id, updateDocumentDto, req.user.userId);
  }

  @Get(':id/download')
  @ApiOperation({ summary: 'Download document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document downloaded successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Downloaded document',
  })
  async downloadDocument(@Param('id', ParseUUIDPipe) id: string, @Res() res: any): Promise<void> {
    const document = await this.documentsService.findOne(id);
    const fileStream = await this.documentsService.getFileStream(document.file_path);

    res.set({
      'Content-Type': document.mime_type,
      'Content-Disposition': `attachment; filename="${document.file_name}"`,
    });

    fileStream.pipe(res);
  }

  @Get(':id/preview')
  @ApiOperation({ summary: 'Preview document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document preview retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Previewed document',
  })
  async previewDocument(@Param('id', ParseUUIDPipe) id: string, @Res() res: any): Promise<void> {
    const document = await this.documentsService.findOne(id);

    // Check if document is previewable
    const previewableMimeTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'text/html',
      'text/css',
      'text/javascript',
      'application/json',
    ];

    if (!previewableMimeTypes.includes(document.mime_type)) {
      res.status(400).json({
        message: 'Document type not supported for preview',
        supportedTypes: previewableMimeTypes
      });
      return;
    }

    const fileStream = await this.documentsService.getFileStream(document.file_path);

    res.set({
      'Content-Type': document.mime_type,
      'Content-Disposition': `inline; filename="${document.file_name}"`,
    });

    fileStream.pipe(res);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Deleted document',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.documentsService.remove(id);
    return { message: 'Document deleted successfully' };
  }

  @Get('health-check')
  @ApiOperation({ summary: 'Check document service health' })
  @ApiResponse({
    status: 200,
    description: 'Service health status',
  })
  async healthCheck(): Promise<{ status: string; services: any }> {
    try {
      // Check MinIO connection
      const minioStatus = await this.documentsService.checkMinioConnection();

      // Check database connection
      const dbStatus = await this.dataSource.query('SELECT 1 as test');

      return {
        status: 'healthy',
        services: {
          minio: minioStatus ? 'connected' : 'disconnected',
          database: dbStatus ? 'connected' : 'disconnected',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        services: {
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  @Get('user/signature')
  @ApiOperation({ summary: 'Get user signature document' })
  @ApiResponse({
    status: 200,
    description: 'User signature document retrieved successfully',
    type: Object
  })
  @ApiResponse({ status: 404, description: 'No signature found for user' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  async getUserSignature(@Request() req: any): Promise<any> {
    try {
      // Get signature document from polymorphic documents table
      const documents = await this.documentsService.findByEntity('user', req.user.userId);
      const signatureDoc = documents.find(doc => doc.document_type === 'signature');

      if (!signatureDoc) {
        throw new NotFoundException('No signature found for this user');
      }

      return signatureDoc;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error getting user signature:', error.message);
      throw new BadRequestException('Unable to retrieve signature at this time');
    }
  }

  @Post('user/signature')
  @UseInterceptors(FileInterceptor('signature', {
    storage: memoryStorage(),
    fileFilter: (req, file, cb) => {
      // Allow only image files
      if (file.mimetype.match(/\/(jpg|jpeg|png|gif|svg\+xml)$/)) {
        cb(null, true);
      } else {
        cb(new Error('Only image files are allowed!'), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
  }))
  @ApiOperation({ summary: 'Upload user signature' })
  @ApiResponse({
    status: 201,
    description: 'Signature uploaded successfully',
    type: Object
  })
  @ApiResponse({ status: 400, description: 'Invalid file or validation errors' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Uploaded user signature',
  })
  async uploadSignature(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any
  ): Promise<any> {
    if (!file) {
      throw new BadRequestException('No signature file provided');
    }

    try {
      // Delete existing signature if it exists
      try {
        const documents = await this.documentsService.findByEntity('user', req.user.userId);
        const existingSignature = documents.find(doc => doc.document_type === 'signature');
        if (existingSignature) {
          await this.documentsService.remove(existingSignature.document_id);
        }
      } catch (error) {
        // Ignore if no existing signature
        console.log('No existing signature to delete');
      }

      // Upload new signature document
      const documentData = {
        document_type: 'signature',
        entity_type: 'user',
        entity_id: req.user.userId,
        is_required: false,
        file_name: file.originalname,
        file_path: '', // Will be set by uploadFile method
        file_size: 0, // Will be set by uploadFile method
        mime_type: '' // Will be set by uploadFile method
      };

      const result = await this.documentsService.uploadFile(file, documentData, req.user.userId);

      return {
        document: result,
        message: 'Signature uploaded successfully'
      };
    } catch (error) {
      console.error('Error uploading user signature:', error.message);
      throw new BadRequestException('Unable to upload signature at this time');
    }
  }

  @Delete('user/signature')
  @ApiOperation({ summary: 'Delete user signature' })
  @ApiResponse({ status: 200, description: 'Signature deleted successfully' })
  @ApiResponse({ status: 404, description: 'No signature found for user' })
  @ApiResponse({ status: 401, description: 'Authentication required' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Deleted user signature',
  })
  async deleteSignature(@Request() req: any): Promise<void> {
    try {
      // Get signature document
      const documents = await this.documentsService.findByEntity('user', req.user.userId);
      const signatureDoc = documents.find(doc => doc.document_type === 'signature');

      if (!signatureDoc) {
        throw new NotFoundException('No signature found for this user');
      }

      // Delete the document
      await this.documentsService.remove(signatureDoc.document_id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting user signature:', error.message);
      throw new BadRequestException('Unable to delete signature at this time');
    }
  }
}
