/**
 * Device approval status enum for validation in DTOs
 */
export enum DeviceApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  UNDER_REVIEW = 'under_review',
  REQUIRES_DOCUMENTATION = 'requires_documentation',
}

/**
 * Valid device approval status values as array for validation
 */
export const DEVICE_APPROVAL_STATUS_VALUES = Object.values(DeviceApprovalStatus);

/**
 * Device approval status descriptions for documentation
 */
export const DEVICE_APPROVAL_STATUS_DESCRIPTIONS = {
  [DeviceApprovalStatus.PENDING]: 'Device is pending review',
  [DeviceApprovalStatus.APPROVED]: 'Device has been approved for use',
  [DeviceApprovalStatus.REJECTED]: 'Device has been rejected',
  [DeviceApprovalStatus.UNDER_REVIEW]: 'Device is currently under review',
  [DeviceApprovalStatus.REQUIRES_DOCUMENTATION]: 'Device requires additional documentation',
};
