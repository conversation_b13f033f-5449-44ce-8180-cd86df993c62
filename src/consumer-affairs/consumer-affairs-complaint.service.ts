import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import {
  ConsumerAffairsComplaint,
  ConsumerAffairsComplaintAttachment,
  ConsumerAffairsComplaintStatusHistory,
} from 'src/entities/consumer-affairs-complaint.entity';
import { ConsumerAffairsComplaintFilterDto, CreateConsumerAffairsComplaintAttachmentDto, CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintStatusDto, UpdateConsumerAffairsComplaineeDto } from 'src/dto/consumer-affairs/consumer-affairs-complaint.dto';
import { ConsumerAffairsComplaintResponseDto, ComplaintStatus, ComplaintCategory } from './consumer-affairs-constants';
import { EmailTemplateService } from 'src/notifications/email-template.service';
import { NotificationHelperService } from 'src/notifications/notification-helper.service';
import { DocumentsService } from 'src/documents/documents.service';
import { CreateDocumentDto } from 'src/dto/document/create-document.dto';
import { User } from 'src/entities/user.entity';


@Injectable()
export class ConsumerAffairsComplaintService {
  private readonly logger = new Logger(ConsumerAffairsComplaintService.name);

  constructor(
    @InjectRepository(ConsumerAffairsComplaint)
    private complaintRepository: Repository<ConsumerAffairsComplaint>,
    @InjectRepository(ConsumerAffairsComplaintAttachment)
    private attachmentRepository: Repository<ConsumerAffairsComplaintAttachment>,
    @InjectRepository(ConsumerAffairsComplaintStatusHistory)
    private statusHistoryRepository: Repository<ConsumerAffairsComplaintStatusHistory>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private emailTemplateService: EmailTemplateService,
    private notificationHelperService: NotificationHelperService,
    private documentsService: DocumentsService,
  ) {}

  async create(
    createDto: CreateConsumerAffairsComplaintDto,
    complainantId: string
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = this.complaintRepository.create({
      ...createDto,
      complainant_id: complainantId,
      created_by: complainantId,
    });

    const savedComplaint = await this.complaintRepository.save(complaint);

    // Create initial status history entry
    await this.createStatusHistory(
      savedComplaint.complaint_id,
      ComplaintStatus.SUBMITTED,
      'Complaint submitted',
      complainantId
    );

    // Send email notification to complainant
    await this.sendComplaintSubmissionNotification(savedComplaint, complainantId);

    return this.findOne(savedComplaint.complaint_id, complainantId);
  }

  async findAll(
    query: PaginateQuery,
    userId: string,
    isStaff: boolean = false
  ): Promise<Paginated<ConsumerAffairsComplaint>> {
    const queryBuilder = this.complaintRepository
      .createQueryBuilder('complaint')
      .leftJoinAndSelect('complaint.complainant', 'complainant')
      .leftJoinAndSelect('complaint.assignee', 'assignee')
      .leftJoinAndSelect('complaint.attachments', 'attachments')
      .leftJoinAndSelect('complaint.status_history', 'status_history')
      .orderBy('status_history.created_at', 'ASC');

    // Data isolation: customers can only see their own complaints
    if (!isStaff) {
      queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
    }

    return paginate(query, queryBuilder, {
      sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'category'],
      searchableColumns: ['title', 'description', 'complaint_number'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        status: true,
        category: true,
        priority: true,
      },
    });
  }

  async findOne(
    complaintId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const queryBuilder = this.createQueryBuilder()
      .where('complaint.complaint_id = :complaintId', { complaintId });

    // Data isolation: customers can only see their own complaints
    if (!isStaff) {
      queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
    }

    const complaint = await queryBuilder.getOne();

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    return this.mapToResponseDto(complaint);
  }

  async update(
    complaintId: string, 
    updateDto: UpdateConsumerAffairsComplaintDto,
    userId: string,
    isStaff: boolean = false
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
      relations: ['complainant'],
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Data isolation: customers can only update their own complaints
    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only update your own complaints');
    }

    // Customers can only update certain fields
    if (!isStaff) {
      const allowedFields = ['title', 'description', 'category'];
      const updateFields = Object.keys(updateDto);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
      }
    }

    // Track status changes
    if (updateDto.status && updateDto.status !== complaint.status) {
      await this.createStatusHistory(
        complaintId,
        updateDto.status,
        `Status changed from ${complaint.status} to ${updateDto.status}`,
        userId
      );

      if (updateDto.status === ComplaintStatus.RESOLVED) {
        updateDto.resolved_at = new Date();
      }
    }

    Object.assign(complaint, updateDto);
    complaint.updated_by = userId;

    await this.complaintRepository.save(complaint);

    return this.findOne(complaintId, userId, isStaff);
  }

  async delete(
    complaintId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<void> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Data isolation: customers can only delete their own complaints
    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only delete your own complaints');
    }

    // Soft delete
    await this.complaintRepository.softDelete(complaintId);
  }

  async addAttachment(
    attachmentDto: CreateConsumerAffairsComplaintAttachmentDto,
    userId: string
  ): Promise<ConsumerAffairsComplaintAttachment> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: attachmentDto.complaint_id },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    const attachment = this.attachmentRepository.create({
      ...attachmentDto,
      uploaded_by: userId,
    });

    return this.attachmentRepository.save(attachment);
  }

  async uploadAttachments(
    complaintId: string,
    files: Express.Multer.File[],
    userId: string
  ): Promise<ConsumerAffairsComplaintAttachment[]> {
    // Verify complaint exists and user has permission
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
      relations: ['complainant'],
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Check if user is the complainant or staff
    const user = await this.userRepository.findOne({
      where: { user_id: userId },
      relations: ['roles'],
    });

    const isStaff = user?.roles?.some(role =>
      ['admin', 'administrator', 'staff', 'moderator', 'manager'].includes(role.name.toLowerCase())
    );

    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only upload attachments to your own complaints');
    }

    const uploadedAttachments: ConsumerAffairsComplaintAttachment[] = [];

    for (const file of files) {
      try {
        // Create document using DocumentsService
        const createDocumentDto: CreateDocumentDto = {
          document_type: 'COMPLAINT_ATTACHMENT',
          file_name: file.originalname,
          entity_type: 'consumer_affairs_complaint',
          entity_id: complaintId,
          file_path: '', // Will be set by DocumentsService
          file_size: file.size,
          mime_type: file.mimetype,
          is_required: false,
        };

        const document = await this.documentsService.uploadFile(file, createDocumentDto, userId);

        // Create attachment record
        const attachmentDto: CreateConsumerAffairsComplaintAttachmentDto = {
          complaint_id: complaintId,
          file_name: file.originalname,
          file_path: document.file_path,
          file_type: file.mimetype,
          file_size: file.size,
        };

        const attachment = await this.addAttachment(attachmentDto, userId);
        uploadedAttachments.push(attachment);

        this.logger.log(`File uploaded successfully: ${file.originalname} for complaint ${complaintId}`);
      } catch (error) {
        this.logger.error(`Failed to upload file ${file.originalname}: ${error.message}`);
        throw new BadRequestException(`Failed to upload file ${file.originalname}: ${error.message}`);
      }
    }

    return uploadedAttachments;
  }

  async getAttachments(complaintId: string, userId: string, isStaff: boolean): Promise<ConsumerAffairsComplaintAttachment[]> {
    // Verify complaint exists and user has permission
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
      relations: ['attachments', 'attachments.uploader'],
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Check permissions
    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only view attachments for your own complaints');
    }

    return complaint.attachments || [];
  }

  async deleteAttachment(
    attachmentId: string,
    userId: string,
    isStaff: boolean
  ): Promise<void> {
    const attachment = await this.attachmentRepository.findOne({
      where: { attachment_id: attachmentId },
      relations: ['complaint'],
    });

    if (!attachment) {
      throw new NotFoundException('Attachment not found');
    }

    // Check permissions - only uploader or staff can delete
    if (!isStaff && attachment.uploaded_by !== userId) {
      throw new ForbiddenException('You can only delete your own attachments');
    }

    // Delete from storage via DocumentsService
    try {
      await this.documentsService.getFileStream(attachment.file_path);
      // If file exists, we could delete it, but for now we'll just remove the record
    } catch (error) {
      this.logger.warn(`File not found in storage: ${attachment.file_path}`);
    }

    await this.attachmentRepository.remove(attachment);
    this.logger.log(`Attachment deleted: ${attachmentId} by user ${userId}`);
  }

  async getAttachmentDownloadUrl(
    attachmentId: string,
    userId: string,
    isStaff: boolean,
    expirySeconds: number = 3600
  ): Promise<{ downloadUrl: string; attachment: ConsumerAffairsComplaintAttachment }> {
    const attachment = await this.attachmentRepository.findOne({
      where: { attachment_id: attachmentId },
      relations: ['complaint'],
    });

    if (!attachment) {
      throw new NotFoundException('Attachment not found');
    }

    // Check permissions
    if (!isStaff && attachment.complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only download attachments from your own complaints');
    }

    // Generate download URL
    const downloadUrl = await this.documentsService.getFileUrl(attachment.file_path, expirySeconds);

    return { downloadUrl, attachment };
  }

  async updateStatus(
    complaintId: string,
    statusDto: UpdateConsumerAffairsComplaintStatusDto,
    userId: string
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
      relations: ['complainant'],
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    const oldStatus = complaint.status;

    // Create status history entry
    await this.createStatusHistory(
      complaintId,
      statusDto.status,
      statusDto.comment,
      userId
    );

    // Update complaint status
    complaint.status = statusDto.status;
    complaint.updated_by = userId;

    if (statusDto.status === ComplaintStatus.RESOLVED) {
      complaint.resolved_at = new Date();
    }

    await this.complaintRepository.save(complaint);

    // Send status update notification to complainant
    await this.sendComplaintStatusUpdateNotification(complaint, oldStatus, statusDto.comment, userId);

    return this.findOne(complaintId, userId, true);
  }

  async updateComplainee(
    complaintId: string,
    complaineeDto: UpdateConsumerAffairsComplaineeDto,
    userId: string
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Update complainee registration number
    complaint.complainee_reg_number = complaineeDto.complainee_reg_number;
    complaint.updated_by = userId;

    await this.complaintRepository.save(complaint);

    // Create status history entry for complainee update
    await this.createStatusHistory(
      complaintId,
      complaint.status as ComplaintStatus,
      `Complainee updated to registration number: ${complaineeDto.complainee_reg_number}`,
      userId
    );

    return this.findOne(complaintId, userId, true);
  }

  /**
   * Send email notification to complainant after complaint submission
   */
  private async sendComplaintSubmissionNotification(
    complaint: ConsumerAffairsComplaint,
    complainantId: string
  ): Promise<void> {
    try {
      this.logger.log(`Sending complaint submission notification for complaint ${complaint.complaint_number}`);

      // Get complainant details
      const complainant = await this.userRepository.findOne({
        where: { user_id: complainantId },
      });

      if (!complainant || !complainant.email) {
        this.logger.warn(`Complainant not found or no email address for complaint ${complaint.complaint_number}`);
        return;
      }

      // Generate email template
      const emailTemplate = this.emailTemplateService.generateComplaintSubmittedTemplate({
        complainantName: `${complainant.first_name} ${complainant.last_name}`,
        complaintNumber: complaint.complaint_number,
        complaintTitle: complaint.title,
        category: complaint.category,
        priority: complaint.priority,
        submissionDate: complaint.created_at.toLocaleDateString('en-GB', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/consumer-affairs/complaints/${complaint.complaint_id}`,
      });

      // Send email notification using the notification helper service
      await this.notificationHelperService.sendEmailNotification({
        recipientId: complainantId,
        recipientEmail: complainant.email,
        recipientName: `${complainant.first_name} ${complainant.last_name}`,
        subject: emailTemplate.subject,
        message: `Your complaint ${complaint.complaint_number} has been successfully submitted and is being processed.`,
        htmlContent: emailTemplate.html,
        entityType: 'consumer_affairs_complaint',
        entityId: complaint.complaint_id,
        actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/consumer-affairs/complaints/${complaint.complaint_id}`,
        recipientType: 'CUSTOMER' as any,
        createdBy: complainantId,
        sendEmail: true,
        createInApp: true,
      });

      this.logger.log(`Complaint submission notification sent successfully for complaint ${complaint.complaint_number}`);
    } catch (error) {
      this.logger.error(`Failed to send complaint submission notification for complaint ${complaint.complaint_number}:`, error);
      // Don't throw error to prevent complaint creation from failing
    }
  }

  /**
   * Send email notification to complainant when complaint status is updated
   */
  private async sendComplaintStatusUpdateNotification(
    complaint: ConsumerAffairsComplaint,
    oldStatus: string,
    comment: string | undefined,
    updatedBy: string
  ): Promise<void> {
    try {
      this.logger.log(`Sending complaint status update notification for complaint ${complaint.complaint_number}`);

      if (!complaint.complainant || !complaint.complainant.email) {
        this.logger.warn(`Complainant not found or no email address for complaint ${complaint.complaint_number}`);
        return;
      }

      const statusMessages = {
        [ComplaintStatus.SUBMITTED]: 'Your complaint has been submitted and is awaiting review.',
        [ComplaintStatus.UNDER_REVIEW]: 'Your complaint is now under review by our team.',
        [ComplaintStatus.INVESTIGATING]: 'We are actively investigating your complaint.',
        [ComplaintStatus.RESOLVED]: 'Your complaint has been resolved.',
        [ComplaintStatus.CLOSED]: 'Your complaint has been closed.',
      };

      const statusMessage = statusMessages[complaint.status as ComplaintStatus] || 'Your complaint status has been updated.';

      // Create a simple email template for status updates
      const subject = `Complaint Update: ${complaint.complaint_number} - Status Changed to ${complaint.status.toUpperCase()} - MACRA`;

      const html = `
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Complaint Status Update - MACRA</title>
          </head>
          <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;">
            <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

              <!-- Header -->
              <div style="background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); padding: 30px 20px; text-align: center;">
                <img src="cid:logo@macra" alt="MACRA Logo" style="height: 60px; margin-bottom: 15px;">
                <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: bold;">Complaint Status Update</h1>
                <p style="color: #e0e7ff; margin: 10px 0 0 0; font-size: 16px;">Your complaint status has been updated</p>
              </div>

              <!-- Content -->
              <div style="padding: 30px 20px;">
                <p style="font-size: 16px; color: #374151; margin-bottom: 20px;">Dear ${complaint.complainant.first_name} ${complaint.complainant.last_name},</p>

                <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 25px;">
                  We wanted to update you on the status of your complaint <strong>${complaint.complaint_number}</strong>.
                </p>

                <!-- Status Update Card -->
                <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                  <h3 style="color: #1e40af; margin: 0 0 15px 0; font-size: 18px;">Status Update</h3>

                  <div style="margin-bottom: 12px;">
                    <strong style="color: #374151;">Previous Status:</strong>
                    <span style="color: #6b7280; text-transform: capitalize;">${oldStatus.replace('_', ' ')}</span>
                  </div>

                  <div style="margin-bottom: 12px;">
                    <strong style="color: #374151;">Current Status:</strong>
                    <span style="color: #1e40af; font-weight: bold; text-transform: capitalize;">${complaint.status.replace('_', ' ')}</span>
                  </div>

                  <div style="margin-bottom: 12px;">
                    <strong style="color: #374151;">Update Date:</strong>
                    <span style="color: #6b7280;">${new Date().toLocaleDateString('en-GB', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}</span>
                  </div>

                  ${comment ? `
                  <div style="margin-bottom: 0;">
                    <strong style="color: #374151;">Comments:</strong>
                    <p style="color: #6b7280; margin: 5px 0 0 0; padding: 10px; background-color: #f3f4f6; border-radius: 4px;">${comment}</p>
                  </div>
                  ` : ''}
                </div>

                <p style="font-size: 16px; color: #374151; line-height: 1.6; margin-bottom: 20px;">
                  ${statusMessage}
                </p>

                <div style="text-align: center; margin: 25px 0;">
                  <a href="${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/consumer-affairs/complaints/${complaint.complaint_id}" style="background-color: #1e40af; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                    View Complaint Details
                  </a>
                </div>

                <p style="font-size: 14px; color: #6b7280; margin-bottom: 20px;">
                  If you have any questions, please contact our Consumer Affairs team:
                </p>
                <ul style="font-size: 14px; color: #6b7280; margin-bottom: 20px;">
                  <li>Email: <EMAIL></li>
                  <li>Phone: +265 1 770 100</li>
                  <li>Website: www.macra.mw</li>
                </ul>

                <p style="font-size: 16px; color: #374151; margin-top: 20px;">
                  Best regards,<br>
                  <strong>MACRA Consumer Affairs Team</strong><br>
                  Malawi Communications Regulatory Authority
                </p>
              </div>

              <!-- Footer -->
              <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
                <p style="margin: 0; font-size: 12px; color: #6c757d;">
                  This is an automated message from MACRA. Please do not reply to this email.<br>
                  © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
                </p>
              </div>
            </div>
          </body>
        </html>
      `;

      // Send email notification using the notification helper service
      await this.notificationHelperService.sendEmailNotification({
        recipientId: complaint.complainant_id,
        recipientEmail: complaint.complainant.email,
        recipientName: `${complaint.complainant.first_name} ${complaint.complainant.last_name}`,
        subject,
        message: `Your complaint ${complaint.complaint_number} status has been updated to ${complaint.status}.`,
        htmlContent: html,
        entityType: 'consumer_affairs_complaint',
        entityId: complaint.complaint_id,
        actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/consumer-affairs/complaints/${complaint.complaint_id}`,
        recipientType: 'CUSTOMER' as any,
        createdBy: updatedBy,
        sendEmail: true,
        createInApp: true,
      });

      this.logger.log(`Complaint status update notification sent successfully for complaint ${complaint.complaint_number}`);
    } catch (error) {
      this.logger.error(`Failed to send complaint status update notification for complaint ${complaint.complaint_number}:`, error);
      // Don't throw error to prevent status update from failing
    }
  }

  private async createStatusHistory(
    complaintId: string,
    status: ComplaintStatus,
    comment: string | undefined,
    userId: string
  ): Promise<void> {
    const statusHistory = this.statusHistoryRepository.create({
      complaint_id: complaintId,
      status,
      comment,
      created_by: userId,
    });

    await this.statusHistoryRepository.save(statusHistory);
  }

  private createQueryBuilder(): SelectQueryBuilder<ConsumerAffairsComplaint> {
    return this.complaintRepository
      .createQueryBuilder('complaint')
      .leftJoinAndSelect('complaint.complainant', 'complainant')
      .leftJoinAndSelect('complaint.assignee', 'assignee')
      .leftJoinAndSelect('complaint.attachments', 'attachments')
      .leftJoinAndSelect('complaint.status_history', 'status_history')
      .leftJoinAndSelect('status_history.creator', 'history_creator');
  }

  private applyFilters(
    queryBuilder: SelectQueryBuilder<ConsumerAffairsComplaint>,
    filters: Partial<ConsumerAffairsComplaintFilterDto>
  ): void {
    if (filters.category) {
      queryBuilder.andWhere('complaint.category = :category', { category: filters.category });
    }

    if (filters.status) {
      queryBuilder.andWhere('complaint.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      queryBuilder.andWhere('complaint.priority = :priority', { priority: filters.priority });
    }

    if (filters.assigned_to) {
      queryBuilder.andWhere('complaint.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
    }

    if (filters.from_date) {
      queryBuilder.andWhere('complaint.created_at >= :from_date', { from_date: filters.from_date });
    }

    if (filters.to_date) {
      queryBuilder.andWhere('complaint.created_at <= :to_date', { to_date: filters.to_date });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(complaint.title ILIKE :search OR complaint.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
  }

  private mapToResponseDto(complaint: ConsumerAffairsComplaint): ConsumerAffairsComplaintResponseDto {
    return {
      complaint_id: complaint.complaint_id,
      complaint_number: complaint.complaint_number,
      complainant_id: complaint.complainant_id,
      complainee_reg_number: complaint.complainee_reg_number,
      title: complaint.title,
      description: complaint.description,
      category: complaint.category,
      status: complaint.status,
      priority: complaint.priority,
      assigned_to: complaint.assigned_to,
      resolution: complaint.resolution,
      resolved_at: complaint.resolved_at,
      created_at: complaint.created_at,
      updated_at: complaint.updated_at,
      complainant: complaint.complainant ? {
        user_id: complaint.complainant.user_id,
        first_name: complaint.complainant.first_name,
        last_name: complaint.complainant.last_name,
        email: complaint.complainant.email,
      } : undefined,
      assignee: complaint.assignee ? {
        user_id: complaint.assignee.user_id,
        first_name: complaint.assignee.first_name,
        last_name: complaint.assignee.last_name,
        email: complaint.assignee.email,
      } : undefined,
      attachments: complaint.attachments?.map(attachment => ({
        attachment_id: attachment.attachment_id,
        file_name: attachment.file_name,
        file_type: attachment.file_type,
        file_size: attachment.file_size,
        uploaded_at: attachment.uploaded_at,
      })),
      status_history: complaint.status_history?.map(history => ({
        history_id: history.history_id,
        status: history.status,
        comment: history.comment,
        created_at: history.created_at,
        creator: {
          user_id: history.creator.user_id,
          first_name: history.creator.first_name,
          last_name: history.creator.last_name,
        },
      })),
    };
  }
}
