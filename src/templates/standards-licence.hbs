<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standards Compliance Certificate - {{licenseNumber}}</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #d32f2f;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
        }
        
        .authority-name {
            font-size: 24px;
            font-weight: bold;
            color: #d32f2f;
            margin: 10px 0;
        }
        
        .authority-subtitle {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        
        .certificate-title {
            font-size: 28px;
            font-weight: bold;
            color: #d32f2f;
            text-align: center;
            margin: 30px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .license-info {
            background: #f8f9fa;
            border: 2px solid #d32f2f;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .license-number {
            font-size: 20px;
            font-weight: bold;
            color: #d32f2f;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-item {
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 3px;
        }
        
        .info-value {
            color: #333;
            font-size: 14px;
        }
        
        .applicant-section {
            margin: 25px 0;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #d32f2f;
            border-bottom: 2px solid #d32f2f;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .conditions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .conditions-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .conditions-text {
            font-size: 13px;
            line-height: 1.5;
            color: #856404;
        }
        
        .footer {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .signature-section {
            text-align: center;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin: 40px 20px 10px;
            padding-top: 10px;
        }
        
        .signature-title {
            font-weight: bold;
            font-size: 14px;
        }
        
        .signature-name {
            font-size: 12px;
            color: #666;
        }
        
        .qr-section {
            text-align: center;
            margin-top: 20px;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .qr-text {
            font-size: 11px;
            color: #666;
            margin-top: 10px;
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72px;
            color: rgba(211, 47, 47, 0.1);
            z-index: -1;
            font-weight: bold;
        }
        
        @media print {
            .watermark {
                position: absolute;
            }
        }
    </style>
</head>
<body>
    <div class="watermark">MACRA</div>
    
    <div class="header">
        <div class="logo">
            {{#if macraLogoDataUrl}}
            <img src="{{macraLogoDataUrl}}" alt="MACRA Logo" width="80" height="80">
            {{else}}
            <!-- MACRA Logo fallback -->
            <svg width="80" height="80" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50" cy="50" r="45" stroke="#d32f2f" stroke-width="4" fill="white"/>
                <text x="50" y="55" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#d32f2f">MACRA</text>
            </svg>
            {{/if}}
        </div>
        <div class="authority-name">MALAWI COMMUNICATIONS REGULATORY AUTHORITY</div>
        <div class="authority-subtitle">Established under the Communications Act, 2016</div>
        <div class="authority-subtitle">P.O. Box 261, Blantyre, Malawi | Tel: +265 1 770 100 | www.macra.mw</div>
    </div>
    
    <div class="certificate-title">Standards Compliance Certificate</div>
    
    <div class="license-info">
        <div class="license-number">Certificate No: {{licenseNumber}}</div>
        
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Issue Date:</div>
                <div class="info-value">{{formatDate issueDate 'long'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Expiry Date:</div>
                <div class="info-value">{{formatDate expiryDate 'long'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Certificate Type:</div>
                <div class="info-value">{{licenseType.name}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Status:</div>
                <div class="info-value">{{uppercase status}}</div>
            </div>
        </div>
    </div>
    
    <div class="applicant-section">
        <div class="section-title">Certificate Holder Information</div>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Organization Name:</div>
                <div class="info-value">{{fallback applicant.organization.name applicant.name}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Registration Number:</div>
                <div class="info-value">{{fallback applicant.organization.registration_number applicant.business_registration_number}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Contact Person:</div>
                <div class="info-value">{{applicant.name}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Email:</div>
                <div class="info-value">{{fallback applicant.organization.email applicant.email}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Phone:</div>
                <div class="info-value">{{fallback applicant.organization.phone applicant.phone}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Address:</div>
                <div class="info-value">{{fallback applicant.organization.physical_address.full_address applicant.place_incorporation}}</div>
            </div>
        </div>
    </div>
    
    <div class="applicant-section">
        <div class="section-title">Certificate Details</div>
        <div class="info-item">
            <div class="info-label">Category:</div>
            <div class="info-value">{{category.name}}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Description:</div>
            <div class="info-value">{{category.description}}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Authorizes:</div>
            <div class="info-value">{{category.authorizes}}</div>
        </div>
    </div>
    
    {{#if conditions}}
    <div class="conditions">
        <div class="conditions-title">Terms and Conditions:</div>
        <div class="conditions-text">{{conditions}}</div>
    </div>
    {{/if}}
    
    <div class="footer">
        <div class="signature-section">
            <div class="signature-line">
                <div class="signature-title">Daud Suleman</div>
                <div class="signature-name">Director General</div>
                <div class="signature-name">MACRA</div>
            </div>
        </div>
        
        <div class="qr-section">
            {{#if qrCodeDataUrl}}
            <img src="{{qrCodeDataUrl}}" alt="Verification QR Code" class="qr-code">
            <div class="qr-text">
                Scan to verify certificate authenticity<br>
                or visit: {{verificationUrl}}
            </div>
            {{/if}}
        </div>
    </div>
</body>
</html>
