import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { LicensesService } from './licenses.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateLicenseDto } from '../dto/licenses/create-license.dto';
import { UpdateLicenseDto } from '../dto/licenses/update-license.dto';
import { Licenses } from '../entities/licenses.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { Response } from 'express';

@ApiTags('Licences')
@Controller('licenses')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicensesController {
  private readonly logger = new Logger(LicensesController.name);

  constructor(private readonly licensesService: LicensesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new licence' })
  @ApiResponse({
    status: 201,
    description: 'Licence created successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 409,
    description: 'Licence with this number already exists',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Created new licence',
  })
  async create(
    @Body() createLicenseDto: CreateLicenseDto,
    @Request() req: any,
  ): Promise<Licenses> {
    return this.licensesService.create(createLicenseDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all licences with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Licences retrieved successfully',
  })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Request() req: any,
  ): Promise<PaginatedResult<Licenses>> {
    const userRoles = req.user?.roles || [];
    const userId = req.user?.userId;
    return this.licensesService.findAll(query, userRoles, userId);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get licence statistics' })
  @ApiResponse({
    status: 200,
    description: 'Licence statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence statistics',
  })
  async getStats(): Promise<any> {
    return this.licensesService.getLicenseStats();
  }

  @Get('expiring-soon')
  @ApiOperation({ summary: 'Get licences expiring soon' })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days ahead to check (default: 30)' })
  @ApiResponse({
    status: 200,
    description: 'Expiring licences retrieved successfully',
    type: [Licenses],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed expiring licences',
  })
  async findExpiringSoon(@Query('days') days?: number): Promise<Licenses[]> {
    return this.licensesService.findExpiringSoon(days ? parseInt(days.toString()) : 30);
  }

  @Get('by-application/:applicationId')
  @ApiOperation({ summary: 'Get licences by application' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licences retrieved successfully',
    type: [Licenses],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licences by applicant',
  })
  async findByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<Licenses[]> {
    return this.licensesService.findByApplicationId(applicationId);
  }

  @Get('by-number/:licenseNumber')
  @ApiOperation({ summary: 'Get licence by licence number' })
  @ApiParam({ name: 'licenseNumber', description: 'Licence number' })
  @ApiResponse({
    status: 200,
    description: 'Licence retrieved successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence by number',
  })
  async findByLicenseNumber(@Param('licenseNumber') licenseNumber: string): Promise<Licenses> {
    return this.licensesService.findByLicenseNumber(licenseNumber);
  }

  @Get(':id/pdf')
  @ApiOperation({ summary: 'Generate and download licence PDF' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'PDF generated and downloaded successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Generated licence PDF',
  })
  async generatePDF(
    @Param('id', ParseUUIDPipe) id: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const license = await this.licensesService.findOne(id);
      const pdfBuffer = await this.licensesService.generateLicensePDF(id);

      const filename = `${license.license_number}_license.pdf`;

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
      });

      res.end(pdfBuffer);
    } catch (error) {
      this.logger.error('Error generating PDF:', error);

      // If it's a template parse error, try clearing cache and retrying once
      if (error.message.includes('Parse error') && !error.retried) {
        this.logger.warn('Template parse error detected, clearing cache and retrying...');
        this.licensesService.clearTemplateCache();

        try {
          const pdfBuffer = await this.licensesService.generateLicensePDF(id);
          const license = await this.licensesService.findOne(id);
          const filename = `${license.license_number}_license.pdf`;

          res.set({
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': pdfBuffer.length.toString(),
          });

          res.end(pdfBuffer);
          return;
        } catch (retryError) {
          retryError.retried = true;
          this.logger.error('Retry after cache clear also failed:', retryError);
        }
      }

      // Check if response has already been sent
      if (!res.headersSent) {
        res.status(500).json({
          statusCode: 500,
          message: 'Failed to generate PDF',
          error: 'Internal Server Error'
        });
      }
    }
  }

  @Post('clear-template-cache')
  @ApiOperation({ summary: 'Clear PDF template cache (development only)' })
  @ApiResponse({
    status: 200,
    description: 'Template cache cleared successfully',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'System',
    description: 'Cleared PDF template cache',
  })
  async clearTemplateCache(): Promise<{ message: string }> {
    this.licensesService.clearTemplateCache();
    return { message: 'Template cache cleared successfully' };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get licence by ID' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence retrieved successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Viewed licence details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Licenses> {
    return this.licensesService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update licence' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence updated successfully',
    type: Licenses,
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Licence with this number already exists',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Updated licence',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLicenseDto: UpdateLicenseDto,
    @Request() req: any,
  ): Promise<Licenses> {
    return this.licensesService.update(id, updateLicenseDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete licence' })
  @ApiParam({ name: 'id', description: 'Licence UUID' })
  @ApiResponse({
    status: 200,
    description: 'Licence deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Licence not found',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'License',
    description: 'Deleted licence',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.licensesService.remove(id);
    return { message: 'Licence deleted successfully' };
  }
}
