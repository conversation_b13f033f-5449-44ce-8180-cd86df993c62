import { PartialType } from '@nestjs/swagger';
import { CreateCeirTestReportDto } from './create-ceir-test-report.dto';
import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCeirTestReportDto extends PartialType(CreateCeirTestReportDto) {
  @ApiProperty({
    description: 'User ID who is updating this test report',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID' })
  updated_by?: string;
}
