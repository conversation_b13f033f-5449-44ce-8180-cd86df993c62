import { IsString, IsEnum, IsOptional, IsBoolean, IsUUID, IsObject, MaxLength, MinLength } from 'class-validator';
import { ActivityNoteType, ActivityNoteStatus } from '../entities/activity-notes.entity';

export class CreateActivityNoteDto {
  @IsString()
  @MaxLength(100)
  entity_type: string;

  @IsUUID()
  entity_id: string;

  @IsString()
  @MinLength(1)
  @MaxLength(10000)
  note: string;

  @IsOptional()
  note_type?: string;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  category?: string;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  step?: string;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @IsString()
  @MaxLength(20)
  @IsOptional()
  priority?: string;

  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;

  @IsBoolean()
  @IsOptional()
  is_internal?: boolean;
}

export class UpdateActivityNoteDto {
  @IsString()
  @MinLength(1)
  @MaxLength(10000)
  @IsOptional()
  note?: string;

  @IsOptional()
  note_type?: string;

  @IsOptional()
  status?: string;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  category?: string;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  step?: string;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @IsString()
  @MaxLength(20)
  @IsOptional()
  priority?: string;

  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;

  @IsBoolean()
  @IsOptional()
  is_internal?: boolean;
}

export class ActivityNoteQueryDto {
  @IsString()
  @MaxLength(100)
  @IsOptional()
  entity_type?: string;

  @IsUUID()
  @IsOptional()
  entity_id?: string;

  @IsEnum(ActivityNoteType)
  @IsOptional()
  note_type?: ActivityNoteType;

  @IsEnum(ActivityNoteStatus)
  @IsOptional()
  status?: ActivityNoteStatus;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  category?: string;

  @IsString()
  @MaxLength(100)
  @IsOptional()
  step?: string;

  @IsString()
  @MaxLength(20)
  @IsOptional()
  priority?: string;

  @IsBoolean()
  @IsOptional()
  is_internal?: boolean;

  @IsUUID()
  @IsOptional()
  created_by?: string;
}
