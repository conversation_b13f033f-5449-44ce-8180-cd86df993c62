/**
 * Formats a number as currency with commas for better readability
 * For numbers over 5 digits, adds a comma after the first 2 figures
 * 
 * @param amount - The amount to format
 * @param currency - The currency code (e.g., 'MWK', 'USD')
 * @param minimumFractionDigits - Minimum number of decimal places (default: 0)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string,
  currency: string = 'USD',
  minimumFractionDigits: number = 0
): string => {
  // Convert string to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if(currency == '$') currency = 'USD';
  
  // Get the currency symbol
  const formatter = new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: minimumFractionDigits,
    useGrouping: false, // We'll handle grouping manually
  });
  
  // Format without grouping to get the base string
  const formatted = formatter.format(numericAmount);
  
  // Extract the numeric part (remove currency symbol and any spaces)
  const parts = formatted.match(/([^\d]*)(\d+(?:\.\d+)?)(.*)/);
  if (!parts) return formatted;
  
  const [, prefix, numericPart, suffix] = parts;
  
  // Format the number with custom grouping
  let formattedNumber = numericPart;
  
  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures
  if (numericPart.replace(/\D/g, '').length >= 5) {
    // Split the integer and decimal parts
    const [integerPart, decimalPart] = numericPart.split('.');
    
    // Format the integer part with commas
    // First, add a comma after the first 2 digits
    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);
    
    // Then add commas for the rest of the number every 3 digits
    formattedInteger = formattedInteger.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    // Combine the parts back
    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');
  } else {
    // For smaller numbers, use standard grouping (every 3 digits)
    formattedNumber = numericPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  // Combine everything back
  return prefix + formattedNumber + suffix;
};

// ============================================================================
// ENHANCED AMOUNT FORMATTERS
// ============================================================================

/**
 * Format amount with currency (alternative to formatCurrency for consistency)
 * @param amount - The amount to format
 * @param currency - Currency code (default: '$')
 * @param locale - Locale for formatting (default: 'en-US')
 */
export const formatAmount = (
  amount: number | string,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return `$ 0.00`;

  return `${currency} ${numAmount.toLocaleString(locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`;
};

/**
 * Format amount without currency symbol
 * @param amount - The amount to format
 * @param decimals - Number of decimal places (default: 2)
 */
export const formatNumber = (amount: number | string, decimals: number = 2): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return '0.00';

  return numAmount.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

/**
 * Format license category fee with special handling for "Short Code Allocation"
 * @param fee - The fee amount (string or number)
 * @param categoryName - The name of the license category
 * @param currency - Currency code (default: 'MWK')
 */
export const formatLicenseCategoryFee = (
  fee: string | number,
  categoryName: string,
): string => {
  // Check if fee is 0 or empty
  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {
    // Show "Free" for categories with 0 fee
    return "Free";
  }
  // Format as currency for non-zero fees
  return formatCurrency(fee);
};

/**
 * Format percentage
 * @param value - The value to format as percentage
 * @param decimals - Number of decimal places (default: 1)
 */
export const formatPercentage = (value: number | string, decimals: number = 1): string => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '0%';

  return `${numValue.toFixed(decimals)}%`;
};

// ============================================================================
// ENHANCED DATE & TIME FORMATTERS
// ============================================================================

/**
 * Formats a date string to a readable format
 *
 * @param dateString - The date string to format
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string => {
  if (!dateString) return 'Not specified';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid date';

  return new Intl.DateTimeFormat('en-MW', options).format(date);
};

/**
 * Format date in long format (e.g., "January 15, 2024")
 * @param dateString - Date string or Date object
 */
export const formatDateLong = (dateString: string | Date): string => {
  if (!dateString) return 'Not specified';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid date';

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Format time (e.g., "2:30 PM")
 * @param dateString - Date string or Date object
 */
export const formatTime = (dateString: string | Date): string => {
  if (!dateString) return 'Not specified';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid time';

  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Format datetime (e.g., "Jan 15, 2024 at 2:30 PM")
 * @param dateString - Date string or Date object
 */
export const formatDateTime = (dateString: string | Date): string => {
  if (!dateString) return 'Not specified';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid datetime';

  return `${formatDate(date)} at ${formatTime(date)}`;
};

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 * @param dateString - Date string or Date object
 */
export const formatRelativeTime = (dateString: string | Date): string => {
  if (!dateString) return 'Not specified';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return 'Invalid date';

  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;

  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
};

/**
 * Format date for input fields (YYYY-MM-DD)
 * @param dateString - Date string or Date object
 */
export const formatDateForInput = (dateString: string | Date): string => {
  if (!dateString) return '';

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  if (isNaN(date.getTime())) return '';

  return date.toISOString().split('T')[0];
};

// ============================================================================
// STRING CASE FORMATTERS
// ============================================================================

/**
 * Convert string to camelCase
 * @param str - String to convert
 */
export const toCamelCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '');
};

/**
 * Convert string to PascalCase
 * @param str - String to convert
 */
export const toPascalCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
      return word.toUpperCase();
    })
    .replace(/\s+/g, '');
};

/**
 * Convert string to kebab-case
 * @param str - String to convert
 */
export const toKebabCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
};

/**
 * Convert string to snake_case
 * @param str - String to convert
 */
export const toSnakeCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase();
};

/**
 * Convert string to Title Case
 * @param str - String to convert
 */
export const toTitleCase = (str: string): string => {
  return str.replace(/\w\S*/g, (txt) => {
    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
  });
};

/**
 * Convert string to Sentence case
 * @param str - String to convert
 */
export const toSentenceCase = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// ============================================================================
// TEXT FORMATTERS
// ============================================================================

/**
 * Truncate text with ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length before truncation
 * @param suffix - Suffix to add (default: '...')
 */
export const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {
  if (!text || text.length <= maxLength) return text || '';
  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Capitalize first letter of each word
 * @param str - String to capitalize
 */
export const capitalizeWords = (str: string): string => {
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

/**
 * Remove extra whitespace and normalize spacing
 * @param str - String to normalize
 */
export const normalizeWhitespace = (str: string): string => {
  return str.replace(/\s+/g, ' ').trim();
};

/**
 * Extract initials from a name
 * @param name - Full name
 * @param maxInitials - Maximum number of initials (default: 2)
 */
export const getInitials = (name: string, maxInitials: number = 2): string => {
  if (!name) return '';

  return name
    .split(' ')
    .filter(word => word.length > 0)
    .slice(0, maxInitials)
    .map(word => word.charAt(0).toUpperCase())
    .join('');
};

/**
 * Convert text to slug format (URL-friendly)
 * @param text - Text to convert
 */
export const toSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Highlight search terms in text
 * @param text - Text to highlight
 * @param searchTerm - Term to highlight
 * @param className - CSS class for highlighting (default: 'highlight')
 */
export const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {
  if (!searchTerm) return text;

  const regex = new RegExp(`(${searchTerm})`, 'gi');
  return text.replace(regex, `<span class="${className}">$1</span>`);
};

// ============================================================================
// PHONE & EMAIL FORMATTERS
// ============================================================================

/**
 * Format phone number
 * @param phone - Phone number to format
 * @param format - Format type ('international' | 'national' | 'minimal')
 */
export const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {
  if (!phone) return '';

  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  if (digits.length < 10) return phone; // Return original if too short

  switch (format) {
    case 'international':
      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;
    case 'national':
      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;
    case 'minimal':
      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;
    default:
      return phone;
  }
};

/**
 * Mask email for privacy (e.g., "j***@example.com")
 * @param email - Email to mask
 */
export const maskEmail = (email: string): string => {
  if (!email || !email.includes('@')) return email;

  const [username, domain] = email.split('@');
  if (username.length <= 2) return email;

  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
  return `${maskedUsername}@${domain}`;
};

/**
 * Validate email format
 * @param email - Email to validate
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// ============================================================================
// ID & REFERENCE FORMATTERS
// ============================================================================

/**
 * Format application number with prefix
 * @param number - Application number
 * @param prefix - Prefix to add (default: 'APP')
 */
export const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {
  if (!number) return '';
  return `${prefix}-${String(number).padStart(6, '0')}`;
};

/**
 * Format invoice number with prefix
 * @param number - Invoice number
 * @param prefix - Prefix to add (default: 'INV')
 */
export const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {
  if (!number) return '';
  return `${prefix}-${String(number).padStart(6, '0')}`;
};

/**
 * Format task number with prefix
 * @param number - Task number
 * @param prefix - Prefix to add (default: 'TASK')
 */
export const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {
  if (!number) return '';
  return `${prefix}-${String(number).padStart(6, '0')}`;
};

/**
 * Generate a random reference ID
 * @param length - Length of the ID (default: 8)
 * @param prefix - Optional prefix
 */
export const generateReferenceId = (length: number = 8, prefix?: string): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return prefix ? `${prefix}-${result}` : result;
};

/**
 * Convert UUID to user-friendly reference ID for customers
 * @param uuid - The UUID to convert
 * @param prefix - Optional prefix (default: 'REF')
 */
export const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {
  if (!uuid) return '';

  // Take first 8 characters of UUID (without hyphens) and convert to uppercase
  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();
  const shortId = cleanUuid.substring(0, 8);

  return `${prefix}-${shortId}`;
};

/**
 * Mask sensitive ID (show only first and last 2 characters)
 * @param id - ID to mask
 */
export const maskId = (id: string): string => {
  if (!id || id.length <= 4) return id;

  const start = id.slice(0, 2);
  const end = id.slice(-2);
  const middle = '*'.repeat(id.length - 4);

  return `${start}${middle}${end}`;
};

// ============================================================================
// STATUS & BADGE FORMATTERS
// ============================================================================

/**
 * Format status text for display
 * @param status - Status to format
 */
export const formatStatus = (status: string): string => {
  if (!status) return '';

  return status
    .replace(/_/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
};

/**
 * Get status color class
 * @param status - Status to get color for
 */


export const getStatusColor = (status: any): string => {
  const statusLower = status.toLowerCase();

  switch (statusLower) {
    case 'active':
    case 'approved':
    case 'completed':
    case 'paid':
    case 'success':
      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';

    case 'pending':
    case 'in_progress':
    case 'processing':
    case 'review':
      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';

    case 'rejected':
    case 'failed':
    case 'error':
    case 'overdue':
    case 'cancelled':
      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';

    case 'draft':
    case 'inactive':
    case 'disabled':
      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';

    case 'warning':
    case 'attention':
      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';

    default:
      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
  }
};

// ============================================================================
// FILE SIZE & VALIDATION FORMATTERS
// ============================================================================

/**
 * Format file size in human readable format
 * @param bytes - File size in bytes
 * @param decimals - Number of decimal places (default: 2)
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param filename - Filename to extract extension from
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Get file type icon class based on extension
 * @param filename - Filename to get icon for
 */
export const getFileTypeIcon = (filename: string): string => {
  const extension = getFileExtension(filename).toLowerCase();

  switch (extension) {
    case 'pdf':
      return 'ri-file-pdf-line text-red-500';
    case 'doc':
    case 'docx':
      return 'ri-file-word-line text-blue-500';
    case 'xls':
    case 'xlsx':
      return 'ri-file-excel-line text-green-500';
    case 'ppt':
    case 'pptx':
      return 'ri-file-ppt-line text-orange-500';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return 'ri-image-line text-purple-500';
    case 'zip':
    case 'rar':
    case '7z':
      return 'ri-file-zip-line text-yellow-500';
    case 'txt':
      return 'ri-file-text-line text-gray-500';
    default:
      return 'ri-file-line text-gray-500';
  }
};

export const formatHumanReadable = (text : string, caseType = 'first') => {
  if (!text || typeof text !== 'string') return '';
  
  // Clean and normalize the text
  let formatted = text
    .trim()
    .replace(/[-_]+/g, ' ') // Replace hyphens and underscores with spaces
    .replace(/\s+/g, ' ')  // Collapse multiple spaces
    .toLowerCase();
  
  // Split into words
  const words = formatted.split(' ');
  
  // Format based on caseType
  switch (caseType.toLowerCase()) {
    case 'lower':
      return formatted;
      
    case 'upper':
      return words
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
        
    case 'first':
    default:
      return words
        .map((word, index) => 
          index === 0 
            ? word.charAt(0).toUpperCase() + word.slice(1)
            : word
        )
        .join(' ');
  }
}