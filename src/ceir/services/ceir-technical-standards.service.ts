import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirTechnicalStandards } from '../entities/ceir-technical-standards.entity';
import { CreateCeirTechnicalStandardDto, UpdateCeirTechnicalStandardDto } from '../dto/ceir-technical-standards';

@Injectable()
export class CeirTechnicalStandardsService {
  constructor(
    @InjectRepository(CeirTechnicalStandards)
    private readonly standardRepository: Repository<CeirTechnicalStandards>,
  ) {}

  async create(createDto: CreateCeirTechnicalStandardDto, userId?: string): Promise<CeirTechnicalStandards> {
    // Check if standard with same reference already exists
    const existingStandard = await this.standardRepository.findOne({
      where: { standard_reference: createDto.standard_reference }
    });

    if (existingStandard) {
      throw new ConflictException(`Technical standard with reference '${createDto.standard_reference}' already exists`);
    }

    const standard = this.standardRepository.create({
      ...createDto,
      created_by: createDto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      publication_date: new Date(createDto.publication_date),
      effective_date: createDto.effective_date ? new Date(createDto.effective_date) : undefined,
      expiry_date: createDto.expiry_date ? new Date(createDto.expiry_date) : undefined,
    });

    return await this.standardRepository.save(standard);
  }

  async findAll(): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' }
    });
  }

  async findAllActive(): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      where: { is_active: true, status: 'active' },
      relations: ['creator', 'updater'],
      order: { standard_reference: 'ASC' }
    });
  }

  async findOne(id: string): Promise<CeirTechnicalStandards> {
    const standard = await this.standardRepository.findOne({
      where: { standard_id: id },
      relations: ['creator', 'updater']
    });

    if (!standard) {
      throw new NotFoundException(`Technical standard with ID '${id}' not found`);
    }

    return standard;
  }

  async findByReference(reference: string): Promise<CeirTechnicalStandards> {
    const standard = await this.standardRepository.findOne({
      where: { standard_reference: reference },
      relations: ['creator', 'updater']
    });

    if (!standard) {
      throw new NotFoundException(`Technical standard with reference '${reference}' not found`);
    }

    return standard;
  }

  async update(id: string, updateDto: UpdateCeirTechnicalStandardDto, userId?: string): Promise<CeirTechnicalStandards> {
    const standard = await this.findOne(id);

    // Check for conflicts if updating standard_reference
    if (updateDto.standard_reference && updateDto.standard_reference !== standard.standard_reference) {
      const existingStandard = await this.standardRepository.findOne({
        where: { standard_reference: updateDto.standard_reference }
      });

      if (existingStandard && existingStandard.standard_id !== id) {
        throw new ConflictException(`Technical standard with reference '${updateDto.standard_reference}' already exists`);
      }
    }

    const updateData = {
      ...updateDto,
      updated_by: updateDto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
      publication_date: updateDto.publication_date ? new Date(updateDto.publication_date) : standard.publication_date,
      effective_date: updateDto.effective_date ? new Date(updateDto.effective_date) : standard.effective_date,
      expiry_date: updateDto.expiry_date ? new Date(updateDto.expiry_date) : standard.expiry_date,
    };

    Object.assign(standard, updateData);
    return await this.standardRepository.save(standard);
  }

  async remove(id: string): Promise<void> {
    const standard = await this.findOne(id);
    await this.standardRepository.softDelete(id);
  }

  async restore(id: string): Promise<CeirTechnicalStandards> {
    await this.standardRepository.restore(id);
    return await this.findOne(id);
  }

  async activate(id: string, userId: string): Promise<CeirTechnicalStandards> {
    const standard = await this.findOne(id);
    standard.is_active = true;
    standard.status = 'active';
    standard.updated_by = userId;
    return await this.standardRepository.save(standard);
  }

  async deactivate(id: string, userId: string): Promise<CeirTechnicalStandards> {
    const standard = await this.findOne(id);
    standard.is_active = false;
    standard.updated_by = userId;
    return await this.standardRepository.save(standard);
  }

  async deprecate(id: string, userId: string, supersededBy?: string): Promise<CeirTechnicalStandards> {
    const standard = await this.findOne(id);
    standard.status = 'deprecated';
    standard.updated_by = userId;
    if (supersededBy) {
      standard.superseded_by = supersededBy;
    }
    return await this.standardRepository.save(standard);
  }

  async withdraw(id: string, userId: string): Promise<CeirTechnicalStandards> {
    const standard = await this.findOne(id);
    standard.status = 'withdrawn';
    standard.is_active = false;
    standard.updated_by = userId;
    return await this.standardRepository.save(standard);
  }

  async findByType(standardType: string): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      where: { standard_type: standardType, is_active: true },
      relations: ['creator', 'updater'],
      order: { standard_reference: 'ASC' }
    });
  }

  async findByOrganization(organization: string): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      where: { issuing_organization: organization, is_active: true },
      relations: ['creator', 'updater'],
      order: { standard_reference: 'ASC' }
    });
  }

  async findByStatus(status: string): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      where: { status },
      relations: ['creator', 'updater'],
      order: { standard_reference: 'ASC' }
    });
  }

  async findByEquipmentCategory(category: string): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository
      .createQueryBuilder('standard')
      .where(':category = ANY(standard.applicable_equipment_categories)', { category })
      .andWhere('standard.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('standard.creator', 'creator')
      .leftJoinAndSelect('standard.updater', 'updater')
      .orderBy('standard.standard_reference', 'ASC')
      .getMany();
  }

  async findByFrequencyBand(frequencyBand: string): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository
      .createQueryBuilder('standard')
      .where(':frequencyBand = ANY(standard.applicable_frequency_bands)', { frequencyBand })
      .andWhere('standard.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('standard.creator', 'creator')
      .leftJoinAndSelect('standard.updater', 'updater')
      .orderBy('standard.standard_reference', 'ASC')
      .getMany();
  }

  async findMandatory(): Promise<CeirTechnicalStandards[]> {
    return await this.standardRepository.find({
      where: { is_mandatory: true, is_active: true, status: 'active' },
      relations: ['creator', 'updater'],
      order: { standard_reference: 'ASC' }
    });
  }

  async findExpiring(days: number = 30): Promise<CeirTechnicalStandards[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return await this.standardRepository
      .createQueryBuilder('standard')
      .where('standard.expiry_date IS NOT NULL')
      .andWhere('standard.expiry_date <= :futureDate', { futureDate })
      .andWhere('standard.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('standard.creator', 'creator')
      .leftJoinAndSelect('standard.updater', 'updater')
      .orderBy('standard.expiry_date', 'ASC')
      .getMany();
  }

  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byType: Record<string, number>;
    byOrganization: Record<string, number>;
    byStatus: Record<string, number>;
    mandatory: number;
    expiring: number;
  }> {
    const [total, active, inactive, mandatory] = await Promise.all([
      this.standardRepository.count(),
      this.standardRepository.count({ where: { is_active: true } }),
      this.standardRepository.count({ where: { is_active: false } }),
      this.standardRepository.count({ where: { is_mandatory: true } }),
    ]);

    const expiring = await this.findExpiring(30);
    const standards = await this.standardRepository.find();

    const byType = standards.reduce((acc, standard) => {
      acc[standard.standard_type] = (acc[standard.standard_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byOrganization = standards.reduce((acc, standard) => {
      acc[standard.issuing_organization] = (acc[standard.issuing_organization] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byStatus = standards.reduce((acc, standard) => {
      acc[standard.status] = (acc[standard.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      inactive,
      byType,
      byOrganization,
      byStatus,
      mandatory,
      expiring: expiring.length,
    };
  }
}
