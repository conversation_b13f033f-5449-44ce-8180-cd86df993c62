import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { InvoicesService, CreateInvoiceDto, UpdateInvoiceDto, InvoiceFilters } from './invoices.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { Roles } from '../common/decorators/roles.decorator';
import { RolesGuard } from '../common/guards/roles.guard';
import { Audit } from '../common/interceptors/audit.interceptor';

@ApiTags('Invoices')
@Controller('invoices')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class InvoicesController {
  constructor(private readonly invoicesService: InvoicesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new invoice' })
  @ApiResponse({ status: 201, description: 'Invoice created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseGuards(RolesGuard)
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Created new invoice',
  })
  async create(@Body() createInvoiceDto: CreateInvoiceDto, @Request() req: any) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.invoicesService.create(createInvoiceDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all invoices with optional filters' })
  @ApiResponse({ status: 200, description: 'Invoices retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by invoice status' })
  @ApiQuery({ name: 'entity_type', required: false, description: 'Filter by entity type' })
  @ApiQuery({ name: 'entity_id', required: false, description: 'Filter by entity ID' })
  @ApiQuery({ name: 'client_id', required: false, description: 'Filter by client ID' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Viewed invoices list',
  })
  async findAll(@Query() filters: InvoiceFilters, @Request() req?: any) {

    // Check user roles to determine filtering behavior
    const userRoles = req?.user?.roles || [];
    // Roles from JWT are strings, not objects
    const isCustomer = userRoles.includes('customer');
    // For customers, filter by their applications only
    // For admin/staff, show all invoices and only respect provided filters
    const enhancedFilters = {
      ...filters,
      // Only add client_id filter for customers
      ...(isCustomer && { client_id: req?.user?.user_id })
    };
    const result = await this.invoicesService.findAll(enhancedFilters);
    return result;
  }

  @Get('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get invoices by entity type and ID' })
  @ApiResponse({ status: 200, description: 'Invoices retrieved successfully' })
  @ApiParam({ name: 'entityType', description: 'Entity type (e.g., application)' })
  @ApiParam({ name: 'entityId', description: 'Entity ID' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Viewed invoices by entity',
  })
  async findByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
  ) {
    return this.invoicesService.findByEntity(entityType, entityId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get invoice by ID' })
  @ApiResponse({ status: 200, description: 'Invoice retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Viewed invoice details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.invoicesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update invoice' })
  @ApiResponse({ status: 200, description: 'Invoice updated successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  @Roles('administrator')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Updated invoice',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateInvoiceDto: UpdateInvoiceDto,
    @Request() req: any,
  ) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.invoicesService.update(id, updateInvoiceDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete invoice' })
  @ApiResponse({ status: 200, description: 'Invoice deleted successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  @Roles('administrator', 'staff')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Deleted invoice',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.invoicesService.remove(id);
  }

  @Post(':id/send')
  @ApiOperation({ summary: 'Send invoice to client' })
  @ApiResponse({ status: 200, description: 'Invoice sent successfully' })
  @ApiResponse({ status: 400, description: 'Invoice cannot be sent' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  // @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Sent invoice to client',
  })
  async sendInvoice(@Param('id', ParseUUIDPipe) id: string, @Request() req: any) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.invoicesService.sendInvoice(id, userId);
  }

  @Post(':id/mark-paid')
  @ApiOperation({ summary: 'Mark invoice as paid' })
  @ApiResponse({ status: 200, description: 'Invoice marked as paid successfully' })
  @ApiResponse({ status: 400, description: 'Invoice already paid' })
  @ApiResponse({ status: 404, description: 'Invoice not found' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  // @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Marked invoice as paid',
  })
  async markAsPaid(@Param('id', ParseUUIDPipe) id: string, @Request() req: any) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.invoicesService.markAsPaid(id, userId);
  }

  @Post('generate/application/:applicationId')
  @ApiOperation({ summary: 'Generate invoice for application' })
  @ApiResponse({ status: 201, description: 'Invoice generated successfully' })
  @ApiResponse({ status: 400, description: 'Invoice already exists or bad request' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @UseGuards(RolesGuard)
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Generated invoice for application',
  })
  async generateApplicationInvoice(
    @Param('applicationId', ParseUUIDPipe) applicationId: string,
    @Body() data: { amount: number; description: string; items?: any[] },
    @Request() req: any,
  ) {
    try {
      // Handle case where authentication is disabled (req.user is undefined)
      const userId = req.user?.user_id || null;
      const result = await this.invoicesService.generateApplicationInvoice(applicationId, data, userId);
      return result;
    } catch (error) {
      throw error;
    }
  }

  @Get('application/:applicationId/status')
  @ApiOperation({ summary: 'Get invoice status for application' })
  @ApiResponse({ status: 200, description: 'Invoice status retrieved successfully' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Viewed application invoice status',
  })
  async getApplicationInvoiceStatus(@Param('applicationId', ParseUUIDPipe) applicationId: string) {
    return this.invoicesService.getApplicationInvoiceStatus(applicationId);
  }

  @Get('application/:applicationId/details')
  @ApiOperation({ summary: 'Get application details for invoice generation' })
  @ApiResponse({ status: 200, description: 'Application details retrieved successfully' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @UseGuards(RolesGuard)
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Invoice',
    description: 'Viewed application details for invoice generation',
  })
  async getApplicationDetailsForInvoice(@Param('applicationId', ParseUUIDPipe) applicationId: string) {
    return this.invoicesService.getApplicationDetailsForInvoice(applicationId);
  }
}
