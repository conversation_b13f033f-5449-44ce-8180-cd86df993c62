# ===================================================================
# MACRA Digital Portal Backend - Docker Compose Configuration
# ===================================================================

version: '1.1'
services:
  db:
    image: postgres:15
    container_name: macra-db
    restart: unless-stopped
    env_file:
      - .env.production
    ports:
      - "5433:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - macra-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: macra-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    env_file:
      - .env.production
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - macra-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  pgdata:

networks:
  macra-network:
    driver: bridge