import { Injectable, NotFoundException, ConflictException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, In } from 'typeorm';
import { Applications, ApplicationStatus } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { ApplicationTaskHelperService } from './application-task-helper.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { TasksService } from '../tasks/tasks.service';
import {
  UpdateApplicationStatusDto,
  ApplicationStatusTrackingResponseDto,
  ApplicationStatusHistoryResponseDto
} from '../dto/application-status/update-application-status.dto';
import { LicensesService } from 'src/licenses/licenses.service';
import { ActivityNotesService } from '../services/activity-notes.service';
import { isNumber } from 'class-validator';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    @InjectRepository(ApplicationStatusHistory)
    private statusHistoryRepository: Repository<ApplicationStatusHistory>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private applicationTaskHelper: ApplicationTaskHelperService,
    private notificationHelper: NotificationHelperService,
    @Inject(forwardRef(() => TasksService))
    private tasksService: TasksService,
    @Inject(forwardRef(() => LicensesService))
    private licensesService: LicensesService,
    @Inject(forwardRef(() => ActivityNotesService))
    private activityNotesService: ActivityNotesService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Applications> = {
    sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
    searchableColumns: ['application_number', 'status'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater', 'assignee'],
    filterableColumns: {
      status: true,
      created_by: true,
      license_category_id: true,
      applicant_id: true,
      assigned_to: true,
      'license_category.license_type_id': true,
    },
  };

  async create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications> {
    // Generate application number if not provided
    let applicationNumber = createApplicationDto.application_number;
    applicationNumber = await this.generateApplicationNumber();
    if (!applicationNumber) {
      applicationNumber = await this.generateApplicationNumber();
    } else {
      // Check if provided application number already exists
      const existingApplication = await this.applicationsRepository.findOne({
        where: { application_number: applicationNumber,
          deleted_at: IsNull()
         },
      });

      if (existingApplication) {
        throw new ConflictException('An application with this number already exists');
      }
    }

    const existingCategoryApplication = await this.applicationsRepository.findOne({
      where: { license_category_id: createApplicationDto.license_category_id,
        applicant_id: createApplicationDto.applicant_id,
        deleted_at: IsNull()
       },
    });

    if (existingCategoryApplication) {
      throw new ConflictException('You can only submit one application per licence category');
    }

    const application = this.applicationsRepository.create({
      ...createApplicationDto,
      application_number: applicationNumber,
      current_step: createApplicationDto.current_step || 1,
      progress_percentage: createApplicationDto.progress_percentage || 0,
      status: createApplicationDto.status || 'draft',
      created_by: createdBy,
    });

    return this.applicationsRepository.save(application);
  }

  /**
   * Generate unique application number in format APP-YYYY-NNNNNN
   * where YYYY is the current year and NNNNNN is a 6-digit incremental number
   */
  private async generateApplicationNumber(): Promise<string> {
    const currentYear = new Date().getFullYear();
    const prefix = `APP-${currentYear}`;

    try {
      // Find the latest application number for the current year
      const latestApplication = await this.applicationsRepository
        .createQueryBuilder('application')
        .where('application.application_number LIKE :pattern', {
          pattern: `${prefix}-%`
        })
        .andWhere('application.deleted_at IS NULL')
        .orderBy('application.application_number', 'DESC')
        .getOne();

      let nextSequence = 1;

      if (latestApplication) {
        const parts = latestApplication.application_number.split('-');
        if (parts.length === 3) {
          const lastSequence = parseInt(parts[2], 10);
          if (!isNaN(lastSequence)) {
            nextSequence = lastSequence + 1;
          }
        }
      }
      // Generate application number with retry logic to handle race conditions
      let attempts = 0;
      const maxAttempts = 100;

      while (attempts < maxAttempts) {
        const sequence = String(nextSequence).padStart(6, '0');
        const applicationNumber = `${prefix}-${sequence}`;
        // Check if this application number already exists
        const existingApplication = await this.applicationsRepository.findOne({
          where: {
            application_number: applicationNumber,
            deleted_at: IsNull()
          }
        });

        if (!existingApplication) {
          return applicationNumber;
        }

        nextSequence++;
        attempts++;
      }

      throw new Error(`Could not generate unique application number after ${maxAttempts} attempts`);
    } catch (error) {
      throw new Error('Failed to generate application number');
    }
  }

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their created applications only
    if (isCustomer && userId) {
      const customerQuery: PaginateQuery = {
        ...query,
        filter: {
          ...query.filter,
          created_by: userId
        }
      };
      return paginate(customerQuery, this.applicationsRepository, this.paginateConfig);
    }

    // For department users, exclude draft applications unless explicitly requested
    const includeDraftValue = query.filter?.['include_draft'];
    const shouldIncludeDrafts = includeDraftValue === 'true' ||
                               (typeof includeDraftValue === 'boolean' && includeDraftValue === true);

    if (shouldIncludeDrafts) {
      // Include all applications including drafts
      return paginate(query, this.applicationsRepository, this.paginateConfig);
    } else {
      // Exclude draft applications by using a custom query builder
      const queryBuilder = this.applicationsRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.applicant', 'applicant')
        .leftJoinAndSelect('application.license_category', 'license_category')
        .leftJoinAndSelect('license_category.license_type', 'license_type')
        .leftJoinAndSelect('application.creator', 'creator')
        .leftJoinAndSelect('application.updater', 'updater')
        .leftJoinAndSelect('application.assignee', 'assignee')
        .where('application.status != :draftStatus', { draftStatus: 'draft' });

      // Apply additional filters from the query
      if (query.filter) {
        Object.entries(query.filter).forEach(([key, value]) => {
          if (key !== 'include_draft' && value !== undefined && value !== '') {
            if (key.includes('.')) {
              // Handle nested filters like 'license_category.license_type_id'
              queryBuilder.andWhere(`${key} = :${key.replace('.', '_')}`, { [key.replace('.', '_')]: value });
            } else {
              queryBuilder.andWhere(`application.${key} = :${key}`, { [key]: value });
            }
          }
        });
      }

      // Apply search if provided
      if (query.search) {
        queryBuilder.andWhere(
          '(application.application_number LIKE :search OR application.status LIKE :search)',
          { search: `%${query.search}%` }
        );
      }

      return paginate(query, queryBuilder, this.paginateConfig);
    }
  }

  async findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    // This method specifically filters applications by the authenticated user
    // It's used for customer-facing endpoints like /user-applications
    return paginate(query, this.applicationsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<Applications> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: id },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async findByApplicant(applicantId: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { applicant_id: applicantId },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async findByStatus(status: string): Promise<Applications[]> {
    return this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    // If updating application number, check for conflicts
    if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
      const existingApplication = await this.applicationsRepository.findOne({
        where: { application_number: updateApplicationDto.application_number },
      });

      if (existingApplication) {
        throw new ConflictException('Application number already exists');
      }
    }

    Object.assign(application, updateApplicationDto, { updated_by: updatedBy });

    // Set submitted_at if status is being changed to submitted for the first time
    if (updateApplicationDto.status === ApplicationStatus.SUBMITTED && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    // Check if status has changed (either through direct status update or other means)
    const currentStatus = savedApplication.status;
    if (currentStatus !== previousStatus) {
      await this.applicationTaskHelper.handleApplicationSubmission(
        application.application_id,
        previousStatus,
        currentStatus,
        updatedBy
      );
    }

    return savedApplication;
  }

  async remove(id: string): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationsRepository.softDelete(application.application_id);
  }

  async updateStatus(id: string, status: string, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    const previousStatus = application.status;

    application.status = status;
    application.updated_by = updatedBy;

    if (status === ApplicationStatus.SUBMITTED && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    const savedApplication = await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    await this.applicationTaskHelper.handleApplicationSubmission(
      application.application_id,
      previousStatus,
      status,
      updatedBy
    );

    return savedApplication;
  }

  async updateApplicationStatus(
    applicationId: string,
    updateStatusDto: UpdateApplicationStatusDto,
    userId: string
  ): Promise<ApplicationStatusTrackingResponseDto> {
    // Find the application
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations: ['applicant', 'license_category']
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }
    // Validate status transition
    this.validateStatusTransition(application.status, updateStatusDto.status);

    const isFree = application.license_category&& isNumber(application.license_category.fee) && Number(application.license_category.fee) > 0 ? false: true; 
    if(isFree && updateStatusDto.status == ApplicationStatus.PASS_EVALUATION){
      updateStatusDto.status = ApplicationStatus.WAITING_FOR_APPROVAL
    }

    // Store previous status
    const previousStatus = application.status;

    // Update application status and progress
    const { newStep, newProgress } = this.calculateStepAndProgress(updateStatusDto.status);
    application.status = updateStatusDto.status;
    application.current_step = newStep;
    application.progress_percentage = newProgress;
    application.updated_by = userId;
    // Set submitted_at if status is being changed to submitted for the first time
    if (updateStatusDto.status === ApplicationStatus.SUBMITTED && !application.submitted_at) {
      application.submitted_at = new Date();
    }

    // Save updated application
    await this.applicationsRepository.save(application);

    // Handle task creation for submitted applications using centralized helper
    await this.applicationTaskHelper.handleApplicationSubmission(
      applicationId,
      previousStatus,
      updateStatusDto.status,
      userId
    );

    // Close all tasks when application reaches final approval states
    try {
      await this.tasksService.closeAllTasksForApplication(
        applicationId, userId,
        `Application status changed to ${updateStatusDto.status}. All tasks automatically closed.`
      );
    } catch (taskError) {
      console.error(`⚠️ Failed to close tasks for application ${applicationId}:`, taskError);
    }
    

    // License is Issued and create License Record
    if (updateStatusDto.status === 'approved') {
      try {
        // Create License Record
        const license = await this.licensesService.createLicenseFromApplication(applicationId, userId);
        // Create Activity note for license creation
        const licenseActivityNote = `License ${license.license_number} has been issued for this application. The license is valid from ${license.issue_date} to ${license.expiry_date}.`;

        await this.activityNotesService.create({
          entity_type: 'application',
          entity_id: applicationId,
          note: licenseActivityNote,
          note_type: 'license_issued' as any,
          category: 'status',
          priority: 'high',
          is_internal: false, // Visible to applicant
        }, userId);

      } catch (licenseError) {
        console.error(`❌ Failed to create license for application ${applicationId}:`, licenseError);
        // Don't fail the status update if license creation fails, but log the error
        // The application status will still be updated to approved
      }
    }

    // Create status history record
    const statusHistory = new ApplicationStatusHistory();
    statusHistory.application_id = applicationId;
    statusHistory.status = updateStatusDto.status;
    statusHistory.previous_status = previousStatus;
    statusHistory.comments = updateStatusDto.comments;
    statusHistory.reason = updateStatusDto.reason;
    statusHistory.changed_by = updateStatusDto.changed_by || userId;

    if (updateStatusDto.estimated_completion_date) {
      statusHistory.estimated_completion_date = new Date(updateStatusDto.estimated_completion_date);
    }

    await this.statusHistoryRepository.save(statusHistory);

    // Send email notification to applicant if requested
    if (updateStatusDto.send_email && application.applicant) {
      try {
        await this.notificationHelper.notifyApplicationStatus(
          applicationId,
          application.applicant.applicant_id,
          application.applicant.email,
          application.application_number,
          updateStatusDto.status,
          userId,
          application.applicant.name,
          application.license_category?.name || 'License',
          previousStatus
        );
      } catch (error) {
        console.error(`Failed to send email notification for application ${applicationId}:`, error);
        // Don't fail the status update if email fails
      }
    }

    // Return updated application with status history
    return this.getApplicationStatusTracking(applicationId);
  }

  async getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingResponseDto> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations: ['applicant', 'license_category']
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    // Get status history
    const statusHistory = await this.statusHistoryRepository.find({
      where: { application_id: applicationId },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    // Transform status history
    const transformedHistory: ApplicationStatusHistoryResponseDto[] = statusHistory.map(history => ({
      history_id: history.history_id,
      application_id: history.application_id,
      status: history.status,
      previous_status: history.previous_status,
      comments: history.comments,
      reason: history.reason,
      changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
      changed_at: history.changed_at,
      estimated_completion_date: history.estimated_completion_date
    }));

    return {
      application_id: application.application_id,
      application_number: application.application_number,
      current_status: application.status,
      current_step: application.current_step,
      progress_percentage: application.progress_percentage,
      submitted_at: application.submitted_at,
      created_at: application.created_at,
      updated_at: application.updated_at,
      status_history: transformedHistory,
      applicant: {
        name: application.applicant.name,
        email: application.applicant.email,
        business_registration_number: application.applicant.business_registration_number || ''
      },
      license_category: {
        name: application.license_category.name,
        description: application.license_category.description
      }
    };
  }

  async getApplicationsByStatus(status: string): Promise<ApplicationStatusTrackingResponseDto[]> {
    const applications = await this.applicationsRepository.find({
      where: { status },
      relations: ['applicant', 'license_category', 'license_category.license_type'],
      order: { updated_at: 'DESC' }
    });

    if (applications.length === 0) {
      return [];
    }

    // Get all status histories for these applications in one query
    const applicationIds = applications.map(app => app.application_id);
    const statusHistories = await this.statusHistoryRepository.find({
      where: { application_id: In(applicationIds) },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    // Group status histories by application_id
    const historiesByAppId = statusHistories.reduce((acc, history) => {
      if (!acc[history.application_id]) {
        acc[history.application_id] = [];
      }
      acc[history.application_id].push(history);
      return acc;
    }, {} as Record<string, any[]>);

    // Transform to response DTOs
    return applications.map(application => {
      const appHistories = historiesByAppId[application.application_id] || [];
      const transformedHistory = appHistories.map(history => ({
        history_id: history.history_id,
        application_id: history.application_id,
        status: history.status,
        previous_status: history.previous_status,
        comments: history.comments,
        reason: history.reason,
        changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
        changed_at: history.changed_at,
        estimated_completion_date: history.estimated_completion_date
      }));

      return {
        application_id: application.application_id,
        application_number: application.application_number,
        current_status: application.status,
        current_step: application.current_step,
        progress_percentage: application.progress_percentage,
        submitted_at: application.submitted_at,
        created_at: application.created_at,
        updated_at: application.updated_at,
        status_history: transformedHistory,
        applicant: {
          name: application.applicant.name,
          email: application.applicant.email,
          business_registration_number: application.applicant.business_registration_number || ''
        },
        license_category: {
          name: application.license_category.name,
          description: application.license_category.description
        }
      };
    });
  }

  async getStatusHistory(applicationId: string): Promise<ApplicationStatusHistoryResponseDto[]> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId }
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    const statusHistory = await this.statusHistoryRepository.find({
      where: { application_id: applicationId },
      relations: ['user'],
      order: { changed_at: 'DESC' }
    });

    return statusHistory.map(history => ({
      history_id: history.history_id,
      application_id: history.application_id,
      status: history.status,
      previous_status: history.previous_status,
      comments: history.comments,
      reason: history.reason,
      changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
      changed_at: history.changed_at,
      estimated_completion_date: history.estimated_completion_date
    }));
  }

  private validateStatusTransition(currentStatus: string, newStatus: string): void {
    const validTransitions: Record<string, string[]> = {
      'draft': ['submitted'],
      'submitted': ['under_review', 'evaluation', 'rejected'],
      'under_review': ['evaluation', 'rejected'],
      'evaluation': ['pass_evaluation', 'approved', 'rejected'],
      'pass_evaluation': ['approved', 'rejected', 'evaluation', 'pass_evaluation'], // Can proceed to final approval or be rejected
      'pending_payment': ['waiting_for_approval', 'draft', 'rejected'], // Can proceed to final approval or be rejected
      'waiting_for_approval': ['approved', 'rejected', 'waiting_for_approval'], // Can proceed to final approval or be rejected
      'approved': ['approved'], // Final status
      'rejected': ['rejected'], // Final status
      'withdrawn': ['withdrawn'] // Final status
    };

    if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}`
      );
    }
  }

  private calculateStepAndProgress(status: string): { newStep: number; newProgress: number } {
    const statusStepMap: Record<string, { step: number; progress: number }> = {
      'draft': { step: 1, progress: 14 },
      'submitted': { step: 2, progress: 25 },
      'under_review': { step: 3, progress: 50 },
      'evaluation': { step: 4, progress: 75 },
      'pass_evaluation': { step: 5, progress: 80 }, // Evaluation passed, awaiting final approval
      'waiting_for_approval': { step: 5, progress: 90 }, // Evaluation passed, awaiting final approval
      'approved': { step: 6, progress: 100 },
      'rejected': { step: 1, progress: 0 },
      'withdrawn': { step: 1, progress: 0 }
    };

    const mapping = statusStepMap[status];
    if (!mapping) {
      throw new BadRequestException(`Unknown status: ${status}`);
    }

    return {
      newStep: mapping.step,
      newProgress: mapping.progress
    };
  }

  async updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications> {
    const application = await this.findOne(id);
    application.current_step = currentStep;
    application.progress_percentage = progressPercentage;
    application.updated_by = updatedBy;

    return this.applicationsRepository.save(application);
  }

  async getApplicationStats(): Promise<any> {
    const stats = await this.applicationsRepository
      .createQueryBuilder('application')
      .select('application.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('application.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }

  async assignApplication(applicationId: string, assignedTo: string, assignedBy: string): Promise<Applications> {
    const application = await this.findOne(applicationId);
    application.assigned_to = assignedTo;
    application.assigned_at = new Date();
    application.updated_by = assignedBy;

    return this.applicationsRepository.save(application);
  }

  async getUnassignedApplications(query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  async getAssignedApplications(userId: string, query: PaginateQuery): Promise<Paginated<Applications>> {
    const config: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.applicationsRepository, config);
  }

  // Debug method to get all applications without filtering
  async findAllDebug(query: PaginateQuery): Promise<Paginated<Applications>> {
    const count = await this.applicationsRepository.count();
    // Get status distribution
    const statusStats = await this.applicationsRepository
      .createQueryBuilder('app')
      .select('app.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('app.status')
      .getRawMany();
        
    const debugConfig: PaginateConfig<Applications> = {
      ...this.paginateConfig,
      // Remove any status filtering for debug
    };
    
    return paginate(query, this.applicationsRepository, debugConfig);
  }
}
