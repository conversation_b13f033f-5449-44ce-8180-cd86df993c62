import { PartialType } from '@nestjs/swagger';
import { CreateCeirTechnicalStandardDto } from './create-ceir-technical-standard.dto';
import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateCeirTechnicalStandardDto extends PartialType(CreateCeirTechnicalStandardDto) {
  @ApiProperty({
    description: 'User ID who is updating this standard',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID' })
  updated_by?: string;
}
