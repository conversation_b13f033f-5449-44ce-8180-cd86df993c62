/**
 * Shortcode categories
 */
export enum ShortcodeCategory {
  RESERVED = 'reserved',
  LIFELINE_SERVICES = 'lifeline_services',
  DATA_VOICE_USSD = 'data_voice_ussd',
  CUSTOMER_CARE_ASSISTANCE = 'customer_care',
  LIFE_AND_SAFETY = 'life_and_safety',
  FUTURE_USE = 'future_use',
}

/**
 * Shortcode status
 */
export enum ShortcodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Shortcode audience
 */
export enum ShortcodeAudience {
  COMMUNITY = 'community',
  NATIONAL = 'national',
  REGIONAL = 'regional',
  DISTRICT = 'district',
}
/**
 * Shortcode numbering plan
 * Shows the numbering plan for shortcodes
 */
export const shortCodeNumbering = [
  {
    length: 3,
    categories: [
      { category: ShortcodeCategory.CUSTOMER_CARE_ASSISTANCE, start: 100, end: 109 },
      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 110, end: 119 },
      { category: ShortcodeCategory.RESERVED, start: 120, end: 199 },
      { category: ShortcodeCategory.RESERVED, start: 200, end: 299 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 300, end: 499 },
      { category: ShortcodeCategory.FUTURE_USE, start: 500, end: 899 },
      { category: ShortcodeCategory.LIFE_AND_SAFETY, start: 900, end: 999 },
    ]
  },
  {
    length: 4,
    categories: [
      { category: ShortcodeCategory.RESERVED, start: 1000, end: 1099 },
      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 1100, end: 1199 },
      { category: ShortcodeCategory.RESERVED, start: 1200, end: 1999 },
      { category: ShortcodeCategory.RESERVED, start: 2000, end: 2999 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 3000, end: 3999 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 4000, end: 4999 },
      { category: ShortcodeCategory.RESERVED, start: 5000, end: 8999 }
    ]
  }
]
