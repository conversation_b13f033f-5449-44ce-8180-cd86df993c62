import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirEquipmentSpecifications } from '../entities/ceir-equipment-specifications.entity';
import { CreateCeirEquipmentSpecificationDto, UpdateCeirEquipmentSpecificationDto } from '../dto/ceir-equipment-specifications';

@Injectable()
export class CeirEquipmentSpecificationsService {
  constructor(
    @InjectRepository(CeirEquipmentSpecifications)
    private readonly specificationRepository: Repository<CeirEquipmentSpecifications>,
  ) {}

  async create(createDto: CreateCeirEquipmentSpecificationDto, userId?: string): Promise<CeirEquipmentSpecifications> {
    // Check if specification for the same device already exists
    const existingSpec = await this.specificationRepository.findOne({
      where: { device_id: createDto.device_id, is_active: true }
    });

    if (existingSpec) {
      throw new ConflictException(`Active specification for device '${createDto.device_id}' already exists`);
    }

    const specification = this.specificationRepository.create({
      ...createDto,
      created_by: createDto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      specification_date: createDto.specification_date ? new Date(createDto.specification_date) : new Date(),
    });

    return await this.specificationRepository.save(specification);
  }

  async findAll(): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository.find({
      relations: ['device', 'equipment_category', 'creator', 'updater'],
      order: { created_at: 'DESC' }
    });
  }

  async findAllActive(): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository.find({
      where: { is_active: true },
      relations: ['device', 'equipment_category', 'creator', 'updater'],
      order: { specification_date: 'DESC' }
    });
  }

  async findOne(id: string): Promise<CeirEquipmentSpecifications> {
    const specification = await this.specificationRepository.findOne({
      where: { specification_id: id },
      relations: ['device', 'equipment_category', 'creator', 'updater']
    });

    if (!specification) {
      throw new NotFoundException(`Equipment specification with ID '${id}' not found`);
    }

    return specification;
  }

  async findByDevice(deviceId: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository.find({
      where: { device_id: deviceId },
      relations: ['device', 'equipment_category', 'creator', 'updater'],
      order: { specification_date: 'DESC' }
    });
  }

  async findActiveByDevice(deviceId: string): Promise<CeirEquipmentSpecifications> {
    const specification = await this.specificationRepository.findOne({
      where: { device_id: deviceId, is_active: true },
      relations: ['device', 'equipment_category', 'creator', 'updater']
    });

    if (!specification) {
      throw new NotFoundException(`Active specification for device '${deviceId}' not found`);
    }

    return specification;
  }

  async update(id: string, updateDto: UpdateCeirEquipmentSpecificationDto, userId?: string): Promise<CeirEquipmentSpecifications> {
    const specification = await this.findOne(id);

    // If updating device_id, check for conflicts
    if (updateDto.device_id && updateDto.device_id !== specification.device_id) {
      const existingSpec = await this.specificationRepository.findOne({
        where: { device_id: updateDto.device_id, is_active: true }
      });

      if (existingSpec && existingSpec.specification_id !== id) {
        throw new ConflictException(`Active specification for device '${updateDto.device_id}' already exists`);
      }
    }

    const updateData = {
      ...updateDto,
      updated_by: updateDto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
      specification_date: updateDto.specification_date ? new Date(updateDto.specification_date) : specification.specification_date,
    };

    Object.assign(specification, updateData);
    return await this.specificationRepository.save(specification);
  }

  async remove(id: string): Promise<void> {
    const specification = await this.findOne(id);
    await this.specificationRepository.softDelete(id);
  }

  async restore(id: string): Promise<CeirEquipmentSpecifications> {
    await this.specificationRepository.restore(id);
    return await this.findOne(id);
  }

  async activate(id: string, userId: string): Promise<CeirEquipmentSpecifications> {
    const specification = await this.findOne(id);
    
    // Deactivate any other active specifications for the same device
    await this.specificationRepository.update(
      { device_id: specification.device_id, is_active: true },
      { is_active: false, updated_by: userId }
    );

    specification.is_active = true;
    specification.updated_by = userId;
    return await this.specificationRepository.save(specification);
  }

  async deactivate(id: string, userId: string): Promise<CeirEquipmentSpecifications> {
    const specification = await this.findOne(id);
    specification.is_active = false;
    specification.updated_by = userId;
    return await this.specificationRepository.save(specification);
  }

  async findByEquipmentCategory(categoryId: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository.find({
      where: { equipment_category_id: categoryId, is_active: true },
      relations: ['device', 'equipment_category', 'creator', 'updater'],
      order: { specification_date: 'DESC' }
    });
  }

  async findByNetworkTechnology(technology: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where(':technology = ANY(spec.supported_network_technologies)', { technology })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'equipment_category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByCategory(categoryId: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where('spec.equipment_category_id = :categoryId', { categoryId })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByFrequency(frequency: number): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where('spec.min_frequency <= :frequency AND spec.max_frequency >= :frequency', { frequency })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'equipment_category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByFrequencyBand(frequencyBand: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where(':frequencyBand = ANY(spec.operating_frequency_bands)', { frequencyBand })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByAntennaType(antennaType: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository.find({
      where: { antenna_type: antennaType, is_active: true },
      relations: ['device', 'equipment_category', 'creator', 'updater'],
      order: { specification_date: 'DESC' }
    });
  }

  async findBySarRange(minSar: number, maxSar: number): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where('(spec.sar_head_wkg BETWEEN :minSar AND :maxSar OR spec.sar_body_wkg BETWEEN :minSar AND :maxSar)', 
        { minSar, maxSar })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'equipment_category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByPowerRange(minPower: number, maxPower: number): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where('spec.max_transmit_power_dbm BETWEEN :minPower AND :maxPower', { minPower, maxPower })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'equipment_category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async findByConnectivityFeature(feature: string): Promise<CeirEquipmentSpecifications[]> {
    return await this.specificationRepository
      .createQueryBuilder('spec')
      .where(':feature = ANY(spec.connectivity_features)', { feature })
      .andWhere('spec.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('spec.device', 'device')
      .leftJoinAndSelect('spec.equipment_category', 'equipment_category')
      .leftJoinAndSelect('spec.creator', 'creator')
      .leftJoinAndSelect('spec.updater', 'updater')
      .orderBy('spec.specification_date', 'DESC')
      .getMany();
  }

  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byCategory: Record<string, number>;
    byAntennaType: Record<string, number>;
    averageSarHead: number;
    averageSarBody: number;
    averageTransmitPower: number;
    thisMonth: number;
  }> {
    const [total, active, inactive] = await Promise.all([
      this.specificationRepository.count(),
      this.specificationRepository.count({ where: { is_active: true } }),
      this.specificationRepository.count({ where: { is_active: false } }),
    ]);

    const thisMonthStart = new Date();
    thisMonthStart.setDate(1);
    thisMonthStart.setHours(0, 0, 0, 0);
    
    const thisMonthEnd = new Date();
    thisMonthEnd.setMonth(thisMonthEnd.getMonth() + 1);
    thisMonthEnd.setDate(0);
    thisMonthEnd.setHours(23, 59, 59, 999);
    
    const thisMonth = await this.specificationRepository.count({
      where: {
        created_at: {
          $gte: thisMonthStart,
          $lte: thisMonthEnd,
        } as any
      }
    });

    const specifications = await this.specificationRepository.find({
      relations: ['equipment_category']
    });

    const byCategory = specifications.reduce((acc, spec) => {
      const categoryName = spec.equipment_category?.category_name || 'Unknown';
      acc[categoryName] = (acc[categoryName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byAntennaType = specifications.reduce((acc, spec) => {
      if (spec.antenna_type) {
        acc[spec.antenna_type] = (acc[spec.antenna_type] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const sarHeadValues = specifications.filter(s => s.sar_head_wkg !== null && s.sar_head_wkg !== undefined).map(s => s.sar_head_wkg!);
    const sarBodyValues = specifications.filter(s => s.sar_body_wkg !== null && s.sar_body_wkg !== undefined).map(s => s.sar_body_wkg!);
    const transmitPowerValues = specifications.filter(s => s.max_transmit_power_dbm !== null && s.max_transmit_power_dbm !== undefined).map(s => s.max_transmit_power_dbm!);

    const averageSarHead = sarHeadValues.length > 0 ?
      sarHeadValues.reduce((sum, val) => sum + val, 0) / sarHeadValues.length : 0;

    const averageSarBody = sarBodyValues.length > 0 ?
      sarBodyValues.reduce((sum, val) => sum + val, 0) / sarBodyValues.length : 0;

    const averageTransmitPower = transmitPowerValues.length > 0 ?
      transmitPowerValues.reduce((sum, val) => sum + val, 0) / transmitPowerValues.length : 0;

    return {
      total,
      active,
      inactive,
      byCategory,
      byAntennaType,
      averageSarHead: Math.round(averageSarHead * 100) / 100,
      averageSarBody: Math.round(averageSarBody * 100) / 100,
      averageTransmitPower: Math.round(averageTransmitPower * 100) / 100,
      thisMonth,
    };
  }
}
