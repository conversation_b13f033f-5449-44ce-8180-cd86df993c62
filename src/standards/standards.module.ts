import { Module } from '@nestjs/common';
import { StandardsService } from './standards.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Device } from 'src/entities/device.entity';
import { Shortcode } from 'src/entities/shortcode.entity';
import { StandardsController } from './standards.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Device, Shortcode])],
  controllers: [StandardsController],
  providers: [StandardsService],
  exports: [StandardsService],
})
export class StandardsModule {}
