{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "useDefineForClassFields": false, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "noEmitOnError": false, "noEmit": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"src/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts", "**/test/**/*", "**/__tests__/**/*", "src/entities/__tests__/**/*", "src/payments/test/**/*"]}