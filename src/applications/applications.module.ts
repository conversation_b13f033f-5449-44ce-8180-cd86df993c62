import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationsController } from './applications.controller';
import { ApplicationsService } from './applications.service';
import { ApplicationTaskHelperService } from './application-task-helper.service';
import { Applications } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { TasksModule } from '../tasks/tasks.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { LicensesModule } from '../licenses/licenses.module';
import { ActivityNotesModule } from '../activity-notes/activity-notes.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Applications, ApplicationStatusHistory, User]),
    forwardRef(() => TasksModule),
    forwardRef(() => LicensesModule),
    forwardRef(() => ActivityNotesModule),
    forwardRef(() => NotificationsModule)
  ],
  controllers: [ApplicationsController],
  providers: [ApplicationsService, ApplicationTaskHelperService],
  exports: [ApplicationsService, ApplicationTaskHelperService],
})
export class ApplicationsModule {}
