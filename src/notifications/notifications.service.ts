import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notifications, NotificationType, NotificationStatus, RecipientType } from '../entities/notifications.entity';
import { CreateNotificationDto } from '../dto/notifications/create-notification.dto';
import { UpdateNotificationDto } from '../dto/notifications/update-notification.dto';
import { PaginateQuery, PaginateConfig, paginate, Paginated } from 'nestjs-paginate';
import { UsersService } from '../users/users.service';
import { ActivityNotesService } from '../services/activity-notes.service';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notifications)
    private notificationsRepository: Repository<Notifications>,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    @Inject(forwardRef(() => ActivityNotesService))
    private activityNotesService: ActivityNotesService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Notifications> = {
    sortableColumns: ['created_at', 'sent_at', 'priority', 'status', 'type'],
    nullSort: 'last',
    defaultSortBy: [['created_at', 'DESC']],
    searchableColumns: ['subject', 'message', 'recipient_email', 'recipient_phone'],
    filterableColumns: {
      type: true,
      status: true,
      priority: true,
      recipient_type: true,
      recipient_id: true,
      entity_type: true,
      entity_id: true,
      is_read: true,
      created_by: true,
    },
    defaultLimit: 20,
    maxLimit: 100,
  };

  async create(createNotificationDto: CreateNotificationDto, createdBy: string, skipActivityNote: boolean = true): Promise<Notifications> {
    // If recipient_email is provided but no recipient_id, try to find user by email
    let finalRecipientId = createNotificationDto.recipient_id;

    if (createNotificationDto.recipient_email && !createNotificationDto.recipient_id) {
      try {
        const user = await this.usersService.findByEmail(createNotificationDto.recipient_email);
        if (user) {
          finalRecipientId = user.user_id;
        } else {
        }
      } catch (error) {
      }
    }

    const notification = this.notificationsRepository.create({
      ...createNotificationDto,
      recipient_id: finalRecipientId,
      created_by: createdBy,
    });

    const savedNotification = await this.notificationsRepository.save(notification);
    if (skipActivityNote) {
      return savedNotification;
    }

    // Also save to activity notes if this is application-related
    try {
      await this.activityNotesService.create({
        entity_type: createNotificationDto.entity_type || 'notification',
        entity_id: createNotificationDto.entity_id || savedNotification.notification_id,
        note: `${createNotificationDto.subject}: ${createNotificationDto.message}`,
        note_type: 'notification',
        category: 'communication',
        metadata: {
          notification_id: savedNotification.notification_id,
          notification_type: createNotificationDto.type,
          recipient_email: createNotificationDto.recipient_email,
          recipient_phone: createNotificationDto.recipient_phone,
          is_email_message: createNotificationDto.type === NotificationType.EMAIL,
          is_sms_message: createNotificationDto.type === NotificationType.SMS,
          timestamp: new Date().toISOString(),
          ...createNotificationDto.metadata
        },
        priority: createNotificationDto.priority === 'high' ? 'high' :
                  createNotificationDto.priority === 'low' ? 'low' : 'normal',
        is_internal: false, // Notifications to customers are not internal
      }, createdBy, true); // Skip notification to prevent circular creation
    } catch (error) {  }

    return savedNotification;
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Notifications>> {
    return paginate(query, this.notificationsRepository, this.paginateConfig);
  }

  async findByRecipient(recipientId: string, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { recipient_id: recipientId },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByRecipientIdOrEmail(recipientId: string, recipientEmail: string, query: PaginateQuery): Promise<Paginated<Notifications>> {
    // Build where conditions
    let whereConditions: any[] = [];

    if (recipientEmail) {
      whereConditions.push({ recipient_email: recipientEmail });
    }else if (recipientId) {
      whereConditions.push({ recipient_id: recipientId });
    } 

    // If no conditions, return empty result by using impossible condition
    if (whereConditions.length === 0) {
      whereConditions = [{ recipient_id: 'impossible-id-that-does-not-exist' }];
    }

    // Create config with OR conditions
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: whereConditions,
    };

    const result = await paginate(query, this.notificationsRepository, config);
    return result;
  }

  async findByType(type: NotificationType, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { type },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByStatus(status: NotificationStatus, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { status },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<Paginated<Notifications>> {
    const config: PaginateConfig<Notifications> = {
      ...this.paginateConfig,
      where: { entity_type: entityType, entity_id: entityId },
    };

    return paginate(query, this.notificationsRepository, config);
  }

  async findOne(id: string): Promise<Notifications> {
    const notification = await this.notificationsRepository.findOne({
      where: { notification_id: id },
      relations: ['recipient', 'creator', 'updater'],
    });

    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }

    return notification;
  }

  async update(id: string, updateNotificationDto: UpdateNotificationDto, updatedBy: string): Promise<Notifications> {
    const notification = await this.findOne(id);

    Object.assign(notification, updateNotificationDto, { updated_by: updatedBy });
    return this.notificationsRepository.save(notification);
  }

  async markAsRead(id: string, updatedBy: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.is_read = true;
    notification.read_at = new Date();
    notification.updated_by = updatedBy;

    return this.notificationsRepository.save(notification);
  }

  async markAsSent(id: string, externalId?: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.SENT;
    notification.sent_at = new Date();
    if (externalId) {
      notification.external_id = externalId;
    }

    return this.notificationsRepository.save(notification);
  }

  async markAsDelivered(id: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.DELIVERED;
    notification.delivered_at = new Date();

    return this.notificationsRepository.save(notification);
  }

  async markAsFailed(id: string, errorMessage: string): Promise<Notifications> {
    const notification = await this.findOne(id);
    
    notification.status = NotificationStatus.FAILED;
    notification.error_message = errorMessage;
    notification.retry_count = (notification.retry_count || 0) + 1;

    return this.notificationsRepository.save(notification);
  }

  async remove(id: string): Promise<void> {
    const notification = await this.findOne(id);
    await this.notificationsRepository.softDelete(notification.notification_id);
  }

  // Helper methods for creating specific notification types
  async createEmailNotification(
    recipientId: string,
    recipientEmail: string,
    subject: string,
    message: string,
    htmlContent?: string,
    entityType?: string,
    entityId?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.EMAIL,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      recipient_email: recipientEmail,
      subject,
      message,
      html_content: htmlContent,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.create(createDto, createdBy || 'system', true);
  }

  async createSmsNotification(
    recipientId: string,
    recipientPhone: string,
    subject: string,
    message: string,
    entityType?: string,
    entityId?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.SMS,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      recipient_phone: recipientPhone,
      subject,
      message,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.create(createDto, createdBy || 'system');
  }

  async createInAppNotification(
    recipientId: string,
    subject: string,
    message: string,
    entityType?: string,
    entityId?: string,
    actionUrl?: string,
    createdBy?: string
  ): Promise<Notifications> {
    const createDto: CreateNotificationDto = {
      type: NotificationType.IN_APP,
      recipient_type: RecipientType.CUSTOMER, // Default, can be overridden
      recipient_id: recipientId,
      subject,
      message,
      entity_type: entityType,
      entity_id: entityId,
      action_url: actionUrl,
    };

    return this.create(createDto, createdBy || 'system');
  }

  // Get notification count for a specific user
  async getNotificationCount(recipientId: string): Promise<any> {
    const totalCount = await this.notificationsRepository.count({
      where: { recipient_id: recipientId }
    });

    const unreadCount = await this.notificationsRepository.count({
      where: {
        recipient_id: recipientId,
        is_read: false
      }
    });

    const readCount = await this.notificationsRepository.count({
      where: {
        recipient_id: recipientId,
        is_read: true
      }
    });

    return {
      total: totalCount,
      unread: unreadCount,
      read: readCount
    };
  }

  // Get notification count for a specific user by ID or email
  async getNotificationCountByIdOrEmail(recipientId: string, recipientEmail: string): Promise<any> {
    // Build where conditions
    let whereConditions: any[] = [];
    if (recipientEmail) {
      whereConditions.push({ recipient_email: recipientEmail });
    }else if (recipientId) {
      whereConditions.push({ recipient_id: recipientId });
    } 

    // If no conditions, return zero counts
    if (whereConditions.length === 0) {
      console.warn('⚠️ No recipient ID or email provided for notification count');
      return {
        total: 0,
        unread: 0,
        read: 0
      };
    }

    const totalCount = await this.notificationsRepository.count({
      where: whereConditions
    });

    const unreadCount = await this.notificationsRepository.count({
      where: whereConditions.map(condition => ({ ...condition, is_read: false }))
    });

    const readCount = await this.notificationsRepository.count({
      where: whereConditions.map(condition => ({ ...condition, is_read: true }))
    });

    return {
      total: totalCount,
      unread: unreadCount,
      read: readCount
    };
  }

  // Get notification statistics
  async getStats(): Promise<any> {
    const totalNotifications = await this.notificationsRepository.count();
    const sentNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.SENT }
    });
    const failedNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.FAILED }
    });
    const pendingNotifications = await this.notificationsRepository.count({
      where: { status: NotificationStatus.PENDING }
    });

    return {
      total: totalNotifications,
      sent: sentNotifications,
      failed: failedNotifications,
      pending: pendingNotifications,
      success_rate: totalNotifications > 0 ? (sentNotifications / totalNotifications) * 100 : 0
    };
  }
}
