import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PostalCode } from './entities/postal-code.entity';
import { SearchPostalCodeDTO } from './dto/postal-code/search.dto';
import { StandardResponse } from './common/interceptors/response.interceptor'; // adjust path if needed

interface PaginationParams {
  page: number;
  limit: number;
}

interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class AppService {
  constructor(
    @InjectRepository(PostalCode)
    private readonly postalCodeRepository: Repository<PostalCode>,
  ) {}

    getHello(): string {
    return 'Hello World!';
  }

  async searchPostalCodes(searchCode: SearchPostalCodeDTO): Promise<StandardResponse<PostalCode[]>> {
    const { region, district, location, postal_code } = searchCode;

    if (!region) {
      throw new BadRequestException({
        success: false,
        message: 'Region is required!',
        data: null,
      });
    }

    const query = this.postalCodeRepository.createQueryBuilder('postal');

    query.where('LOWER(postal.region) = :region', { region: region.toLowerCase() });

    if (district?.trim()) {
      query.andWhere('LOWER(postal.district) = :district', {
        district: district.toLowerCase(),
      });
    }

    if (location?.trim()) {
      query.andWhere('LOWER(postal.location) = :location', {
        location: location.toLowerCase(),
      });
    }

    if (postal_code?.trim()) {
      query.andWhere('postal.postal_code = :postal_code', { postal_code });
    }

    const results = await query
      .orderBy('postal.region', 'ASC')
      .addOrderBy('postal.district', 'ASC')
      .addOrderBy('postal.location', 'ASC')
      .getMany();

    return {
      success: true,
      message: results.length
        ? 'Postal codes retrieved successfully'
        : 'No postal codes found for the given filters',
      data: results,
      meta: { total: results.length },
      timestamp: new Date().toISOString(),
      path: '/postal-codes/search',
      statusCode: 200,
    };
  }

  async getAllPostalCodes(params: PaginationParams): Promise<StandardResponse<PostalCode[]>> {
    const { page, limit } = params;
    const offset = (page - 1) * limit;

    try {
      console.log(`Fetching postal codes - Page: ${page}, Limit: ${limit}, Offset: ${offset}`);

      // Get total count for pagination metadata
      const total = await this.postalCodeRepository.count();

      // Get paginated data with proper ordering
      const results = await this.postalCodeRepository.find({
        order: {
          region: 'ASC',
          district: 'ASC',
          location: 'ASC',
          postal_code: 'ASC'
        },
        skip: offset,
        take: limit
      });

      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMeta = {
        total,
        page,
        limit,
        totalPages,
        hasNext,
        hasPrev
      };

      console.log(`Retrieved ${results.length} postal codes out of ${total} total`);

      return {
        success: true,
        message: results.length > 0
          ? `Retrieved ${results.length} postal codes (page ${page} of ${totalPages})`
          : 'No postal codes found',
        data: results,
        meta,
        timestamp: new Date().toISOString(),
        path: '/postal-codes/all',
        statusCode: 200,
      };
    } catch (error) {
      console.error('Error in getAllPostalCodes:', error);
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve postal codes',
        data: null,
        error: error.message || 'Unknown database error'
      });
    }
  }

  async getPostalCodeByCode(postalCode: string): Promise<PostalCode> {
    const result = await this.postalCodeRepository.findOne({
      where: { postal_code: postalCode },
    });

    if (!result) {
      throw new NotFoundException({
        success: false,
        message: 'Postal code not found',
        data: null,
      });
    }

    return result
  }
}
