import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { ScopeOfService } from '../entities/scope-of-service.entity';
import { CreateScopeOfServiceDto } from '../dto/scope-of-service/create-scope-of-service.dto';
import { UpdateScopeOfServiceDto } from '../dto/scope-of-service/update-scope-of-service.dto';

@Injectable()
export class ScopeOfServiceService {
  constructor(
    @InjectRepository(ScopeOfService)
    private scopeOfServiceRepository: Repository<ScopeOfService>,
  ) {}

  async create(dto: CreateScopeOfServiceDto, createdBy: string): Promise<ScopeOfService> {
    const scopeOfService = this.scopeOfServiceRepository.create({
      ...dto,
      scope_of_service_id: uuidv4(),
      created_by: createdBy,
    });
    return await this.scopeOfServiceRepository.save(scopeOfService);
  }

  async findAll(): Promise<ScopeOfService[]> {
    return await this.scopeOfServiceRepository.find({
      where: { deleted_at: undefined },
      order: { created_at: 'DESC' }
    });
  }

  async findOne(id: string): Promise<ScopeOfService> {
    const scopeOfService = await this.scopeOfServiceRepository.findOne({
      where: { scope_of_service_id: id, deleted_at: undefined }
    });

    if (!scopeOfService) {
      throw new NotFoundException(`Scope of service with ID ${id} not found`);
    }

    return scopeOfService;
  }

  async findByApplication(applicationId: string): Promise<ScopeOfService | null> {
    // Validate applicationId
    if (!applicationId || typeof applicationId !== 'string') {
      throw new Error(`Invalid application ID: ${applicationId}`);
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(applicationId)) {
      throw new Error(`Invalid UUID format for application ID: ${applicationId}`);
    }

    try {
      return await this.scopeOfServiceRepository.findOne({
        where: { application_id: applicationId, deleted_at: undefined },
        order: { created_at: 'DESC' }
      });
    } catch (error) {
      console.error(`Error finding scope of service for application ${applicationId}:`, error);
      throw error;
    }
  }

  async update(id: string, dto: UpdateScopeOfServiceDto, updatedBy: string): Promise<ScopeOfService> {
    const scopeOfService = await this.findOne(id);
    
    Object.assign(scopeOfService, dto, { updated_by: updatedBy });
    return await this.scopeOfServiceRepository.save(scopeOfService);
  }

  async softDelete(id: string): Promise<void> {
    const scopeOfService = await this.findOne(id);
    scopeOfService.deleted_at = new Date();
    await this.scopeOfServiceRepository.save(scopeOfService);
  }

  async createOrUpdate(applicationId: string, dto: Omit<CreateScopeOfServiceDto, 'application_id'>, userId: string): Promise<ScopeOfService> {
    // Check if scope of service already exists for this application
    const existing = await this.findByApplication(applicationId);
    
    if (existing) {
      // Update existing scope of service
      return await this.update(existing.scope_of_service_id, dto, userId);
    } else {
      // Create new scope of service
      return await this.create({
        application_id: applicationId,
        ...dto
      }, userId);
    }
  }
}
