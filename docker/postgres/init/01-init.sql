--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

-- *not* creating schema, since initdb creates it


--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA public IS '';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: client_systems_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.client_systems_status_enum AS ENUM (
    'active',
    'inactive',
    'maintenance',
    'deprecated'
);


--
-- Name: client_systems_system_type_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.client_systems_system_type_enum AS ENUM (
    'web_application',
    'mobile_app',
    'api_client',
    'third_party_integration',
    'internal_system'
);


--
-- Name: consumer_affairs_complaint_status_history_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.consumer_affairs_complaint_status_history_status_enum AS ENUM (
    'submitted',
    'under_review',
    'investigating',
    'resolved',
    'closed'
);


--
-- Name: consumer_affairs_complaints_category_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.consumer_affairs_complaints_category_enum AS ENUM (
    'Billing & Charges',
    'Service Quality',
    'Network Issues',
    'Customer Service',
    'Contract Disputes',
    'Accessibility',
    'Fraud & Scams',
    'Other'
);


--
-- Name: consumer_affairs_complaints_priority_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.consumer_affairs_complaints_priority_enum AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


--
-- Name: consumer_affairs_complaints_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.consumer_affairs_complaints_status_enum AS ENUM (
    'submitted',
    'under_review',
    'investigating',
    'resolved',
    'closed'
);


--
-- Name: data_breach_report_status_history_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.data_breach_report_status_history_status_enum AS ENUM (
    'submitted',
    'under_review',
    'investigating',
    'resolved',
    'closed'
);


--
-- Name: data_breach_reports_category_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.data_breach_reports_category_enum AS ENUM (
    'Unauthorized Data Access',
    'Data Misuse or Sharing',
    'Privacy Violations',
    'Identity Theft',
    'Phishing Attempts',
    'Data Loss or Theft',
    'Consent Violations',
    'Other'
);


--
-- Name: data_breach_reports_priority_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.data_breach_reports_priority_enum AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


--
-- Name: data_breach_reports_severity_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.data_breach_reports_severity_enum AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


--
-- Name: data_breach_reports_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.data_breach_reports_status_enum AS ENUM (
    'submitted',
    'under_review',
    'investigating',
    'resolved',
    'closed'
);

--
-- Name: evaluations_evaluation_type_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.evaluations_evaluation_type_enum AS ENUM (
    'individual_license_a',
    'class_license_b',
    'network_service',
    'postal_service',
    'radio_communication',
    'satellite_service',
    'tv_broadcasting',
    'university_radio',
    'type_approval_certificate'
);


--
-- Name: evaluations_recommendation_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.evaluations_recommendation_enum AS ENUM (
    'approve',
    'conditional_approve',
    'reject'
);


--
-- Name: evaluations_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.evaluations_status_enum AS ENUM (
    'draft',
    'completed',
    'approved',
    'rejected'
);


--
-- Name: licenses_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.licenses_status_enum AS ENUM (
    'active',
    'expired',
    'suspended',
    'revoked',
    'under_review'
);


--
-- Name: proof_of_payments_payment_method_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.proof_of_payments_payment_method_enum AS ENUM (
    'Bank Transfer',
    'Mobile Money',
    'Credit Card',
    'Cash'
);


--
-- Name: proof_of_payments_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.proof_of_payments_status_enum AS ENUM (
    'pending',
    'approved',
    'rejected'
);


--
-- Name: users_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.users_status_enum AS ENUM (
    'active',
    'inactive',
    'suspended'
);

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activity_notes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.activity_notes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    entity_type character varying(100) NOT NULL,
    entity_id uuid NOT NULL,
    note text NOT NULL,
    note_type character varying DEFAULT 'general_note'::character varying NOT NULL,
    status character varying DEFAULT 'active'::character varying NOT NULL,
    category character varying(100),
    step character varying(100),
    metadata json,
    priority character varying(20) DEFAULT 'normal'::character varying NOT NULL,
    is_visible boolean DEFAULT true NOT NULL,
    is_internal boolean DEFAULT false NOT NULL,
    created_by character varying NOT NULL,
    updated_by character varying,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    archived_at timestamp without time zone,
    deleted_at timestamp without time zone
);



--
-- Name: addresses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.addresses (
    address_id character varying(36) NOT NULL,
    address_line_1 text NOT NULL,
    address_type character varying DEFAULT 'postal'::character varying,
    entity_type character varying DEFAULT 'user'::character varying,
    entity_id character varying(36),
    address_line_2 text,
    address_line_3 text,
    postal_code character varying(9) NOT NULL,
    country character varying(50) NOT NULL,
    city character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: admin_alerts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.admin_alerts (
    admin_alert_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    category character varying NOT NULL,
    message character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);



--
-- Name: applicant_disclosure; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.applicant_disclosure (
    applicant_disclosure_id character varying(36) NOT NULL,
    applicant_id character varying NOT NULL,
    censured text,
    disciplined text,
    penalized text,
    suspended text,
    prosecuted text,
    convicted_warned_conduct text,
    investigated_subjected text,
    failed_debt_issued text,
    litigation text,
    adjudged_insolvent text,
    creditor_compromise text,
    liquidator_receiver_property_judicial_manager text,
    voluntary_winding_up text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: applicants; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.applicants (
    applicant_id character varying(36) NOT NULL,
    name character varying(255) NOT NULL,
    business_registration_number character varying,
    tpin character varying NOT NULL,
    website character varying,
    email character varying NOT NULL,
    phone character varying(20) NOT NULL,
    fax character varying(20),
    level_of_insurance_cover text,
    date_incorporation date NOT NULL,
    place_incorporation character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: application_status_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.application_status_history (
    history_id character varying(36) NOT NULL,
    application_id character varying NOT NULL,
    status character varying(30) NOT NULL,
    previous_status character varying(30),
    comments text,
    reason text,
    changed_by character varying NOT NULL,
    changed_at timestamp without time zone DEFAULT now() NOT NULL,
    estimated_completion_date timestamp without time zone
);



--
-- Name: applications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.applications (
    application_id character varying(36) NOT NULL,
    application_number character varying NOT NULL,
    applicant_id character varying NOT NULL,
    license_category_id character varying NOT NULL,
    status character varying DEFAULT 'draft'::character varying NOT NULL,
    current_step integer NOT NULL,
    progress_percentage integer NOT NULL,
    submitted_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    assigned_to character varying,
    assigned_at timestamp without time zone,
    deleted_at timestamp without time zone
);



--
-- Name: audit_trails; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.audit_trails (
    audit_id character varying(36) NOT NULL,
    action character varying DEFAULT 'create'::character varying NOT NULL,
    module character varying,
    status character varying,
    resource_type character varying(255) NOT NULL,
    resource_id character varying(255),
    description text,
    old_values json,
    new_values json,
    metadata json,
    ip_address character varying(45),
    user_agent text,
    session_id character varying(255),
    error_message text,
    user_id character varying(36),
    created_at timestamp without time zone DEFAULT now() NOT NULL
);



--
-- Name: ceir_certification_bodies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ceir_certification_bodies (
    certification_body_id uuid NOT NULL,
    organization_name character varying(255) NOT NULL,
    short_name character varying(100),
    body_type character varying(50) NOT NULL,
    registration_number character varying(100) NOT NULL,
    country character varying(100) NOT NULL,
    address_id character varying,
    contact_id character varying,
    primary_email character varying(255),
    primary_phone character varying(50),
    website_url character varying(500),
    accreditation_body character varying(255),
    accreditation_number character varying(100),
    accreditation_date date,
    accreditation_expiry_date date,
    accreditation_status character varying(50) DEFAULT 'active'::character varying NOT NULL,
    certification_scopes text NOT NULL,
    authorized_equipment_categories text,
    competent_standards text,
    authorized_frequency_bands text,
    is_macra_recognized boolean DEFAULT false NOT NULL,
    can_issue_ceir_certificates boolean DEFAULT false NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: ceir_equipment_specifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ceir_equipment_specifications (
    specification_id uuid NOT NULL,
    device_id uuid NOT NULL,
    equipment_category_id uuid NOT NULL,
    hardware_version character varying(100),
    software_version character varying(100),
    firmware_version character varying(100),
    supported_network_technologies text,
    operating_frequency_bands text,
    max_transmit_power_dbm numeric(5,2),
    receiver_sensitivity_dbm numeric(6,2),
    antenna_type character varying(50),
    antenna_gain_dbi numeric(4,2),
    sar_head_wkg numeric(4,2),
    sar_body_wkg numeric(4,2),
    operating_temperature_range character varying(100),
    storage_temperature_range character varying(100),
    operating_humidity_range character varying(100),
    power_supply_voltage character varying(100),
    power_consumption_watts numeric(5,2),
    battery_capacity_mah integer,
    physical_dimensions character varying(100),
    weight_grams numeric(6,2),
    ip_rating character varying(20),
    supported_sim_types text,
    memory_specifications text,
    display_specifications text,
    camera_specifications text,
    connectivity_features text,
    security_features text,
    compliance_certifications text,
    specification_date date,
    technical_notes text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: ceir_equipment_type_categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ceir_equipment_type_categories (
    category_id uuid NOT NULL,
    category_type character varying(50) NOT NULL,
    category_name character varying(100) NOT NULL,
    description text,
    ceir_standard_code character varying(50) NOT NULL,
    supported_frequency_bands text,
    required_standards text,
    required_test_procedures text,
    max_transmit_power_dbm numeric(5,2),
    requires_sar_testing boolean DEFAULT false NOT NULL,
    requires_emc_testing boolean DEFAULT true NOT NULL,
    requires_rf_testing boolean DEFAULT true NOT NULL,
    approval_validity_months integer DEFAULT 60 NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: ceir_technical_standards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ceir_technical_standards (
    standard_id uuid NOT NULL,
    standard_reference character varying(100) NOT NULL,
    standard_title character varying(255) NOT NULL,
    description text,
    standard_type character varying(50) NOT NULL,
    issuing_organization character varying(50) NOT NULL,
    version character varying(50) NOT NULL,
    publication_date date NOT NULL,
    effective_date date,
    expiry_date date,
    status character varying(50) DEFAULT 'active'::character varying NOT NULL,
    applicable_frequency_bands text,
    applicable_equipment_categories text,
    test_methods text,
    compliance_requirements text,
    superseded_by character varying(100),
    supersedes character varying(100),
    document_url character varying(500),
    is_mandatory boolean DEFAULT true NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: ceir_test_reports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ceir_test_reports (
    report_id uuid NOT NULL,
    report_number character varying(100) NOT NULL,
    application_id character varying,
    device_id uuid NOT NULL,
    certification_body_id uuid NOT NULL,
    technical_standard_id uuid NOT NULL,
    test_type character varying(50) NOT NULL,
    report_title character varying(255) NOT NULL,
    test_date date NOT NULL,
    report_date date NOT NULL,
    expiry_date date,
    test_result character varying(50) NOT NULL,
    report_status character varying(50) DEFAULT 'draft'::character varying NOT NULL,
    test_methods text,
    tested_frequency_bands text,
    max_measured_power_dbm numeric(5,2),
    sar_value_wkg numeric(4,2),
    test_conditions text,
    test_equipment text,
    test_results_details text,
    deviations text,
    recommendations text,
    test_engineer character varying(255),
    reviewed_by character varying(255),
    approved_by character varying(255),
    report_file_path character varying(500),
    is_valid boolean DEFAULT true NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: client_systems; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.client_systems (
    client_system_id uuid NOT NULL,
    name character varying(255) NOT NULL,
    system_code character varying(100) NOT NULL,
    description text,
    system_type public.client_systems_system_type_enum DEFAULT 'web_application'::public.client_systems_system_type_enum NOT NULL,
    status public.client_systems_status_enum DEFAULT 'active'::public.client_systems_status_enum NOT NULL,
    api_endpoint character varying(255),
    callback_url character varying(500),
    contact_email character varying(255),
    contact_phone character varying(20),
    organization character varying(255),
    access_permissions text,
    last_accessed_at timestamp without time zone,
    version character varying(50),
    notes text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: consumer_affairs_complaint_attachments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.consumer_affairs_complaint_attachments (
    attachment_id character varying(36) NOT NULL,
    complaint_id character varying(36) NOT NULL,
    file_name character varying(255) NOT NULL,
    file_path character varying(255) NOT NULL,
    file_type character varying(100) NOT NULL,
    file_size bigint NOT NULL,
    uploaded_at timestamp without time zone DEFAULT now() NOT NULL,
    uploaded_by character varying(36) NOT NULL
);



--
-- Name: consumer_affairs_complaint_status_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.consumer_affairs_complaint_status_history (
    history_id character varying(36) NOT NULL,
    complaint_id character varying(36) NOT NULL,
    status public.consumer_affairs_complaint_status_history_status_enum NOT NULL,
    comment text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying(36) NOT NULL
);



--
-- Name: consumer_affairs_complaints; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.consumer_affairs_complaints (
    complaint_id character varying(36) NOT NULL,
    complaint_number character varying NOT NULL,
    complainant_id character varying(36) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    category public.consumer_affairs_complaints_category_enum NOT NULL,
    status public.consumer_affairs_complaints_status_enum DEFAULT 'submitted'::public.consumer_affairs_complaints_status_enum NOT NULL,
    priority public.consumer_affairs_complaints_priority_enum DEFAULT 'medium'::public.consumer_affairs_complaints_priority_enum NOT NULL,
    assigned_to character varying(36),
    resolution text,
    internal_notes text,
    resolved_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying(36),
    updated_by character varying(36)
);



--
-- Name: contact_persons; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.contact_persons (
    contact_id character varying(36) NOT NULL,
    entity_type character varying DEFAULT 'applicant'::character varying,
    entity_id character varying,
    first_name character varying(100) NOT NULL,
    application_id character varying,
    last_name character varying(100) NOT NULL,
    middle_name character varying(100),
    designation character varying(50) NOT NULL,
    email character varying(255) NOT NULL,
    phone character varying(20) NOT NULL,
    is_primary boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: contacts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.contacts (
    contact_id character varying(36) NOT NULL,
    telephone character varying(20) NOT NULL,
    email character varying(255),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    entity_type character varying DEFAULT 'user'::character varying,
    entity_id character varying,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: data_breach_report_attachments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_breach_report_attachments (
    attachment_id character varying(36) NOT NULL,
    report_id character varying NOT NULL,
    file_name character varying(255) NOT NULL,
    file_path character varying(255) NOT NULL,
    file_type character varying(100) NOT NULL,
    file_size bigint NOT NULL,
    uploaded_at timestamp without time zone DEFAULT now() NOT NULL,
    uploaded_by character varying NOT NULL
);



--
-- Name: data_breach_report_status_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_breach_report_status_history (
    history_id character varying(36) NOT NULL,
    report_id character varying NOT NULL,
    status public.data_breach_report_status_history_status_enum NOT NULL,
    comment text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL
);



--
-- Name: data_breach_reports; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_breach_reports (
    report_id character varying(36) NOT NULL,
    report_number character varying NOT NULL,
    reporter_id character varying NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    category public.data_breach_reports_category_enum NOT NULL,
    severity public.data_breach_reports_severity_enum NOT NULL,
    status public.data_breach_reports_status_enum DEFAULT 'submitted'::public.data_breach_reports_status_enum NOT NULL,
    priority public.data_breach_reports_priority_enum DEFAULT 'medium'::public.data_breach_reports_priority_enum NOT NULL,
    incident_date date NOT NULL,
    organization_involved character varying(255) NOT NULL,
    affected_data_types text,
    contact_attempts text,
    assigned_to character varying,
    resolution text,
    internal_notes text,
    resolved_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: departments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.departments (
    department_id character varying(36) NOT NULL,
    code character varying(5) NOT NULL,
    name character varying(100) NOT NULL,
    description text NOT NULL,
    email character varying(100) NOT NULL,
    manager_id character varying,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: devices; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.devices (
    device_id uuid NOT NULL,
    application_id character varying,
    manufacturer_name character varying(100) NOT NULL,
    manufacturer_address text,
    manufacturer_country character varying(100) NOT NULL,
    brand_trade_name character varying(100),
    product_type_name character varying(100),
    equipment_category character varying,
    imei character varying(15),
    approval_status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    device_approval_number character varying(100),
    device_approval_date date,
    equipment_model character varying,
    approval_notes text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.documents (
    document_id character varying(36) NOT NULL,
    document_type character varying,
    file_name character varying NOT NULL,
    entity_type character varying(255),
    entity_id uuid,
    file_path text NOT NULL,
    file_size integer,
    mime_type character varying(100) NOT NULL,
    is_required boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: employee_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.employee_roles (
    employee_id character varying NOT NULL,
    role_id character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: employees; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.employees (
    user_id character varying(36) NOT NULL,
    employee_number character varying NOT NULL,
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    middle_name character varying(100),
    "position" character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: evaluation_criteria; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.evaluation_criteria (
    criteria_id character varying(36) NOT NULL,
    evaluation_id character varying NOT NULL,
    category character varying(255) NOT NULL,
    subcategory character varying(255) NOT NULL,
    score numeric(5,2) NOT NULL,
    weight numeric(3,2) NOT NULL,
    max_marks integer,
    awarded_marks integer,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: evaluations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.evaluations (
    evaluation_id character varying(36) NOT NULL,
    application_id character varying NOT NULL,
    evaluator_id character varying NOT NULL,
    evaluation_type public.evaluations_evaluation_type_enum NOT NULL,
    status public.evaluations_status_enum DEFAULT 'draft'::public.evaluations_status_enum NOT NULL,
    total_score numeric(5,2) NOT NULL,
    recommendation public.evaluations_recommendation_enum NOT NULL,
    evaluators_notes text,
    shareholding_compliance boolean,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone,
    completed_at timestamp without time zone
);



--
-- Name: identification_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.identification_types (
    identification_type_id character varying(36) NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: invoices; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.invoices (
    invoice_id character varying(36) NOT NULL,
    client_id character varying NOT NULL,
    invoice_number character varying NOT NULL,
    amount numeric(10,2) NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    entity_type character varying(255),
    entity_id uuid,
    issue_date timestamp without time zone,
    due_date timestamp without time zone,
    description text NOT NULL,
    items json,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: legal_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.legal_history (
    legal_history_id character varying(36) NOT NULL,
    application_id character varying NOT NULL,
    criminal_history boolean DEFAULT false NOT NULL,
    criminal_details text,
    bankruptcy_history boolean DEFAULT false NOT NULL,
    bankruptcy_details text,
    regulatory_actions boolean DEFAULT false NOT NULL,
    regulatory_details text,
    litigation_history boolean DEFAULT false NOT NULL,
    litigation_details text,
    compliance_record text,
    previous_licenses text,
    declaration_accepted boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: license_categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.license_categories (
    license_category_id character varying(36) NOT NULL,
    license_type_id character varying NOT NULL,
    name character varying(255) NOT NULL,
    fee character varying(20) NOT NULL,
    description text NOT NULL,
    authorizes text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone,
    parent_id character varying
);



--
-- Name: license_category_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.license_category_documents (
    license_category_document_id character varying(36) NOT NULL,
    license_category_id character varying NOT NULL,
    name character varying(255) NOT NULL,
    is_required boolean DEFAULT true NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: license_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.license_types (
    license_type_id character varying(36) NOT NULL,
    name character varying(255) NOT NULL,
    description text NOT NULL,
    validity integer NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    code character varying,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: licenses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.licenses (
    license_id character varying(36) NOT NULL,
    description character varying,
    license_number character varying NOT NULL,
    application_id character varying NOT NULL,
    applicant_id uuid NOT NULL,
    license_type_id uuid NOT NULL,
    status public.licenses_status_enum DEFAULT 'active'::public.licenses_status_enum NOT NULL,
    issue_date date NOT NULL,
    expiry_date date NOT NULL,
    issued_by character varying NOT NULL,
    code character varying,
    conditions text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    notification_id character varying(36) NOT NULL,
    type character varying(50) NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    priority character varying(50) DEFAULT 'medium'::character varying NOT NULL,
    recipient_type character varying(50) NOT NULL,
    recipient_id character varying,
    recipient_email character varying,
    recipient_phone character varying,
    subject character varying(255) NOT NULL,
    message text NOT NULL,
    html_content text,
    entity_type character varying(50),
    entity_id uuid,
    metadata json,
    external_id character varying,
    error_message text,
    retry_count integer DEFAULT 0 NOT NULL,
    is_read boolean DEFAULT false NOT NULL,
    sent_at timestamp without time zone,
    delivered_at timestamp without time zone,
    action_url text,
    expires_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone,
    read_at timestamp without time zone
);



--
-- Name: organizations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organizations (
    organization_id character varying(36) NOT NULL,
    name character varying(255) NOT NULL,
    registration_number character varying NOT NULL,
    website character varying NOT NULL,
    email character varying NOT NULL,
    phone character varying(20) NOT NULL,
    fax character varying(20),
    physical_address_id character varying,
    postal_address_id character varying,
    contact_id character varying,
    date_incorporation date NOT NULL,
    place_incorporation character varying NOT NULL,
    created_by character varying,
    updated_by character varying,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone
);



--
-- Name: payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.payments (
    payment_id character varying(36) NOT NULL,
    invoice_number character varying NOT NULL,
    amount numeric(15,2) NOT NULL,
    currency character varying DEFAULT 'MWK'::character varying NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    payment_type character varying NOT NULL,
    description text NOT NULL,
    issue_date date NOT NULL,
    due_date date,
    paid_date date,
    payment_method character varying,
    notes text,
    transaction_reference character varying,
    proof_of_payment_url text,
    proof_of_payment_notes text,
    proof_of_payment_uploaded_at timestamp without time zone,
    invoice_id uuid,
    entity_type character varying(255),
    entity_id uuid,
    user_id character varying NOT NULL,
    created_by character varying NOT NULL,
    updated_by character varying,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    application_id character varying(36)
);



--
-- Name: permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.permissions (
    permission_id character varying(36) NOT NULL,
    name character varying(100) NOT NULL,
    description character varying(255) NOT NULL,
    category character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: postal_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.postal_codes (
    postal_code_id character varying(36) NOT NULL,
    region character varying NOT NULL,
    district character varying NOT NULL,
    location character varying NOT NULL,
    postal_code character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone
);



--
-- Name: professional_services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.professional_services (
    professional_services_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    application_id character varying(36) NOT NULL,
    consultants text NOT NULL,
    service_providers text NOT NULL,
    technical_support text NOT NULL,
    maintenance_arrangements text NOT NULL,
    professional_partnerships text,
    outsourced_services text,
    quality_assurance text,
    training_programs text,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying(36) NOT NULL,
    updated_by character varying(36),
    deleted_at timestamp without time zone
);



--
-- Name: proof_of_payments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.proof_of_payments (
    proof_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    transaction_reference character varying NOT NULL,
    amount numeric(15,2) NOT NULL,
    currency character varying NOT NULL,
    payment_method public.proof_of_payments_payment_method_enum NOT NULL,
    payment_date date NOT NULL,
    document_path character varying NOT NULL,
    original_filename character varying NOT NULL,
    file_size integer NOT NULL,
    mime_type character varying NOT NULL,
    status public.proof_of_payments_status_enum DEFAULT 'pending'::public.proof_of_payments_status_enum NOT NULL,
    notes text,
    review_notes text,
    reviewed_by character varying,
    reviewed_at timestamp without time zone,
    payment_id character varying NOT NULL,
    submitted_by character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);



--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.role_permissions (
    role_id character varying(36) NOT NULL,
    permission_id character varying(36) NOT NULL
);



--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    role_id character varying(36) NOT NULL,
    name character varying(50) NOT NULL,
    description character varying(255),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: scope_of_service; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.scope_of_service (
    scope_of_service_id character varying(36) NOT NULL,
    application_id character varying NOT NULL,
    nature_of_service character varying(300) NOT NULL,
    premises character varying(300) NOT NULL,
    transport_type character varying(300) NOT NULL,
    customer_assistance character varying(300) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: shareholder_details; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shareholder_details (
    shareholder_id character varying(36) NOT NULL,
    stakeholder_id character varying NOT NULL,
    shareholding_percent integer NOT NULL,
    description character varying(300),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: shortcodes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shortcodes (
    shortcode_id character varying(36) NOT NULL,
    shortcode character varying(5) NOT NULL,
    application_id character varying(36),
    assigned_to character varying(36),
    shortcode_length integer DEFAULT 3 NOT NULL,
    audience character varying(10) DEFAULT 'community'::character varying NOT NULL,
    status character varying(10) DEFAULT 'inactive'::character varying NOT NULL,
    category character varying(20) DEFAULT 'reserved'::character varying NOT NULL,
    description character varying(255),
    notes character varying(255),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying NOT NULL,
    updated_by character varying
);



--
-- Name: stakeholders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.stakeholders (
    stakeholder_id character varying(36) NOT NULL,
    application_id character varying(36) NOT NULL,
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    middle_name character varying(100),
    nationality character varying(50) NOT NULL,
    "position" character varying DEFAULT 'shareholder'::character varying NOT NULL,
    profile character varying(300) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying(36),
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying(36),
    deleted_at timestamp without time zone
);



--
-- Name: tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tasks (
    task_id character varying(36) NOT NULL,
    task_number character varying(100) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    task_type character varying(50) DEFAULT 'application'::character varying NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    priority character varying(50) DEFAULT 'medium'::character varying NOT NULL,
    entity_type character varying(50),
    entity_id character varying(36),
    assigned_to character varying,
    assigned_by character varying NOT NULL,
    assigned_at timestamp without time zone,
    due_date timestamp without time zone,
    completed_at timestamp without time zone,
    review text,
    review_notes text,
    completion_notes text,
    metadata json,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_by character varying,
    deleted_at timestamp without time zone
);



--
-- Name: user_identifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_identifications (
    identification_type_id character varying NOT NULL,
    user_id character varying(36) NOT NULL,
    identification_value character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying
);



--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_roles (
    user_id character varying(36) NOT NULL,
    role_id character varying(36) NOT NULL
);



--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    user_id character varying(36) NOT NULL,
    email character varying(255) NOT NULL,
    password character varying(255) NOT NULL,
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    middle_name character varying(100),
    phone character varying(20) NOT NULL,
    department_id character varying(100),
    organization_id character varying(100),
    status public.users_status_enum DEFAULT 'active'::public.users_status_enum NOT NULL,
    profile_image text,
    two_factor_next_verification date,
    two_factor_code character varying(65),
    two_factor_enabled boolean DEFAULT false NOT NULL,
    two_factor_temp character varying(64),
    email_verified_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    deleted_at timestamp without time zone,
    created_by character varying,
    updated_by character varying,
    last_login timestamp without time zone
);



--
-- Data for Name: activity_notes; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.activity_notes (id, entity_type, entity_id, note, note_type, status, category, step, metadata, priority, is_visible, is_internal, created_by, updated_by, created_at, updated_at, archived_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: addresses; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.addresses (address_id, address_line_1, address_type, entity_type, entity_id, address_line_2, address_line_3, postal_code, country, city, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: admin_alerts; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.admin_alerts (admin_alert_id, category, message, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: applicant_disclosure; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.applicant_disclosure (applicant_disclosure_id, applicant_id, censured, disciplined, penalized, suspended, prosecuted, convicted_warned_conduct, investigated_subjected, failed_debt_issued, litigation, adjudged_insolvent, creditor_compromise, liquidator_receiver_property_judicial_manager, voluntary_winding_up, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: applicants; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.applicants (applicant_id, name, business_registration_number, tpin, website, email, phone, fax, level_of_insurance_cover, date_incorporation, place_incorporation, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: application_status_history; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.application_status_history (history_id, application_id, status, previous_status, comments, reason, changed_by, changed_at, estimated_completion_date) FROM stdin;
\.


--
-- Data for Name: applications; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.applications (application_id, application_number, applicant_id, license_category_id, status, current_step, progress_percentage, submitted_at, created_at, created_by, updated_at, updated_by, assigned_to, assigned_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: audit_trails; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.audit_trails (audit_id, action, module, status, resource_type, resource_id, description, old_values, new_values, metadata, ip_address, user_agent, session_id, error_message, user_id, created_at) FROM stdin;
\.


--
-- Data for Name: ceir_certification_bodies; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.ceir_certification_bodies (certification_body_id, organization_name, short_name, body_type, registration_number, country, address_id, contact_id, primary_email, primary_phone, website_url, accreditation_body, accreditation_number, accreditation_date, accreditation_expiry_date, accreditation_status, certification_scopes, authorized_equipment_categories, competent_standards, authorized_frequency_bands, is_macra_recognized, can_issue_ceir_certificates, is_active, notes, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: ceir_equipment_specifications; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.ceir_equipment_specifications (specification_id, device_id, equipment_category_id, hardware_version, software_version, firmware_version, supported_network_technologies, operating_frequency_bands, max_transmit_power_dbm, receiver_sensitivity_dbm, antenna_type, antenna_gain_dbi, sar_head_wkg, sar_body_wkg, operating_temperature_range, storage_temperature_range, operating_humidity_range, power_supply_voltage, power_consumption_watts, battery_capacity_mah, physical_dimensions, weight_grams, ip_rating, supported_sim_types, memory_specifications, display_specifications, camera_specifications, connectivity_features, security_features, compliance_certifications, specification_date, technical_notes, is_active, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: ceir_equipment_type_categories; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.ceir_equipment_type_categories (category_id, category_type, category_name, description, ceir_standard_code, supported_frequency_bands, required_standards, required_test_procedures, max_transmit_power_dbm, requires_sar_testing, requires_emc_testing, requires_rf_testing, approval_validity_months, is_active, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: ceir_technical_standards; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.ceir_technical_standards (standard_id, standard_reference, standard_title, description, standard_type, issuing_organization, version, publication_date, effective_date, expiry_date, status, applicable_frequency_bands, applicable_equipment_categories, test_methods, compliance_requirements, superseded_by, supersedes, document_url, is_mandatory, is_active, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: ceir_test_reports; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.ceir_test_reports (report_id, report_number, application_id, device_id, certification_body_id, technical_standard_id, test_type, report_title, test_date, report_date, expiry_date, test_result, report_status, test_methods, tested_frequency_bands, max_measured_power_dbm, sar_value_wkg, test_conditions, test_equipment, test_results_details, deviations, recommendations, test_engineer, reviewed_by, approved_by, report_file_path, is_valid, notes, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: client_systems; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.client_systems (client_system_id, name, system_code, description, system_type, status, api_endpoint, callback_url, contact_email, contact_phone, organization, access_permissions, last_accessed_at, version, notes, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: consumer_affairs_complaint_attachments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.consumer_affairs_complaint_attachments (attachment_id, complaint_id, file_name, file_path, file_type, file_size, uploaded_at, uploaded_by) FROM stdin;
\.


--
-- Data for Name: consumer_affairs_complaint_status_history; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.consumer_affairs_complaint_status_history (history_id, complaint_id, status, comment, created_at, created_by) FROM stdin;
\.


--
-- Data for Name: consumer_affairs_complaints; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.consumer_affairs_complaints (complaint_id, complaint_number, complainant_id, title, description, category, status, priority, assigned_to, resolution, internal_notes, resolved_at, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: contact_persons; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.contact_persons (contact_id, entity_type, entity_id, first_name, application_id, last_name, middle_name, designation, email, phone, is_primary, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: contacts; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.contacts (contact_id, telephone, email, created_at, entity_type, entity_id, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: data_breach_report_attachments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.data_breach_report_attachments (attachment_id, report_id, file_name, file_path, file_type, file_size, uploaded_at, uploaded_by) FROM stdin;
\.


--
-- Data for Name: data_breach_report_status_history; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.data_breach_report_status_history (history_id, report_id, status, comment, created_at, created_by) FROM stdin;
\.


--
-- Data for Name: data_breach_reports; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.data_breach_reports (report_id, report_number, reporter_id, title, description, category, severity, status, priority, incident_date, organization_involved, affected_data_types, contact_attempts, assigned_to, resolution, internal_notes, resolved_at, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: departments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.departments (department_id, code, name, description, email, manager_id, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
26586627-ec97-4b1e-a0ef-52c419a018ab	PROC	Procurement	Handles procurement and purchasing of goods and services.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
69db7d24-6a9f-4091-af6c-30a73c51cb30	HR	Human Resources	Responsible for recruitment, staff welfare and training.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
6b8c1b9d-ed9b-40a9-958c-97a508bcdf0f	ICT	Information and Communication Technology	Manages IT infrastructure and communication systems.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
ffc37363-c0fa-40fa-8eaa-c9ac9c534505	ADMIN	Administration	Provides administrative support to the organization.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
7c5aae42-9d77-4b28-8302-c194fe9d12e2	POST	Postal	Oversees postal services and operations.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
1a297fc0-7333-4b3e-9d4d-c2d3dfb2fb52	STD	Standards	Ensures compliance with national and international standards.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
c0afa99f-5180-462c-9295-6f17a5fa7f92	AUD	Internal Audit	Conducts internal audits to ensure financial and operational integrity.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
b5851c01-b2bb-4925-8617-f856783255f6	IR	International Relations	Manages relationships with international partners and organizations.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
8ac0f8fc-754a-4fe7-acc6-b6bc09276618	FIN	Finance	Handles financial planning, budgeting, and accounting.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
73b43672-a606-46a5-9abe-c6a2dd530196	NET	Networks	Responsible for network infrastructure and security.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
2535e1d3-44c1-40b6-aea8-5ad8846e8b22	LEGAL	Legal	Provides legal advice and manages compliance with laws.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
7e01e84f-e47b-44f8-a215-b0e191fa405e	TEL	Telecommunications	Regulates telecommunications services and providers.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
fdaf11ca-0b4e-4ac1-92cd-eb0993900d97	CONAF	Consumer Affairs	Protects consumer rights and addresses complaints.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
5a403ebe-6fee-4860-8c64-deb306639f09	DPA	Data Protection	Ensures data privacy and protection compliance.	<EMAIL>	\N	2025-07-29 13:45:45.202292	2025-07-29 13:45:45.202292	\N	\N	\N
\.


--
-- Data for Name: devices; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.devices (device_id, application_id, manufacturer_name, manufacturer_address, manufacturer_country, brand_trade_name, product_type_name, equipment_category, imei, approval_status, device_approval_number, device_approval_date, equipment_model, approval_notes, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.documents (document_id, document_type, file_name, entity_type, entity_id, file_path, file_size, mime_type, is_required, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: employee_roles; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.employee_roles (employee_id, role_id, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: employees; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.employees (user_id, employee_number, first_name, last_name, middle_name, "position", created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: evaluation_criteria; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.evaluation_criteria (criteria_id, evaluation_id, category, subcategory, score, weight, max_marks, awarded_marks, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: evaluations; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.evaluations (evaluation_id, application_id, evaluator_id, evaluation_type, status, total_score, recommendation, evaluators_notes, shareholding_compliance, created_at, created_by, updated_at, updated_by, deleted_at, completed_at) FROM stdin;
\.


--
-- Data for Name: identification_types; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.identification_types (identification_type_id, name, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: invoices; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.invoices (invoice_id, client_id, invoice_number, amount, status, entity_type, entity_id, issue_date, due_date, description, items, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: legal_history; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.legal_history (legal_history_id, application_id, criminal_history, criminal_details, bankruptcy_history, bankruptcy_details, regulatory_actions, regulatory_details, litigation_history, litigation_details, compliance_record, previous_licenses, declaration_accepted, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_categories; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.license_categories (license_category_id, license_type_id, name, fee, description, authorizes, created_at, created_by, updated_at, updated_by, deleted_at, parent_id) FROM stdin;
********-337d-4d3d-92e9-b4a0a15bae13	92ce0377-e8fd-4a38-80ec-a66c2729448d	Mobile Network Operator (MNO)	500000.00	License for operating mobile telecommunications networks	Operation of mobile telecommunications networks including voice, data, and messaging services	2025-07-29 13:45:43.530704	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.530704	\N	\N	\N
3b166600-9380-4303-b8d1-56a2f50bcb9e	92ce0377-e8fd-4a38-80ec-a66c2729448d	Internet Service Provider (ISP)	250000.00	License for providing internet services to end users	Provision of internet access services to residential and business customers	2025-07-29 13:45:43.535459	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.535459	\N	\N	\N
98d3ecfa-2001-478b-bc44-62848dbb6e51	92ce0377-e8fd-4a38-80ec-a66c2729448d	Fixed Network Operator	300000.00	License for operating fixed telecommunications networks	Operation of fixed-line telecommunications networks and services	2025-07-29 13:45:43.540011	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.540011	\N	\N	\N
9b01139b-3751-4e7d-aedb-eff45ad2d6bd	92ce0377-e8fd-4a38-80ec-a66c2729448d	Virtual Network Operator (MVNO)	150000.00	License for mobile virtual network operators	Provision of mobile services using other operators infrastructure	2025-07-29 13:45:43.546114	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.546114	\N	\N	\N
2a93c99e-7271-4617-9c7b-ad287e982def	92ce0377-e8fd-4a38-80ec-a66c2729448d	Satellite Communication Services	400000.00	License for satellite-based communication services	Operation of satellite communication services and earth stations	2025-07-29 13:45:43.560205	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.560205	\N	\N	\N
8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	82daf945-093c-446d-b6e1-6af8c073bf99	International Commercial Courier	100000.00	License for international courier and express delivery services	Collection, transport, and delivery of international mail and packages	2025-07-29 13:45:43.56773	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.56773	\N	\N	\N
06c9f1cf-da5f-4537-be91-22c61f0c40b0	82daf945-093c-446d-b6e1-6af8c073bf99	Domestic Commercial Courier	50000.00	License for domestic courier services within Malawi	Collection, transport, and delivery of domestic mail and packages	2025-07-29 13:45:43.581688	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.581688	\N	\N	\N
99c88fcd-8a69-424c-b40e-7cc141b1e75c	82daf945-093c-446d-b6e1-6af8c073bf99	Intra-City Commercial	25000.00	License for courier services within city limits	Collection and delivery of mail and packages within city boundaries	2025-07-29 13:45:43.594077	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.594077	\N	\N	\N
f8e211a7-9f08-4b68-b7dc-2706b58383b4	82daf945-093c-446d-b6e1-6af8c073bf99	District Commercial	30000.00	License for courier services within district boundaries	Collection and delivery of mail and packages within district boundaries	2025-07-29 13:45:43.609918	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.609918	\N	\N	\N
ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	82daf945-093c-446d-b6e1-6af8c073bf99	Freight Forwarders	75000.00	License for freight forwarding and logistics services	Freight forwarding, customs clearance, and logistics services	2025-07-29 13:45:43.622049	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.622049	\N	\N	\N
ec9b1045-fd23-49f7-86b9-cea0cb5372f1	03b97f8c-65ee-426a-8924-c07317606641	Type Approval Certificate	300.00	Certificate for telecommunications equipment type approval	Import, sale, and use of approved telecommunications equipment	2025-07-29 13:45:43.636794	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.636794	\N	\N	\N
7fa90d64-7965-4552-aa73-09474d35fe56	03b97f8c-65ee-426a-8924-c07317606641	Short Code Allocation	0	Allocation of short codes for SMS and USSD services	Use of allocated short codes for commercial services	2025-07-29 13:45:43.649332	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.649332	\N	\N	\N
a20650aa-9ab6-479a-86a0-6ad45edc6db2	e8616674-c074-4c3f-95e3-6d7eb41b364c	Radio Broadcasting License	200000.00	License for radio broadcasting services	Operation of radio broadcasting stations and content transmission	2025-07-29 13:45:43.66298	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.66298	\N	\N	\N
0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	e8616674-c074-4c3f-95e3-6d7eb41b364c	Television Broadcasting License	350000.00	License for television broadcasting services	Operation of television broadcasting stations and content transmission	2025-07-29 13:45:43.671454	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.671454	\N	\N	\N
c6dc15ef-21d3-4859-8f96-926a59e1be31	e8616674-c074-4c3f-95e3-6d7eb41b364c	Community Radio License	50000.00	License for community-based radio broadcasting	Operation of community radio stations for local content	2025-07-29 13:45:43.677541	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.677541	\N	\N	\N
8b2c9cf9-115c-4574-8888-28e64282eaef	e8616674-c074-4c3f-95e3-6d7eb41b364c	Campus Radio License	25000.00	License for educational institution radio broadcasting	Operation of radio stations within educational institutions	2025-07-29 13:45:43.684225	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.684225	\N	\N	\N
759e35f3-c04e-43d9-8c80-0471b99528f1	991f5653-f99b-47da-abd9-eac35d1851cd	Spectrum Assignment	1000000.00	Assignment of radio frequency spectrum	Exclusive use of assigned radio frequency spectrum	2025-07-29 13:45:43.698424	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.698424	\N	\N	\N
805727cb-a74e-432c-ac67-98011ebccbbf	991f5653-f99b-47da-abd9-eac35d1851cd	Spectrum Authorization	500000.00	Authorization for spectrum use in specific applications	Use of radio frequency spectrum for authorized applications	2025-07-29 13:45:43.706115	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.706115	\N	\N	\N
dfb54884-823f-41f8-b612-c0743be60345	991f5653-f99b-47da-abd9-eac35d1851cd	Temporary Spectrum Permit	50000.00	Temporary permit for spectrum use during events	Temporary use of radio frequency spectrum for specific events	2025-07-29 13:45:43.711613	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.711613	\N	\N	\N
\.


--
-- Data for Name: license_category_documents; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.license_category_documents (license_category_document_id, license_category_id, name, is_required, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
a6d4c0f5-5a7c-48f2-8808-c6c805a0b16f	********-337d-4d3d-92e9-b4a0a15bae13	Business Plan	t	2025-07-29 13:45:43.720659	2025-07-29 13:45:43.720659	\N	\N	\N
a16c46d4-1c5e-4e70-971a-a176fdd2c6e5	********-337d-4d3d-92e9-b4a0a15bae13	Project proposal	t	2025-07-29 13:45:43.728884	2025-07-29 13:45:43.728884	\N	\N	\N
5747a3f4-c81e-4e4e-96fe-076f024f6eb0	********-337d-4d3d-92e9-b4a0a15bae13	Stakeholder CVs	t	2025-07-29 13:45:43.733107	2025-07-29 13:45:43.733107	\N	\N	\N
73a81f9a-73ca-466e-87a5-e1f681b8df43	********-337d-4d3d-92e9-b4a0a15bae13	Market analysis and projections	t	2025-07-29 13:45:43.737155	2025-07-29 13:45:43.737155	\N	\N	\N
9d5eab04-0b43-4f91-903a-f32f88a6a45d	********-337d-4d3d-92e9-b4a0a15bae13	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:43.742674	2025-07-29 13:45:43.742674	\N	\N	\N
88794148-5734-458f-83dd-09e7d6bb514a	********-337d-4d3d-92e9-b4a0a15bae13	Tariff proposals	t	2025-07-29 13:45:43.748398	2025-07-29 13:45:43.748398	\N	\N	\N
32ad4349-68f8-41cc-996e-b3d8b8c965b2	********-337d-4d3d-92e9-b4a0a15bae13	Cash flow projections for 3 years	t	2025-07-29 13:45:43.752173	2025-07-29 13:45:43.752173	\N	\N	\N
e2194f09-0a09-4264-9006-f0f74cb24dbd	********-337d-4d3d-92e9-b4a0a15bae13	Experience in the provision of similar services	t	2025-07-29 13:45:43.756106	2025-07-29 13:45:43.756106	\N	\N	\N
8e92fade-c11a-489b-9490-d77ce69c1034	********-337d-4d3d-92e9-b4a0a15bae13	Business registration or incorporation certificate	t	2025-07-29 13:45:43.7627	2025-07-29 13:45:43.7627	\N	\N	\N
e11b19ff-c907-48b2-b925-6f2e50c7eb0d	********-337d-4d3d-92e9-b4a0a15bae13	Valid tax compliance certificate	t	2025-07-29 13:45:43.766283	2025-07-29 13:45:43.766283	\N	\N	\N
60780807-78c7-47c1-831d-cb93153f73cf	********-337d-4d3d-92e9-b4a0a15bae13	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:43.769312	2025-07-29 13:45:43.769312	\N	\N	\N
ef5a777e-32b8-4ba9-a4d6-6308c8f2566f	********-337d-4d3d-92e9-b4a0a15bae13	Proof of premises (lease/title deed)	t	2025-07-29 13:45:43.772883	2025-07-29 13:45:43.772883	\N	\N	\N
b05e6138-1e46-4248-93a0-78055814c093	********-337d-4d3d-92e9-b4a0a15bae13	Goods in transit insurance	t	2025-07-29 13:45:43.779266	2025-07-29 13:45:43.779266	\N	\N	\N
830b80cb-87aa-4453-8f6e-03b0c9722ff6	********-337d-4d3d-92e9-b4a0a15bae13	Inventory of fleet/equipment	t	2025-07-29 13:45:43.782345	2025-07-29 13:45:43.782345	\N	\N	\N
c108a3bd-f191-4bdb-9772-2909f837e887	********-337d-4d3d-92e9-b4a0a15bae13	Customer service policy	t	2025-07-29 13:45:43.785577	2025-07-29 13:45:43.785577	\N	\N	\N
8512be24-06f6-4d1f-a841-1fb70aff82ab	********-337d-4d3d-92e9-b4a0a15bae13	IT/tracking system description	t	2025-07-29 13:45:43.788732	2025-07-29 13:45:43.788732	\N	\N	\N
647ae553-ba4d-4fe5-acba-e6e13516bc5e	********-337d-4d3d-92e9-b4a0a15bae13	Three months of bank statements	t	2025-07-29 13:45:43.794284	2025-07-29 13:45:43.794284	\N	\N	\N
b21180c8-e88f-4e2c-87e2-ebbb44cbe057	********-337d-4d3d-92e9-b4a0a15bae13	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:43.798457	2025-07-29 13:45:43.798457	\N	\N	\N
0139fa9c-df98-454e-9526-d6b82bdb7d76	3b166600-9380-4303-b8d1-56a2f50bcb9e	Business Plan	t	2025-07-29 13:45:43.801186	2025-07-29 13:45:43.801186	\N	\N	\N
68ea2a84-55ea-4fe2-93ac-d4dd8d1582d3	3b166600-9380-4303-b8d1-56a2f50bcb9e	Project proposal	t	2025-07-29 13:45:43.804687	2025-07-29 13:45:43.804687	\N	\N	\N
d0f4cbf3-9375-4531-b313-0d9b565aba72	3b166600-9380-4303-b8d1-56a2f50bcb9e	Stakeholder CVs	t	2025-07-29 13:45:43.809072	2025-07-29 13:45:43.809072	\N	\N	\N
91962cdf-3960-4d22-b6c2-b46f99210e47	3b166600-9380-4303-b8d1-56a2f50bcb9e	Market analysis and projections	t	2025-07-29 13:45:43.813268	2025-07-29 13:45:43.813268	\N	\N	\N
dbe24795-fe0d-4573-a98b-1b8efb75601b	3b166600-9380-4303-b8d1-56a2f50bcb9e	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:43.816381	2025-07-29 13:45:43.816381	\N	\N	\N
af504a67-a5f5-4410-b39d-8a49b6968e58	3b166600-9380-4303-b8d1-56a2f50bcb9e	Tariff proposals	t	2025-07-29 13:45:43.819079	2025-07-29 13:45:43.819079	\N	\N	\N
23d5bed1-b2b0-453d-ac22-6699e02c4a5a	3b166600-9380-4303-b8d1-56a2f50bcb9e	Cash flow projections for 3 years	t	2025-07-29 13:45:43.821966	2025-07-29 13:45:43.821966	\N	\N	\N
50877d90-4543-471c-88ba-4fed982315bd	3b166600-9380-4303-b8d1-56a2f50bcb9e	Experience in the provision of similar services	t	2025-07-29 13:45:43.829001	2025-07-29 13:45:43.829001	\N	\N	\N
f3505f97-0133-499a-9161-a171609117f7	3b166600-9380-4303-b8d1-56a2f50bcb9e	Business registration or incorporation certificate	t	2025-07-29 13:45:43.832651	2025-07-29 13:45:43.832651	\N	\N	\N
dcb73e61-6624-4c2e-962c-143f28e1bc5b	3b166600-9380-4303-b8d1-56a2f50bcb9e	Valid tax compliance certificate	t	2025-07-29 13:45:43.835626	2025-07-29 13:45:43.835626	\N	\N	\N
b4c7bf5f-ed13-473f-a561-db75693ec36f	3b166600-9380-4303-b8d1-56a2f50bcb9e	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:43.838953	2025-07-29 13:45:43.838953	\N	\N	\N
7896ad5d-b5c1-493f-b3bd-baf4cf7e5fdd	3b166600-9380-4303-b8d1-56a2f50bcb9e	Proof of premises (lease/title deed)	t	2025-07-29 13:45:43.843256	2025-07-29 13:45:43.843256	\N	\N	\N
a5ca5cd5-a483-4e94-8ff0-fd5cf123d3cd	3b166600-9380-4303-b8d1-56a2f50bcb9e	Goods in transit insurance	t	2025-07-29 13:45:43.846723	2025-07-29 13:45:43.846723	\N	\N	\N
8c6b9775-5d89-43fc-bf0a-4dedeb6b6c02	3b166600-9380-4303-b8d1-56a2f50bcb9e	Inventory of fleet/equipment	t	2025-07-29 13:45:43.850156	2025-07-29 13:45:43.850156	\N	\N	\N
3c674568-789f-4fe8-87c9-729bce78f122	3b166600-9380-4303-b8d1-56a2f50bcb9e	Customer service policy	t	2025-07-29 13:45:43.853083	2025-07-29 13:45:43.853083	\N	\N	\N
4d976699-7a02-40d3-9260-3d7c10270b34	3b166600-9380-4303-b8d1-56a2f50bcb9e	IT/tracking system description	t	2025-07-29 13:45:43.855471	2025-07-29 13:45:43.855471	\N	\N	\N
d6fc9a69-a2e1-4085-b5d4-3b8cab3a1361	3b166600-9380-4303-b8d1-56a2f50bcb9e	Three months of bank statements	t	2025-07-29 13:45:43.860021	2025-07-29 13:45:43.860021	\N	\N	\N
fc7accef-34b1-41fd-a981-259e5013b0a7	3b166600-9380-4303-b8d1-56a2f50bcb9e	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:43.863692	2025-07-29 13:45:43.863692	\N	\N	\N
dbdd48b5-be4d-4dfa-bcd7-998554066c3e	98d3ecfa-2001-478b-bc44-62848dbb6e51	Business Plan	t	2025-07-29 13:45:43.867012	2025-07-29 13:45:43.867012	\N	\N	\N
9844605b-35e1-4e08-af16-01a1cb76bb15	98d3ecfa-2001-478b-bc44-62848dbb6e51	Project proposal	t	2025-07-29 13:45:43.870152	2025-07-29 13:45:43.870152	\N	\N	\N
dd9d1e7f-9b7a-4df2-8756-d32504577311	98d3ecfa-2001-478b-bc44-62848dbb6e51	Stakeholder CVs	t	2025-07-29 13:45:43.873391	2025-07-29 13:45:43.873391	\N	\N	\N
2750db2c-2ce5-40bd-80e9-4afe14fabb2a	98d3ecfa-2001-478b-bc44-62848dbb6e51	Market analysis and projections	t	2025-07-29 13:45:43.879753	2025-07-29 13:45:43.879753	\N	\N	\N
d788a96a-fd9d-456a-a436-8273b273ba1b	98d3ecfa-2001-478b-bc44-62848dbb6e51	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:43.883421	2025-07-29 13:45:43.883421	\N	\N	\N
fee9ec82-1644-4b8f-9c3e-ccb1f928b498	98d3ecfa-2001-478b-bc44-62848dbb6e51	Tariff proposals	t	2025-07-29 13:45:43.886514	2025-07-29 13:45:43.886514	\N	\N	\N
0c9c058f-cd3e-43a6-b82e-e52595b247be	98d3ecfa-2001-478b-bc44-62848dbb6e51	Cash flow projections for 3 years	t	2025-07-29 13:45:43.889209	2025-07-29 13:45:43.889209	\N	\N	\N
a81127e7-bc31-4896-a22e-dbb6f0f277ba	98d3ecfa-2001-478b-bc44-62848dbb6e51	Experience in the provision of similar services	t	2025-07-29 13:45:43.894288	2025-07-29 13:45:43.894288	\N	\N	\N
dca4a5c4-a4be-4579-aa66-cf9916128a5e	98d3ecfa-2001-478b-bc44-62848dbb6e51	Business registration or incorporation certificate	t	2025-07-29 13:45:43.898014	2025-07-29 13:45:43.898014	\N	\N	\N
dfb10f65-8214-4d95-920c-f204ecb68415	98d3ecfa-2001-478b-bc44-62848dbb6e51	Valid tax compliance certificate	t	2025-07-29 13:45:43.901571	2025-07-29 13:45:43.901571	\N	\N	\N
5e20f6dc-21f0-4e22-a202-faba4964bd0d	98d3ecfa-2001-478b-bc44-62848dbb6e51	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:43.904831	2025-07-29 13:45:43.904831	\N	\N	\N
c2795191-a163-4797-abc4-f4e8ff94f338	98d3ecfa-2001-478b-bc44-62848dbb6e51	Proof of premises (lease/title deed)	t	2025-07-29 13:45:43.908516	2025-07-29 13:45:43.908516	\N	\N	\N
91643f96-3a41-4269-bea0-fddb7bee6906	98d3ecfa-2001-478b-bc44-62848dbb6e51	Goods in transit insurance	t	2025-07-29 13:45:43.912203	2025-07-29 13:45:43.912203	\N	\N	\N
b3858c5d-dcec-4948-a7cf-3914e38daa4f	98d3ecfa-2001-478b-bc44-62848dbb6e51	Inventory of fleet/equipment	t	2025-07-29 13:45:43.915667	2025-07-29 13:45:43.915667	\N	\N	\N
5b342a05-dc5b-4164-b16a-c15ac4cabfa3	98d3ecfa-2001-478b-bc44-62848dbb6e51	Customer service policy	t	2025-07-29 13:45:43.919799	2025-07-29 13:45:43.919799	\N	\N	\N
0a1c71a5-ee11-4986-9b04-3cca27e1a0a8	98d3ecfa-2001-478b-bc44-62848dbb6e51	IT/tracking system description	t	2025-07-29 13:45:43.922909	2025-07-29 13:45:43.922909	\N	\N	\N
84b300a0-dd9e-41c5-a989-48d319b30841	98d3ecfa-2001-478b-bc44-62848dbb6e51	Three months of bank statements	t	2025-07-29 13:45:43.928123	2025-07-29 13:45:43.928123	\N	\N	\N
********-6693-4a17-b3e9-0b6b3d13fd20	98d3ecfa-2001-478b-bc44-62848dbb6e51	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:43.931988	2025-07-29 13:45:43.931988	\N	\N	\N
95b05a62-396f-4146-8c52-f8f2b7a9ee94	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Business Plan	t	2025-07-29 13:45:43.934775	2025-07-29 13:45:43.934775	\N	\N	\N
01d012cd-0696-4700-ac1d-398fba78f6c2	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Project proposal	t	2025-07-29 13:45:43.938778	2025-07-29 13:45:43.938778	\N	\N	\N
00cd54c7-c68f-4d7c-add5-82391f9df56c	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Stakeholder CVs	t	2025-07-29 13:45:43.942928	2025-07-29 13:45:43.942928	\N	\N	\N
8805f858-2cd8-4829-8f0e-48d1513ed874	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Market analysis and projections	t	2025-07-29 13:45:43.947724	2025-07-29 13:45:43.947724	\N	\N	\N
e77debd6-4704-48a7-9615-390acaa2fde4	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:43.952573	2025-07-29 13:45:43.952573	\N	\N	\N
7927f67a-7d4f-4f4b-ae87-bdf79afb03f0	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Tariff proposals	t	2025-07-29 13:45:43.955984	2025-07-29 13:45:43.955984	\N	\N	\N
0889ff3a-6599-489c-aa3d-15dd2478ea13	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Cash flow projections for 3 years	t	2025-07-29 13:45:43.959828	2025-07-29 13:45:43.959828	\N	\N	\N
bce4997f-567f-4b46-9456-e8a8b1e6b89e	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Experience in the provision of similar services	t	2025-07-29 13:45:43.96395	2025-07-29 13:45:43.96395	\N	\N	\N
7fb30adc-69ad-47e5-8fd1-78031491b3fb	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Business registration or incorporation certificate	t	2025-07-29 13:45:43.966786	2025-07-29 13:45:43.966786	\N	\N	\N
928fc95b-16c5-4fed-99de-d81e6afefa1f	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Valid tax compliance certificate	t	2025-07-29 13:45:43.970594	2025-07-29 13:45:43.970594	\N	\N	\N
391e6e08-3942-483c-aa2b-5f7b3a0b2a15	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:43.973687	2025-07-29 13:45:43.973687	\N	\N	\N
175e3753-d381-4c7e-aad4-92cceb7b1178	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Proof of premises (lease/title deed)	t	2025-07-29 13:45:43.978961	2025-07-29 13:45:43.978961	\N	\N	\N
5571d22f-093e-40bb-b376-3c774f707c5a	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Goods in transit insurance	t	2025-07-29 13:45:43.982775	2025-07-29 13:45:43.982775	\N	\N	\N
516f5a50-c5ed-42fe-8eba-c1c8b45c6f82	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Inventory of fleet/equipment	t	2025-07-29 13:45:43.987053	2025-07-29 13:45:43.987053	\N	\N	\N
78c8ff5c-9933-4c9b-adce-11e6d085dfdf	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Customer service policy	t	2025-07-29 13:45:43.990079	2025-07-29 13:45:43.990079	\N	\N	\N
f8446853-d3f0-4aac-88ff-25c2fbb01d45	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	IT/tracking system description	t	2025-07-29 13:45:43.996429	2025-07-29 13:45:43.996429	\N	\N	\N
41f87629-8e09-4030-a77c-eb05eacf5d88	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Three months of bank statements	t	2025-07-29 13:45:44.003042	2025-07-29 13:45:44.003042	\N	\N	\N
5ea263d8-fd1e-44b4-a536-1a46ab49c95c	9b01139b-3751-4e7d-aedb-eff45ad2d6bd	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.006533	2025-07-29 13:45:44.006533	\N	\N	\N
8403fb2b-8a78-478f-bcc9-495ea44c0ae5	2a93c99e-7271-4617-9c7b-ad287e982def	Business Plan	t	2025-07-29 13:45:44.013092	2025-07-29 13:45:44.013092	\N	\N	\N
bda79bf8-55e1-4725-826b-7b1d3d515813	2a93c99e-7271-4617-9c7b-ad287e982def	Project proposal	t	2025-07-29 13:45:44.016463	2025-07-29 13:45:44.016463	\N	\N	\N
93a8bb5e-e483-4a4a-bb6e-156f8e05d4db	2a93c99e-7271-4617-9c7b-ad287e982def	Stakeholder CVs	t	2025-07-29 13:45:44.019603	2025-07-29 13:45:44.019603	\N	\N	\N
b8e9eb3c-4a28-45ac-9248-96a1147b8bf9	2a93c99e-7271-4617-9c7b-ad287e982def	Market analysis and projections	t	2025-07-29 13:45:44.022519	2025-07-29 13:45:44.022519	\N	\N	\N
6d36e7dd-edbc-4f69-ad5a-d5485d7187b4	2a93c99e-7271-4617-9c7b-ad287e982def	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.026911	2025-07-29 13:45:44.026911	\N	\N	\N
56436262-c4f8-419b-8f45-9b68c186a56a	2a93c99e-7271-4617-9c7b-ad287e982def	Tariff proposals	t	2025-07-29 13:45:44.032168	2025-07-29 13:45:44.032168	\N	\N	\N
dba269a1-ac54-4e6b-a687-be17eb47412b	2a93c99e-7271-4617-9c7b-ad287e982def	Cash flow projections for 3 years	t	2025-07-29 13:45:44.035089	2025-07-29 13:45:44.035089	\N	\N	\N
a7bdaa3b-aa09-4988-a8f0-dff7a8398156	2a93c99e-7271-4617-9c7b-ad287e982def	Experience in the provision of similar services	t	2025-07-29 13:45:44.038239	2025-07-29 13:45:44.038239	\N	\N	\N
d28d5956-fb2f-433d-88c7-cbfa34ae8e33	2a93c99e-7271-4617-9c7b-ad287e982def	Business registration or incorporation certificate	t	2025-07-29 13:45:44.0409	2025-07-29 13:45:44.0409	\N	\N	\N
a86bbb48-9795-44ec-858d-cf7c8a350515	2a93c99e-7271-4617-9c7b-ad287e982def	Valid tax compliance certificate	t	2025-07-29 13:45:44.046349	2025-07-29 13:45:44.046349	\N	\N	\N
70180444-59e5-4679-8f90-a6e1d4009927	2a93c99e-7271-4617-9c7b-ad287e982def	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.052225	2025-07-29 13:45:44.052225	\N	\N	\N
f6cca6c1-0921-4136-92d6-59eaff0e6a83	2a93c99e-7271-4617-9c7b-ad287e982def	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.056021	2025-07-29 13:45:44.056021	\N	\N	\N
393954a8-4a6e-4c7f-911a-e7cf9ff6f315	2a93c99e-7271-4617-9c7b-ad287e982def	Goods in transit insurance	t	2025-07-29 13:45:44.060984	2025-07-29 13:45:44.060984	\N	\N	\N
a173c9db-e84d-4b7d-8440-355cd9429766	2a93c99e-7271-4617-9c7b-ad287e982def	Inventory of fleet/equipment	t	2025-07-29 13:45:44.065371	2025-07-29 13:45:44.065371	\N	\N	\N
1f9e18d1-f18f-4aa0-aced-c5dc9746aaaa	2a93c99e-7271-4617-9c7b-ad287e982def	Customer service policy	t	2025-07-29 13:45:44.069172	2025-07-29 13:45:44.069172	\N	\N	\N
596f701a-0b44-4a50-9904-182f144dcb60	2a93c99e-7271-4617-9c7b-ad287e982def	IT/tracking system description	t	2025-07-29 13:45:44.072132	2025-07-29 13:45:44.072132	\N	\N	\N
0042d971-96f2-49a8-b333-9f7ee38ef9a3	2a93c99e-7271-4617-9c7b-ad287e982def	Three months of bank statements	t	2025-07-29 13:45:44.076263	2025-07-29 13:45:44.076263	\N	\N	\N
050ea473-f191-447e-83a8-9592afc0960b	2a93c99e-7271-4617-9c7b-ad287e982def	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.080619	2025-07-29 13:45:44.080619	\N	\N	\N
95c3bbb4-76f5-4ac5-9de0-2a2dcc513932	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Business Plan	t	2025-07-29 13:45:44.084251	2025-07-29 13:45:44.084251	\N	\N	\N
f60ee838-f167-4526-8d10-982bc0d4deb7	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Project proposal	t	2025-07-29 13:45:44.087253	2025-07-29 13:45:44.087253	\N	\N	\N
0b3f55b5-0d9b-4b05-99f6-2c76297f4f54	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Stakeholder CVs	t	2025-07-29 13:45:44.091167	2025-07-29 13:45:44.091167	\N	\N	\N
c6146faa-e268-4b0d-9b53-e38f77f11d87	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Market analysis and projections	t	2025-07-29 13:45:44.097027	2025-07-29 13:45:44.097027	\N	\N	\N
eb2d7d92-dfa1-42f7-a8b2-fbc7179813b3	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.100035	2025-07-29 13:45:44.100035	\N	\N	\N
744aaa1c-ba9a-4910-8923-4471d65821c6	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Tariff proposals	t	2025-07-29 13:45:44.103357	2025-07-29 13:45:44.103357	\N	\N	\N
b6c07656-065b-439b-aaa3-1f1e5f1eac83	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Cash flow projections for 3 years	t	2025-07-29 13:45:44.106622	2025-07-29 13:45:44.106622	\N	\N	\N
e40ca745-a451-4141-8283-412725ef3ad4	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Experience in the provision of similar services	t	2025-07-29 13:45:44.111096	2025-07-29 13:45:44.111096	\N	\N	\N
6df8a6be-5428-4f67-af93-cbe6a87b12c7	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Business registration or incorporation certificate	t	2025-07-29 13:45:44.11449	2025-07-29 13:45:44.11449	\N	\N	\N
c57bb5c2-d6a6-450d-9020-92a674f27efa	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Valid tax compliance certificate	t	2025-07-29 13:45:44.118058	2025-07-29 13:45:44.118058	\N	\N	\N
227e8da3-b751-4188-b631-6f263f62c4c2	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.121297	2025-07-29 13:45:44.121297	\N	\N	\N
2a48c669-ff8b-493f-8ccf-61c4dc4c3640	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.123943	2025-07-29 13:45:44.123943	\N	\N	\N
58c603e2-4539-45f9-86be-9f8ec05021c0	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Goods in transit insurance	t	2025-07-29 13:45:44.129529	2025-07-29 13:45:44.129529	\N	\N	\N
50a182f5-2090-4aad-9098-d5af2ef27ccc	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Inventory of fleet/equipment	t	2025-07-29 13:45:44.137514	2025-07-29 13:45:44.137514	\N	\N	\N
238e005d-40a1-4183-893e-0ed37d1c06ab	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Customer service policy	t	2025-07-29 13:45:44.14068	2025-07-29 13:45:44.14068	\N	\N	\N
3766a793-d4db-4a05-9ffc-bcd35d47142c	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	IT/tracking system description	t	2025-07-29 13:45:44.146684	2025-07-29 13:45:44.146684	\N	\N	\N
8c6eae37-ab67-4f59-8f90-af55906a1be1	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Three months of bank statements	t	2025-07-29 13:45:44.149917	2025-07-29 13:45:44.149917	\N	\N	\N
********-4e49-453f-9d30-d9393db30786	8ece49a8-82dc-48ad-b5c8-97a11c2ca4f4	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.152953	2025-07-29 13:45:44.152953	\N	\N	\N
9ffc2252-1d2e-4ad1-bc79-0969ab0d293d	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Business Plan	t	2025-07-29 13:45:44.156488	2025-07-29 13:45:44.156488	\N	\N	\N
66eb0d43-be3a-4136-9c6e-c5e1f2188065	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Project proposal	t	2025-07-29 13:45:44.161638	2025-07-29 13:45:44.161638	\N	\N	\N
1217ff6f-7a0f-412b-adb8-0edc8343da00	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Stakeholder CVs	t	2025-07-29 13:45:44.166198	2025-07-29 13:45:44.166198	\N	\N	\N
ccd7e60a-395f-456f-8b5d-2642a594bdf6	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Market analysis and projections	t	2025-07-29 13:45:44.169812	2025-07-29 13:45:44.169812	\N	\N	\N
94438ad1-9c18-48c1-b3b1-60272cb48e60	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.172818	2025-07-29 13:45:44.172818	\N	\N	\N
a132a22e-5882-4960-852b-deb75287bbda	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Tariff proposals	t	2025-07-29 13:45:44.177787	2025-07-29 13:45:44.177787	\N	\N	\N
f8894d9c-a86a-406b-bf56-f86bdcd2af6f	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Cash flow projections for 3 years	t	2025-07-29 13:45:44.186474	2025-07-29 13:45:44.186474	\N	\N	\N
6022f957-b15c-4d4d-a9d6-1a63b68f3d41	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Experience in the provision of similar services	t	2025-07-29 13:45:44.189297	2025-07-29 13:45:44.189297	\N	\N	\N
dd680ab4-182e-45c7-afe1-bb88805f41be	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Business registration or incorporation certificate	t	2025-07-29 13:45:44.193636	2025-07-29 13:45:44.193636	\N	\N	\N
fc13b8d1-14cd-4a64-9e36-a7541ab840ea	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Valid tax compliance certificate	t	2025-07-29 13:45:44.198542	2025-07-29 13:45:44.198542	\N	\N	\N
8c6cc8f2-a1ae-4960-8f7a-d600531c8064	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.20219	2025-07-29 13:45:44.20219	\N	\N	\N
c1859070-abe6-4a7c-bed3-5a796b065949	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.205458	2025-07-29 13:45:44.205458	\N	\N	\N
de06bc16-5ce0-4791-9df6-9dcc5501d458	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Goods in transit insurance	t	2025-07-29 13:45:44.209704	2025-07-29 13:45:44.209704	\N	\N	\N
af47e628-7293-422e-bafd-7f6c680a76f6	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Inventory of fleet/equipment	t	2025-07-29 13:45:44.213836	2025-07-29 13:45:44.213836	\N	\N	\N
32282d46-ed27-4b1f-b67b-4dfebeebc48a	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Customer service policy	t	2025-07-29 13:45:44.219094	2025-07-29 13:45:44.219094	\N	\N	\N
b5bcf275-933a-4750-973b-9ca83734b81a	06c9f1cf-da5f-4537-be91-22c61f0c40b0	IT/tracking system description	t	2025-07-29 13:45:44.223403	2025-07-29 13:45:44.223403	\N	\N	\N
9481b1c3-d71a-4266-a171-78e3a8e126d7	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Three months of bank statements	t	2025-07-29 13:45:44.228197	2025-07-29 13:45:44.228197	\N	\N	\N
7d569a44-7342-4fef-baf2-8dfc8b4569cf	06c9f1cf-da5f-4537-be91-22c61f0c40b0	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.233104	2025-07-29 13:45:44.233104	\N	\N	\N
3e655d47-64d1-412d-b5ac-ebc31896c476	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Business Plan	t	2025-07-29 13:45:44.236083	2025-07-29 13:45:44.236083	\N	\N	\N
659499fd-3ee8-468f-85a5-90a9226e9188	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Project proposal	t	2025-07-29 13:45:44.23883	2025-07-29 13:45:44.23883	\N	\N	\N
33d7ceed-f64d-4070-83c6-87ea4bec75ce	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Stakeholder CVs	t	2025-07-29 13:45:44.24325	2025-07-29 13:45:44.24325	\N	\N	\N
4c2160ed-1ca0-4be5-9f84-e02f1d6cc244	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Market analysis and projections	t	2025-07-29 13:45:44.248663	2025-07-29 13:45:44.248663	\N	\N	\N
082c3cee-f377-45f8-a0ff-bce05c8511bc	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.251571	2025-07-29 13:45:44.251571	\N	\N	\N
de2e4486-1026-4293-9388-21308a1c13f5	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Tariff proposals	t	2025-07-29 13:45:44.25493	2025-07-29 13:45:44.25493	\N	\N	\N
1f071610-c6d7-4c79-960b-3195ff50b6ab	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Cash flow projections for 3 years	t	2025-07-29 13:45:44.258321	2025-07-29 13:45:44.258321	\N	\N	\N
f2c0d001-e592-4f92-b97b-d6dfcc58db4a	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Experience in the provision of similar services	t	2025-07-29 13:45:44.266524	2025-07-29 13:45:44.266524	\N	\N	\N
4b34509d-c439-4c85-8a40-de1a87789338	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Business registration or incorporation certificate	t	2025-07-29 13:45:44.270735	2025-07-29 13:45:44.270735	\N	\N	\N
bf74147d-152d-4541-b394-44784f15343d	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Valid tax compliance certificate	t	2025-07-29 13:45:44.273801	2025-07-29 13:45:44.273801	\N	\N	\N
51bbab7c-f176-4eea-a01c-cc994a0df0ee	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.279791	2025-07-29 13:45:44.279791	\N	\N	\N
e2e11d1c-1b6c-4a7c-bb2d-132944a04b66	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.284022	2025-07-29 13:45:44.284022	\N	\N	\N
6500abe8-7cca-4b5d-a3f1-20858047fcf4	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Goods in transit insurance	t	2025-07-29 13:45:44.288536	2025-07-29 13:45:44.288536	\N	\N	\N
e06401e7-d3a7-433b-b2a6-ae56d67fcf9c	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Inventory of fleet/equipment	t	2025-07-29 13:45:44.291749	2025-07-29 13:45:44.291749	\N	\N	\N
936ef8c1-2538-48c6-abce-485591091bea	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Customer service policy	t	2025-07-29 13:45:44.298463	2025-07-29 13:45:44.298463	\N	\N	\N
459c0b17-d48b-4890-bf88-f60fa9fd2cd1	99c88fcd-8a69-424c-b40e-7cc141b1e75c	IT/tracking system description	t	2025-07-29 13:45:44.302138	2025-07-29 13:45:44.302138	\N	\N	\N
e0f04023-811a-40f4-831a-54aec77790b0	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Three months of bank statements	t	2025-07-29 13:45:44.305324	2025-07-29 13:45:44.305324	\N	\N	\N
d991c800-74a1-4f07-b59d-048edefc7072	99c88fcd-8a69-424c-b40e-7cc141b1e75c	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.31079	2025-07-29 13:45:44.31079	\N	\N	\N
3625dbb9-ca22-46d4-9d4b-06bafe4e8d66	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Business Plan	t	2025-07-29 13:45:44.315467	2025-07-29 13:45:44.315467	\N	\N	\N
4635ce83-5da7-4a8d-912e-6bef324a1a91	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Project proposal	t	2025-07-29 13:45:44.318922	2025-07-29 13:45:44.318922	\N	\N	\N
3163c190-6496-49d7-9df7-42d67c1448ab	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Stakeholder CVs	t	2025-07-29 13:45:44.321699	2025-07-29 13:45:44.321699	\N	\N	\N
********-2209-4611-a007-9ebd5d887c3a	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Market analysis and projections	t	2025-07-29 13:45:44.324672	2025-07-29 13:45:44.324672	\N	\N	\N
067095d8-bfd5-47a8-a80e-fe447d6a0209	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.332203	2025-07-29 13:45:44.332203	\N	\N	\N
9c5d501b-261c-41d1-9f66-38c88949e134	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Tariff proposals	t	2025-07-29 13:45:44.335851	2025-07-29 13:45:44.335851	\N	\N	\N
a12adf60-3f41-483c-a7bb-9365cf636257	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Cash flow projections for 3 years	t	2025-07-29 13:45:44.33903	2025-07-29 13:45:44.33903	\N	\N	\N
f7ff9d7c-718a-490c-a1aa-d2fa510fca87	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Experience in the provision of similar services	t	2025-07-29 13:45:44.342011	2025-07-29 13:45:44.342011	\N	\N	\N
518c0a4e-1131-4e66-99ac-86dcf218c446	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Business registration or incorporation certificate	t	2025-07-29 13:45:44.348421	2025-07-29 13:45:44.348421	\N	\N	\N
581772fd-33b0-438a-9089-a9fa8440d197	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Valid tax compliance certificate	t	2025-07-29 13:45:44.351649	2025-07-29 13:45:44.351649	\N	\N	\N
70285d23-bed2-4c0e-b420-4a580c936d29	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.354344	2025-07-29 13:45:44.354344	\N	\N	\N
c51b903b-c732-4da8-a9a3-23493d2653a4	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.357651	2025-07-29 13:45:44.357651	\N	\N	\N
81c33a93-ea1d-4828-a7f6-388c242e0c02	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Goods in transit insurance	t	2025-07-29 13:45:44.362402	2025-07-29 13:45:44.362402	\N	\N	\N
bf733a67-0b25-4326-a3dd-f3f6d34942b8	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Inventory of fleet/equipment	t	2025-07-29 13:45:44.366459	2025-07-29 13:45:44.366459	\N	\N	\N
818c03cf-0b95-4e69-98d9-c5167d7f328c	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Customer service policy	t	2025-07-29 13:45:44.369453	2025-07-29 13:45:44.369453	\N	\N	\N
dc5d463a-a902-42ce-85a8-8844e92360f1	f8e211a7-9f08-4b68-b7dc-2706b58383b4	IT/tracking system description	t	2025-07-29 13:45:44.373116	2025-07-29 13:45:44.373116	\N	\N	\N
683886cb-af04-40d2-9281-f8c49c61469d	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Three months of bank statements	t	2025-07-29 13:45:44.377241	2025-07-29 13:45:44.377241	\N	\N	\N
811c33d0-fbdd-408a-82f2-debeb033fed3	f8e211a7-9f08-4b68-b7dc-2706b58383b4	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.382109	2025-07-29 13:45:44.382109	\N	\N	\N
5fb0026d-9a28-4697-80c5-90d4e0172233	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Business Plan	t	2025-07-29 13:45:44.385064	2025-07-29 13:45:44.385064	\N	\N	\N
5cc1d364-037e-471e-966e-94337c0397b5	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Project proposal	t	2025-07-29 13:45:44.388005	2025-07-29 13:45:44.388005	\N	\N	\N
669034b8-2ba1-4929-900e-3a7f2eb96da3	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Stakeholder CVs	t	2025-07-29 13:45:44.391249	2025-07-29 13:45:44.391249	\N	\N	\N
aff809d1-cda9-4f3f-8071-60de2f3c7382	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Market analysis and projections	t	2025-07-29 13:45:44.397345	2025-07-29 13:45:44.397345	\N	\N	\N
a2285dfb-129e-4d21-a7c6-45fb74578129	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.403648	2025-07-29 13:45:44.403648	\N	\N	\N
d88baa51-5d79-4ec2-a370-079bb6b5f708	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Tariff proposals	t	2025-07-29 13:45:44.407375	2025-07-29 13:45:44.407375	\N	\N	\N
e4dfe2d4-234f-4665-8d3d-22a3ae150a46	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Cash flow projections for 3 years	t	2025-07-29 13:45:44.412408	2025-07-29 13:45:44.412408	\N	\N	\N
5baac40f-a148-45af-868d-752c60e87863	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Experience in the provision of similar services	t	2025-07-29 13:45:44.416709	2025-07-29 13:45:44.416709	\N	\N	\N
739d0e7a-cd28-4932-ae45-e42ae4b5336e	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Business registration or incorporation certificate	t	2025-07-29 13:45:44.42065	2025-07-29 13:45:44.42065	\N	\N	\N
84defff0-c0a8-4951-a5c3-44d11e171d23	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Valid tax compliance certificate	t	2025-07-29 13:45:44.424211	2025-07-29 13:45:44.424211	\N	\N	\N
f57a13eb-e3e4-40f3-b1f3-d4b8d46c7e04	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.429172	2025-07-29 13:45:44.429172	\N	\N	\N
a921994d-c9a3-44d9-a819-337675feaa9d	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.433923	2025-07-29 13:45:44.433923	\N	\N	\N
52b0d0ee-655c-433b-bdb7-bcb794e557ce	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Goods in transit insurance	t	2025-07-29 13:45:44.438072	2025-07-29 13:45:44.438072	\N	\N	\N
b68463bb-ebfa-4d4a-9cab-f368879fb667	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Inventory of fleet/equipment	t	2025-07-29 13:45:44.441064	2025-07-29 13:45:44.441064	\N	\N	\N
c40df0a1-9f0d-4652-b2db-0287e131acae	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Customer service policy	t	2025-07-29 13:45:44.447196	2025-07-29 13:45:44.447196	\N	\N	\N
d2d51089-7912-493b-ac90-516cae337d46	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	IT/tracking system description	t	2025-07-29 13:45:44.450807	2025-07-29 13:45:44.450807	\N	\N	\N
6c45aef2-de92-4fa2-965b-96a2d7a2f1ac	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Three months of bank statements	t	2025-07-29 13:45:44.453999	2025-07-29 13:45:44.453999	\N	\N	\N
a29c6a8a-50c8-4725-8f9b-ea2af5d220ee	ce10d73e-9a76-48fb-8c87-8f0ac2c8fb70	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.45721	2025-07-29 13:45:44.45721	\N	\N	\N
87432f94-3c80-4331-a5ad-a318f3ccb29c	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Business Plan	t	2025-07-29 13:45:44.461994	2025-07-29 13:45:44.461994	\N	\N	\N
83320a09-aca8-482d-b45f-1b3d094df787	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Project proposal	t	2025-07-29 13:45:44.466552	2025-07-29 13:45:44.466552	\N	\N	\N
f84a7868-9f2e-4fa0-811c-6b12ff42a97b	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Stakeholder CVs	t	2025-07-29 13:45:44.470177	2025-07-29 13:45:44.470177	\N	\N	\N
c1c991d5-b0cc-4408-9598-d7b708413709	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Market analysis and projections	t	2025-07-29 13:45:44.473745	2025-07-29 13:45:44.473745	\N	\N	\N
2f4761d5-f1a2-4143-8528-4a98041ef0c2	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.477373	2025-07-29 13:45:44.477373	\N	\N	\N
c4c7ebed-2f68-4027-8fe1-cbda88092bfa	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Tariff proposals	t	2025-07-29 13:45:44.482407	2025-07-29 13:45:44.482407	\N	\N	\N
d7ce0b8a-ab0f-47fe-aba7-8c0621ea1279	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Cash flow projections for 3 years	t	2025-07-29 13:45:44.485375	2025-07-29 13:45:44.485375	\N	\N	\N
95a80031-a3d6-4c3e-ab47-7b58912a6c41	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Experience in the provision of similar services	t	2025-07-29 13:45:44.489167	2025-07-29 13:45:44.489167	\N	\N	\N
5a447484-99a2-45c4-9689-852ee2020b3a	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Business registration or incorporation certificate	t	2025-07-29 13:45:44.493682	2025-07-29 13:45:44.493682	\N	\N	\N
f23e2578-df2f-4047-a0e5-8aa2ac1218fb	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Valid tax compliance certificate	t	2025-07-29 13:45:44.499525	2025-07-29 13:45:44.499525	\N	\N	\N
a2bd3c58-9e8d-44b9-9f63-c9d3c8638826	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.50272	2025-07-29 13:45:44.50272	\N	\N	\N
6514f0b3-4507-48de-8863-384f434f14b2	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.506256	2025-07-29 13:45:44.506256	\N	\N	\N
60990507-8c17-43fe-9033-4980933974b3	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Goods in transit insurance	t	2025-07-29 13:45:44.509442	2025-07-29 13:45:44.509442	\N	\N	\N
3cd2a991-0ae8-4322-aa53-9c75b7814220	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Inventory of fleet/equipment	t	2025-07-29 13:45:44.518274	2025-07-29 13:45:44.518274	\N	\N	\N
dc2edd45-e139-45d2-a91d-772346d28965	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Customer service policy	t	2025-07-29 13:45:44.522977	2025-07-29 13:45:44.522977	\N	\N	\N
cd94c1ad-1810-4b32-80fe-68b3b3944588	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	IT/tracking system description	t	2025-07-29 13:45:44.527546	2025-07-29 13:45:44.527546	\N	\N	\N
8534f3a7-1428-4060-b57b-495eed8f5d80	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Three months of bank statements	t	2025-07-29 13:45:44.533926	2025-07-29 13:45:44.533926	\N	\N	\N
eff4dc8f-8ee8-4e65-9d40-c91e8206f615	ec9b1045-fd23-49f7-86b9-cea0cb5372f1	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.537856	2025-07-29 13:45:44.537856	\N	\N	\N
8f1e51a4-080e-4fbe-8b57-93848deee75a	7fa90d64-7965-4552-aa73-09474d35fe56	Business Plan	t	2025-07-29 13:45:44.542471	2025-07-29 13:45:44.542471	\N	\N	\N
316fd2f7-b8b6-473e-a994-c35952f7050a	7fa90d64-7965-4552-aa73-09474d35fe56	Project proposal	t	2025-07-29 13:45:44.548918	2025-07-29 13:45:44.548918	\N	\N	\N
657c89e4-3294-4c6f-af2f-fe4939574929	7fa90d64-7965-4552-aa73-09474d35fe56	Stakeholder CVs	t	2025-07-29 13:45:44.553205	2025-07-29 13:45:44.553205	\N	\N	\N
3ef73441-99a9-4afd-84ad-2a9f54e8da9d	7fa90d64-7965-4552-aa73-09474d35fe56	Market analysis and projections	t	2025-07-29 13:45:44.557159	2025-07-29 13:45:44.557159	\N	\N	\N
546c0821-9c3d-49a2-91bf-236eb68a91a2	7fa90d64-7965-4552-aa73-09474d35fe56	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.561886	2025-07-29 13:45:44.561886	\N	\N	\N
17152a40-ba62-4155-aaac-2fa3a45e7fd2	7fa90d64-7965-4552-aa73-09474d35fe56	Tariff proposals	t	2025-07-29 13:45:44.565629	2025-07-29 13:45:44.565629	\N	\N	\N
1d95a591-fa9e-450f-b96c-5e286c0765ae	7fa90d64-7965-4552-aa73-09474d35fe56	Cash flow projections for 3 years	t	2025-07-29 13:45:44.569053	2025-07-29 13:45:44.569053	\N	\N	\N
e589370a-c674-42cb-99b9-6dc881fb7278	7fa90d64-7965-4552-aa73-09474d35fe56	Experience in the provision of similar services	t	2025-07-29 13:45:44.573276	2025-07-29 13:45:44.573276	\N	\N	\N
d88682d4-c0ca-4974-81ea-ddc03c60d38e	7fa90d64-7965-4552-aa73-09474d35fe56	Business registration or incorporation certificate	t	2025-07-29 13:45:44.578766	2025-07-29 13:45:44.578766	\N	\N	\N
13825f35-b378-4885-a337-497136985187	7fa90d64-7965-4552-aa73-09474d35fe56	Valid tax compliance certificate	t	2025-07-29 13:45:44.582288	2025-07-29 13:45:44.582288	\N	\N	\N
1eaaae8b-b743-4ba4-8078-53ea6993aea0	7fa90d64-7965-4552-aa73-09474d35fe56	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.585378	2025-07-29 13:45:44.585378	\N	\N	\N
c2e9cb94-bc3c-4a04-b1e2-c52209425ddb	7fa90d64-7965-4552-aa73-09474d35fe56	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.588735	2025-07-29 13:45:44.588735	\N	\N	\N
45456dc4-14e1-45be-a8dc-97000e0f91b0	7fa90d64-7965-4552-aa73-09474d35fe56	Goods in transit insurance	t	2025-07-29 13:45:44.59222	2025-07-29 13:45:44.59222	\N	\N	\N
49ce3d1d-c3e7-4616-be92-4c822d3e2148	7fa90d64-7965-4552-aa73-09474d35fe56	Inventory of fleet/equipment	t	2025-07-29 13:45:44.5977	2025-07-29 13:45:44.5977	\N	\N	\N
7a3ce2fe-8a85-4330-a6c8-d764ad7e53fc	7fa90d64-7965-4552-aa73-09474d35fe56	Customer service policy	t	2025-07-29 13:45:44.601674	2025-07-29 13:45:44.601674	\N	\N	\N
e0f40d74-72b5-44f1-9799-93684ccc58c2	7fa90d64-7965-4552-aa73-09474d35fe56	IT/tracking system description	t	2025-07-29 13:45:44.605849	2025-07-29 13:45:44.605849	\N	\N	\N
59a2ce67-e4b0-4ec2-a87d-4cc36521ae87	7fa90d64-7965-4552-aa73-09474d35fe56	Three months of bank statements	t	2025-07-29 13:45:44.609352	2025-07-29 13:45:44.609352	\N	\N	\N
96f63907-da24-4ded-96df-5aa02c93ee02	7fa90d64-7965-4552-aa73-09474d35fe56	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.614212	2025-07-29 13:45:44.614212	\N	\N	\N
5e61fe9c-79c3-44e8-9de0-e66331ec74a0	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Business Plan	t	2025-07-29 13:45:44.618255	2025-07-29 13:45:44.618255	\N	\N	\N
040145a5-dcd7-44bf-9cf6-a8f27255a81f	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Project proposal	t	2025-07-29 13:45:44.621713	2025-07-29 13:45:44.621713	\N	\N	\N
04684e64-21b1-4d87-bee9-e683c9e65535	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Stakeholder CVs	t	2025-07-29 13:45:44.624731	2025-07-29 13:45:44.624731	\N	\N	\N
********-08c3-45bf-8bc3-79d31ba9d704	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Market analysis and projections	t	2025-07-29 13:45:44.629268	2025-07-29 13:45:44.629268	\N	\N	\N
200b2679-4756-413b-94a4-f42b0a7a7ac6	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.633272	2025-07-29 13:45:44.633272	\N	\N	\N
9f9b2201-2324-462f-add5-965c44355b64	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Tariff proposals	t	2025-07-29 13:45:44.636692	2025-07-29 13:45:44.636692	\N	\N	\N
eb3794b9-f65f-4a58-a110-4b05d66b9aa7	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Cash flow projections for 3 years	t	2025-07-29 13:45:44.641673	2025-07-29 13:45:44.641673	\N	\N	\N
73a042a2-5fef-4062-9b1d-8bbd444de16b	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Experience in the provision of similar services	t	2025-07-29 13:45:44.646195	2025-07-29 13:45:44.646195	\N	\N	\N
e0aaf45d-7f2d-4298-95c7-9adb0779522b	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Business registration or incorporation certificate	t	2025-07-29 13:45:44.649814	2025-07-29 13:45:44.649814	\N	\N	\N
aaac7391-7d7e-4bb9-9c91-312b359e76bc	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Valid tax compliance certificate	t	2025-07-29 13:45:44.653125	2025-07-29 13:45:44.653125	\N	\N	\N
0d17c326-f211-410c-9c86-f94a9aab8c46	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.656433	2025-07-29 13:45:44.656433	\N	\N	\N
fae14e28-244f-4c6f-96da-8e462f65c73d	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.659789	2025-07-29 13:45:44.659789	\N	\N	\N
c443812b-9893-43aa-b24b-4cf68645c5b0	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Goods in transit insurance	t	2025-07-29 13:45:44.665288	2025-07-29 13:45:44.665288	\N	\N	\N
2588bf28-1089-42e2-9c70-16079676158a	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Inventory of fleet/equipment	t	2025-07-29 13:45:44.669228	2025-07-29 13:45:44.669228	\N	\N	\N
03e557a3-161a-4773-9150-a8f45ee4f3de	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Customer service policy	t	2025-07-29 13:45:44.672026	2025-07-29 13:45:44.672026	\N	\N	\N
a6b2f8fd-fdd9-4dc6-aafa-c43ba456fb90	a20650aa-9ab6-479a-86a0-6ad45edc6db2	IT/tracking system description	t	2025-07-29 13:45:44.675281	2025-07-29 13:45:44.675281	\N	\N	\N
c7113875-28d8-4d47-b549-676d0d3b3710	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Three months of bank statements	t	2025-07-29 13:45:44.679208	2025-07-29 13:45:44.679208	\N	\N	\N
3b8403b2-25cb-4ee9-86b1-f6b067e8a810	a20650aa-9ab6-479a-86a0-6ad45edc6db2	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.683114	2025-07-29 13:45:44.683114	\N	\N	\N
a08acbdf-86e3-4c59-ab10-da295ceff919	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Business Plan	t	2025-07-29 13:45:44.686295	2025-07-29 13:45:44.686295	\N	\N	\N
cac4ab8e-be9a-4720-977c-e872282c707d	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Project proposal	t	2025-07-29 13:45:44.690188	2025-07-29 13:45:44.690188	\N	\N	\N
40a9fcd4-e6fb-4265-9b11-dc1139b7715b	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Stakeholder CVs	t	2025-07-29 13:45:44.694315	2025-07-29 13:45:44.694315	\N	\N	\N
c7cc0161-5d92-4384-9384-68e3d8299cbf	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Market analysis and projections	t	2025-07-29 13:45:44.698229	2025-07-29 13:45:44.698229	\N	\N	\N
7101ca7a-1ed4-48c7-b05a-ebfbaab5bd52	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.701298	2025-07-29 13:45:44.701298	\N	\N	\N
689df250-b876-411e-bf60-e60a268474a7	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Tariff proposals	t	2025-07-29 13:45:44.704143	2025-07-29 13:45:44.704143	\N	\N	\N
be5c88f9-6511-4777-91d3-063bc795f90c	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Cash flow projections for 3 years	t	2025-07-29 13:45:44.706239	2025-07-29 13:45:44.706239	\N	\N	\N
850e5657-85fe-4304-8e1d-56d91f7cf438	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Experience in the provision of similar services	t	2025-07-29 13:45:44.708926	2025-07-29 13:45:44.708926	\N	\N	\N
c27ec30d-8d19-41cd-8407-f1c18029b7f0	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Business registration or incorporation certificate	t	2025-07-29 13:45:44.715105	2025-07-29 13:45:44.715105	\N	\N	\N
dfe5537e-62dd-4f1e-b113-aeb65560cfa6	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Valid tax compliance certificate	t	2025-07-29 13:45:44.718203	2025-07-29 13:45:44.718203	\N	\N	\N
3dec82da-37c4-4ac6-a1a7-0c11d41bde61	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.72123	2025-07-29 13:45:44.72123	\N	\N	\N
4f7c392d-52bc-4228-8d4d-b24a8f44f8cd	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.724162	2025-07-29 13:45:44.724162	\N	\N	\N
c499176e-08c8-47b6-8925-256367e00d95	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Goods in transit insurance	t	2025-07-29 13:45:44.727655	2025-07-29 13:45:44.727655	\N	\N	\N
e240fb33-3277-4478-bac8-a34731f5a940	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Inventory of fleet/equipment	t	2025-07-29 13:45:44.732732	2025-07-29 13:45:44.732732	\N	\N	\N
9bc9bf53-5b6d-4e88-8558-912cb6877b85	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Customer service policy	t	2025-07-29 13:45:44.735862	2025-07-29 13:45:44.735862	\N	\N	\N
8d2edc08-9554-41fd-8e47-99880fa60fc1	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	IT/tracking system description	t	2025-07-29 13:45:44.738904	2025-07-29 13:45:44.738904	\N	\N	\N
9c0fe628-13cf-4432-91e4-e0d1b18a5487	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Three months of bank statements	t	2025-07-29 13:45:44.742823	2025-07-29 13:45:44.742823	\N	\N	\N
482f7b85-e9a8-4752-b312-f8ba4070f5b1	0ca371eb-c31a-49d5-ac36-b56f1d8dbbc7	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.74904	2025-07-29 13:45:44.74904	\N	\N	\N
c49c1d43-2503-414e-a425-d09ce355a825	c6dc15ef-21d3-4859-8f96-926a59e1be31	Business Plan	t	2025-07-29 13:45:44.757075	2025-07-29 13:45:44.757075	\N	\N	\N
67b710aa-550d-415e-9582-a8a967b7cf3c	c6dc15ef-21d3-4859-8f96-926a59e1be31	Project proposal	t	2025-07-29 13:45:44.768251	2025-07-29 13:45:44.768251	\N	\N	\N
cfd912a7-8164-4aab-a320-769406db0cad	c6dc15ef-21d3-4859-8f96-926a59e1be31	Stakeholder CVs	t	2025-07-29 13:45:44.777703	2025-07-29 13:45:44.777703	\N	\N	\N
206be29d-c427-4294-a5b7-d41ff3e666a9	c6dc15ef-21d3-4859-8f96-926a59e1be31	Market analysis and projections	t	2025-07-29 13:45:44.787601	2025-07-29 13:45:44.787601	\N	\N	\N
7825a28c-a347-406a-a991-313996fdccf6	c6dc15ef-21d3-4859-8f96-926a59e1be31	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.799902	2025-07-29 13:45:44.799902	\N	\N	\N
8f1eedbf-8efc-4c11-a1a4-9c98d1710d49	c6dc15ef-21d3-4859-8f96-926a59e1be31	Tariff proposals	t	2025-07-29 13:45:44.807882	2025-07-29 13:45:44.807882	\N	\N	\N
b91c6ff4-d500-4d9d-be8f-bcf4ae4c5c44	c6dc15ef-21d3-4859-8f96-926a59e1be31	Cash flow projections for 3 years	t	2025-07-29 13:45:44.815947	2025-07-29 13:45:44.815947	\N	\N	\N
99197448-e7be-487e-a9cf-76d3621b0c2e	c6dc15ef-21d3-4859-8f96-926a59e1be31	Experience in the provision of similar services	t	2025-07-29 13:45:44.82046	2025-07-29 13:45:44.82046	\N	\N	\N
9db1e4f5-c430-4c19-a988-91076b263ddd	c6dc15ef-21d3-4859-8f96-926a59e1be31	Business registration or incorporation certificate	t	2025-07-29 13:45:44.824051	2025-07-29 13:45:44.824051	\N	\N	\N
d973b8c0-6c58-472e-8742-026500193fe8	c6dc15ef-21d3-4859-8f96-926a59e1be31	Valid tax compliance certificate	t	2025-07-29 13:45:44.828119	2025-07-29 13:45:44.828119	\N	\N	\N
706799cd-be71-474c-af09-50a15f87b185	c6dc15ef-21d3-4859-8f96-926a59e1be31	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.835371	2025-07-29 13:45:44.835371	\N	\N	\N
42895bea-f631-46dd-b385-9207db837a87	c6dc15ef-21d3-4859-8f96-926a59e1be31	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.838854	2025-07-29 13:45:44.838854	\N	\N	\N
4d2118ed-84aa-4e06-97a5-a7d8b69a9467	c6dc15ef-21d3-4859-8f96-926a59e1be31	Goods in transit insurance	t	2025-07-29 13:45:44.84245	2025-07-29 13:45:44.84245	\N	\N	\N
40076f2d-b90d-4999-bdf2-405016b03b51	c6dc15ef-21d3-4859-8f96-926a59e1be31	Inventory of fleet/equipment	t	2025-07-29 13:45:44.848096	2025-07-29 13:45:44.848096	\N	\N	\N
aa7d264f-789e-4c70-ae86-915d24cb8761	c6dc15ef-21d3-4859-8f96-926a59e1be31	Customer service policy	t	2025-07-29 13:45:44.853052	2025-07-29 13:45:44.853052	\N	\N	\N
7868aa48-0eba-47aa-9b52-0e2f5f40abd4	c6dc15ef-21d3-4859-8f96-926a59e1be31	IT/tracking system description	t	2025-07-29 13:45:44.85653	2025-07-29 13:45:44.85653	\N	\N	\N
cafd7851-1c84-4962-a2f3-86ae61ec054f	c6dc15ef-21d3-4859-8f96-926a59e1be31	Three months of bank statements	t	2025-07-29 13:45:44.859701	2025-07-29 13:45:44.859701	\N	\N	\N
80819c64-9dbf-4ce8-9cfe-395ae77cfe7f	c6dc15ef-21d3-4859-8f96-926a59e1be31	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.865313	2025-07-29 13:45:44.865313	\N	\N	\N
b789b9fc-04e7-4e9a-a9b6-1b4e82e6b3f1	8b2c9cf9-115c-4574-8888-28e64282eaef	Business Plan	t	2025-07-29 13:45:44.870158	2025-07-29 13:45:44.870158	\N	\N	\N
c1c4b934-9cee-408e-8807-d82b8dd879e0	8b2c9cf9-115c-4574-8888-28e64282eaef	Project proposal	t	2025-07-29 13:45:44.874217	2025-07-29 13:45:44.874217	\N	\N	\N
a2ca8a7b-7e4d-433c-abed-578012bec0ee	8b2c9cf9-115c-4574-8888-28e64282eaef	Stakeholder CVs	t	2025-07-29 13:45:44.87742	2025-07-29 13:45:44.87742	\N	\N	\N
1f14156f-37e8-42b6-b3ab-4ed2105d74f7	8b2c9cf9-115c-4574-8888-28e64282eaef	Market analysis and projections	t	2025-07-29 13:45:44.883368	2025-07-29 13:45:44.883368	\N	\N	\N
411259de-5800-43b7-9042-aa665ccc3635	8b2c9cf9-115c-4574-8888-28e64282eaef	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.887085	2025-07-29 13:45:44.887085	\N	\N	\N
1531e376-398e-443b-8d05-35e9e9968454	8b2c9cf9-115c-4574-8888-28e64282eaef	Tariff proposals	t	2025-07-29 13:45:44.889934	2025-07-29 13:45:44.889934	\N	\N	\N
e15d66c7-4168-41be-b11c-437ee8879712	8b2c9cf9-115c-4574-8888-28e64282eaef	Cash flow projections for 3 years	t	2025-07-29 13:45:44.89367	2025-07-29 13:45:44.89367	\N	\N	\N
18e6cb61-8314-4ac2-b213-bd12f435d60e	8b2c9cf9-115c-4574-8888-28e64282eaef	Experience in the provision of similar services	t	2025-07-29 13:45:44.899333	2025-07-29 13:45:44.899333	\N	\N	\N
4f66a293-14ef-4454-8431-044689fac597	8b2c9cf9-115c-4574-8888-28e64282eaef	Business registration or incorporation certificate	t	2025-07-29 13:45:44.903536	2025-07-29 13:45:44.903536	\N	\N	\N
d53ddfbe-dc3a-4c5e-8655-718f3f70fa71	8b2c9cf9-115c-4574-8888-28e64282eaef	Valid tax compliance certificate	t	2025-07-29 13:45:44.907064	2025-07-29 13:45:44.907064	\N	\N	\N
50022759-495c-4b60-aa33-8e5e68ffaf69	8b2c9cf9-115c-4574-8888-28e64282eaef	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.910502	2025-07-29 13:45:44.910502	\N	\N	\N
5a1f391f-3934-4a03-bccd-07d93938178c	8b2c9cf9-115c-4574-8888-28e64282eaef	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.915431	2025-07-29 13:45:44.915431	\N	\N	\N
92ff33c4-3fa2-47bd-8ad9-a4eed1e4d9bb	8b2c9cf9-115c-4574-8888-28e64282eaef	Goods in transit insurance	t	2025-07-29 13:45:44.918567	2025-07-29 13:45:44.918567	\N	\N	\N
cfdd022b-10fc-429c-9778-c26756b53635	8b2c9cf9-115c-4574-8888-28e64282eaef	Inventory of fleet/equipment	t	2025-07-29 13:45:44.922022	2025-07-29 13:45:44.922022	\N	\N	\N
5467bf53-1e97-40c2-9dc7-802f39b57c65	8b2c9cf9-115c-4574-8888-28e64282eaef	Customer service policy	t	2025-07-29 13:45:44.925001	2025-07-29 13:45:44.925001	\N	\N	\N
7797ee4a-1ce6-4989-8dfc-721ca0ef3477	8b2c9cf9-115c-4574-8888-28e64282eaef	IT/tracking system description	t	2025-07-29 13:45:44.929248	2025-07-29 13:45:44.929248	\N	\N	\N
1d5793a1-e333-4d25-bf76-2c7e11dc3dc3	8b2c9cf9-115c-4574-8888-28e64282eaef	Three months of bank statements	t	2025-07-29 13:45:44.933892	2025-07-29 13:45:44.933892	\N	\N	\N
de4f77e6-50c5-4b53-b902-7bc9c38c4ca8	8b2c9cf9-115c-4574-8888-28e64282eaef	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:44.938076	2025-07-29 13:45:44.938076	\N	\N	\N
dbe38496-0b87-4994-8bee-e4d2256f1744	759e35f3-c04e-43d9-8c80-0471b99528f1	Business Plan	t	2025-07-29 13:45:44.942101	2025-07-29 13:45:44.942101	\N	\N	\N
9b9013ac-5b0f-46d7-9065-9c0985d159bd	759e35f3-c04e-43d9-8c80-0471b99528f1	Project proposal	t	2025-07-29 13:45:44.946303	2025-07-29 13:45:44.946303	\N	\N	\N
830c1a1c-9438-42f7-a843-677cd6f1b131	759e35f3-c04e-43d9-8c80-0471b99528f1	Stakeholder CVs	t	2025-07-29 13:45:44.950492	2025-07-29 13:45:44.950492	\N	\N	\N
4e95bb6a-8c72-4423-95f8-23f1ef859542	759e35f3-c04e-43d9-8c80-0471b99528f1	Market analysis and projections	t	2025-07-29 13:45:44.953543	2025-07-29 13:45:44.953543	\N	\N	\N
28d14f67-6963-473d-ac31-0b503048b752	759e35f3-c04e-43d9-8c80-0471b99528f1	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:44.9562	2025-07-29 13:45:44.9562	\N	\N	\N
38aa23a0-894b-456b-9de6-d441b63fb3c9	759e35f3-c04e-43d9-8c80-0471b99528f1	Tariff proposals	t	2025-07-29 13:45:44.959567	2025-07-29 13:45:44.959567	\N	\N	\N
d35a9663-dd6c-4e9f-abcb-307f136c7d02	759e35f3-c04e-43d9-8c80-0471b99528f1	Cash flow projections for 3 years	t	2025-07-29 13:45:44.964889	2025-07-29 13:45:44.964889	\N	\N	\N
76f4d9d0-9eb1-4c1c-8f81-c6c8e9cbee87	759e35f3-c04e-43d9-8c80-0471b99528f1	Experience in the provision of similar services	t	2025-07-29 13:45:44.968553	2025-07-29 13:45:44.968553	\N	\N	\N
05395a95-e3d2-4354-88f4-c5a7ef799ea9	759e35f3-c04e-43d9-8c80-0471b99528f1	Business registration or incorporation certificate	t	2025-07-29 13:45:44.972397	2025-07-29 13:45:44.972397	\N	\N	\N
fd20136b-da23-4d91-9ff6-4bd6f251cadc	759e35f3-c04e-43d9-8c80-0471b99528f1	Valid tax compliance certificate	t	2025-07-29 13:45:44.976091	2025-07-29 13:45:44.976091	\N	\N	\N
c0d841b7-79ce-49b3-b20d-ceba1a17bfab	759e35f3-c04e-43d9-8c80-0471b99528f1	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:44.981389	2025-07-29 13:45:44.981389	\N	\N	\N
32ed093f-d651-4aa1-bba1-388af14115aa	759e35f3-c04e-43d9-8c80-0471b99528f1	Proof of premises (lease/title deed)	t	2025-07-29 13:45:44.985986	2025-07-29 13:45:44.985986	\N	\N	\N
27226c4d-306b-4273-bf04-4d884e0c7d36	759e35f3-c04e-43d9-8c80-0471b99528f1	Goods in transit insurance	t	2025-07-29 13:45:44.98926	2025-07-29 13:45:44.98926	\N	\N	\N
63c665ca-b6d5-4c12-93f7-c4667585926c	759e35f3-c04e-43d9-8c80-0471b99528f1	Inventory of fleet/equipment	t	2025-07-29 13:45:44.992679	2025-07-29 13:45:44.992679	\N	\N	\N
43b4ecff-9338-4fcb-9f49-86a6ae7be002	759e35f3-c04e-43d9-8c80-0471b99528f1	Customer service policy	t	2025-07-29 13:45:44.997809	2025-07-29 13:45:44.997809	\N	\N	\N
09da9bc8-f32d-40b8-9242-f88e0142a2ef	759e35f3-c04e-43d9-8c80-0471b99528f1	IT/tracking system description	t	2025-07-29 13:45:45.003032	2025-07-29 13:45:45.003032	\N	\N	\N
ac07abe3-546c-4d13-8c02-b60fd6e84d60	759e35f3-c04e-43d9-8c80-0471b99528f1	Three months of bank statements	t	2025-07-29 13:45:45.006672	2025-07-29 13:45:45.006672	\N	\N	\N
37b1cc0f-9d80-405b-8029-8f1c564ab5c8	759e35f3-c04e-43d9-8c80-0471b99528f1	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:45.010224	2025-07-29 13:45:45.010224	\N	\N	\N
6d615105-e792-4d81-a47d-55ac036e2fc4	805727cb-a74e-432c-ac67-98011ebccbbf	Business Plan	t	2025-07-29 13:45:45.01634	2025-07-29 13:45:45.01634	\N	\N	\N
b250b201-e9a0-4b63-a784-e967beddc56e	805727cb-a74e-432c-ac67-98011ebccbbf	Project proposal	t	2025-07-29 13:45:45.020368	2025-07-29 13:45:45.020368	\N	\N	\N
1ac472f4-3c52-4c27-85ff-e9bdda4cafb2	805727cb-a74e-432c-ac67-98011ebccbbf	Stakeholder CVs	t	2025-07-29 13:45:45.023643	2025-07-29 13:45:45.023643	\N	\N	\N
a130799c-52c1-46d5-adbe-dd6b3c28b217	805727cb-a74e-432c-ac67-98011ebccbbf	Market analysis and projections	t	2025-07-29 13:45:45.026753	2025-07-29 13:45:45.026753	\N	\N	\N
0ed840f7-d6f1-42d3-9f76-8dc323d56c1f	805727cb-a74e-432c-ac67-98011ebccbbf	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:45.031713	2025-07-29 13:45:45.031713	\N	\N	\N
0e47b34e-3dcd-4bdd-b7df-03da08a58798	805727cb-a74e-432c-ac67-98011ebccbbf	Tariff proposals	t	2025-07-29 13:45:45.037384	2025-07-29 13:45:45.037384	\N	\N	\N
9bdcb694-3754-4208-b811-3a01e2c21b04	805727cb-a74e-432c-ac67-98011ebccbbf	Cash flow projections for 3 years	t	2025-07-29 13:45:45.040521	2025-07-29 13:45:45.040521	\N	\N	\N
abd07f1f-1624-454c-9983-6c2a6c2cd7bc	805727cb-a74e-432c-ac67-98011ebccbbf	Experience in the provision of similar services	t	2025-07-29 13:45:45.04447	2025-07-29 13:45:45.04447	\N	\N	\N
62e5779f-46d0-459e-a704-b815f302f60a	805727cb-a74e-432c-ac67-98011ebccbbf	Business registration or incorporation certificate	t	2025-07-29 13:45:45.053141	2025-07-29 13:45:45.053141	\N	\N	\N
d1ba2fbc-4a75-4347-8d65-aa7404a33e65	805727cb-a74e-432c-ac67-98011ebccbbf	Valid tax compliance certificate	t	2025-07-29 13:45:45.057045	2025-07-29 13:45:45.057045	\N	\N	\N
bd7c34ff-8719-480b-8db7-a476f9eb423e	805727cb-a74e-432c-ac67-98011ebccbbf	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:45.05991	2025-07-29 13:45:45.05991	\N	\N	\N
4e6a66d2-6182-43f1-b289-72b01af10385	805727cb-a74e-432c-ac67-98011ebccbbf	Proof of premises (lease/title deed)	t	2025-07-29 13:45:45.064114	2025-07-29 13:45:45.064114	\N	\N	\N
035408cf-5a9c-4e36-a529-90a4bb9f55da	805727cb-a74e-432c-ac67-98011ebccbbf	Goods in transit insurance	t	2025-07-29 13:45:45.07009	2025-07-29 13:45:45.07009	\N	\N	\N
9f6de10b-d76e-4abd-bd49-76007f20e715	805727cb-a74e-432c-ac67-98011ebccbbf	Inventory of fleet/equipment	t	2025-07-29 13:45:45.073691	2025-07-29 13:45:45.073691	\N	\N	\N
47e52012-53c8-4f36-88fd-911ad75c8c8a	805727cb-a74e-432c-ac67-98011ebccbbf	Customer service policy	t	2025-07-29 13:45:45.077546	2025-07-29 13:45:45.077546	\N	\N	\N
ca67aa4e-0deb-4b68-985a-67ef2dfb0a86	805727cb-a74e-432c-ac67-98011ebccbbf	IT/tracking system description	t	2025-07-29 13:45:45.083661	2025-07-29 13:45:45.083661	\N	\N	\N
6935738a-01ee-47cd-88f6-56f5ce4ae63e	805727cb-a74e-432c-ac67-98011ebccbbf	Three months of bank statements	t	2025-07-29 13:45:45.087144	2025-07-29 13:45:45.087144	\N	\N	\N
5ccafe41-dc0d-4a61-b485-bd4fdf23dfe1	805727cb-a74e-432c-ac67-98011ebccbbf	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:45.090356	2025-07-29 13:45:45.090356	\N	\N	\N
********-e14b-4813-9e99-edad9f592627	dfb54884-823f-41f8-b612-c0743be60345	Business Plan	t	2025-07-29 13:45:45.094043	2025-07-29 13:45:45.094043	\N	\N	\N
c5be894b-9b82-4e1f-a940-3dcde526ac20	dfb54884-823f-41f8-b612-c0743be60345	Project proposal	t	2025-07-29 13:45:45.103899	2025-07-29 13:45:45.103899	\N	\N	\N
ff9cd714-154c-4546-883a-b6cab4dae59a	dfb54884-823f-41f8-b612-c0743be60345	Stakeholder CVs	t	2025-07-29 13:45:45.108362	2025-07-29 13:45:45.108362	\N	\N	\N
3a0debaf-b3f3-47af-89b3-0bad6d121dd2	dfb54884-823f-41f8-b612-c0743be60345	Market analysis and projections	t	2025-07-29 13:45:45.113013	2025-07-29 13:45:45.113013	\N	\N	\N
********-72d5-4dbb-a4a9-25eeff00ec49	dfb54884-823f-41f8-b612-c0743be60345	Particulars of financial resources to be applied to project	t	2025-07-29 13:45:45.119649	2025-07-29 13:45:45.119649	\N	\N	\N
65bfca46-7d5b-45bc-987c-3120ce3bf992	dfb54884-823f-41f8-b612-c0743be60345	Tariff proposals	t	2025-07-29 13:45:45.123229	2025-07-29 13:45:45.123229	\N	\N	\N
f7de8f10-c1e0-4deb-982d-24edc1f6eb1b	dfb54884-823f-41f8-b612-c0743be60345	Cash flow projections for 3 years	t	2025-07-29 13:45:45.127034	2025-07-29 13:45:45.127034	\N	\N	\N
92ec35b3-2926-4ee8-a329-47d2bc73a645	dfb54884-823f-41f8-b612-c0743be60345	Experience in the provision of similar services	t	2025-07-29 13:45:45.13434	2025-07-29 13:45:45.13434	\N	\N	\N
e0c5e99e-d0d0-45ce-8be3-329cc57c95a5	dfb54884-823f-41f8-b612-c0743be60345	Business registration or incorporation certificate	t	2025-07-29 13:45:45.138166	2025-07-29 13:45:45.138166	\N	\N	\N
fcc34474-d838-4268-824d-25cd53f8c3be	dfb54884-823f-41f8-b612-c0743be60345	Valid tax compliance certificate	t	2025-07-29 13:45:45.144849	2025-07-29 13:45:45.144849	\N	\N	\N
839e0788-1673-4d79-9796-bddb1b97dee7	dfb54884-823f-41f8-b612-c0743be60345	Business plan (including service model, financials, and coverage)	t	2025-07-29 13:45:45.150896	2025-07-29 13:45:45.150896	\N	\N	\N
337cf7c2-9681-4f9c-a642-aa40a8341d24	dfb54884-823f-41f8-b612-c0743be60345	Proof of premises (lease/title deed)	t	2025-07-29 13:45:45.157802	2025-07-29 13:45:45.157802	\N	\N	\N
fa61171d-a767-4e34-ac42-621142723f7d	dfb54884-823f-41f8-b612-c0743be60345	Goods in transit insurance	t	2025-07-29 13:45:45.161031	2025-07-29 13:45:45.161031	\N	\N	\N
01d8d631-dc2d-49c9-9a80-d4dde13db89a	dfb54884-823f-41f8-b612-c0743be60345	Inventory of fleet/equipment	t	2025-07-29 13:45:45.165571	2025-07-29 13:45:45.165571	\N	\N	\N
3a585dcb-7f7d-4f12-af01-c26c0c5980f3	dfb54884-823f-41f8-b612-c0743be60345	Customer service policy	t	2025-07-29 13:45:45.169204	2025-07-29 13:45:45.169204	\N	\N	\N
70cdd4e6-eb80-4300-bf53-c060c431d648	dfb54884-823f-41f8-b612-c0743be60345	IT/tracking system description	t	2025-07-29 13:45:45.172945	2025-07-29 13:45:45.172945	\N	\N	\N
d958b0cb-5c05-43f0-833d-9e480f5382e9	dfb54884-823f-41f8-b612-c0743be60345	Three months of bank statements	t	2025-07-29 13:45:45.177251	2025-07-29 13:45:45.177251	\N	\N	\N
543ae8db-208d-4afd-8e88-1582ccc583ec	dfb54884-823f-41f8-b612-c0743be60345	Proof of payment (application fee of USD 100)	t	2025-07-29 13:45:45.181936	2025-07-29 13:45:45.181936	\N	\N	\N
\.


--
-- Data for Name: license_types; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.license_types (license_type_id, name, description, validity, created_at, created_by, updated_at, code, updated_by, deleted_at) FROM stdin;
92ce0377-e8fd-4a38-80ec-a66c2729448d	Telecommunications	Licenses for telecommunications services including mobile networks, fixed networks, internet services, and broadcasting	5	2025-07-29 13:45:43.495345	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.495345	telecommunications	\N	\N
82daf945-093c-446d-b6e1-6af8c073bf99	Postal Services	Licenses for postal and courier services including domestic and international mail delivery	3	2025-07-29 13:45:43.500594	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.500594	postal_services	\N	\N
03b97f8c-65ee-426a-8924-c07317606641	Standards Compliance	Certificates for standards compliance including type approval and short-codes	2	2025-07-29 13:45:43.504844	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.504844	standards_compliance	\N	\N
e8616674-c074-4c3f-95e3-6d7eb41b364c	Broadcasting	Licenses for radio and television broadcasting services	5	2025-07-29 13:45:43.510903	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.510903	broadcasting	\N	\N
991f5653-f99b-47da-abd9-eac35d1851cd	Spectrum Management	Licenses for radio frequency spectrum allocation and management	10	2025-07-29 13:45:43.515072	50aa1b2e-71ba-4a49-99f0-35fde62c8a06	2025-07-29 13:45:43.515072	spectrum_management	\N	\N
\.


--
-- Data for Name: licenses; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.licenses (license_id, description, license_number, application_id, applicant_id, license_type_id, status, issue_date, expiry_date, issued_by, code, conditions, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.notifications (notification_id, type, status, priority, recipient_type, recipient_id, recipient_email, recipient_phone, subject, message, html_content, entity_type, entity_id, metadata, external_id, error_message, retry_count, is_read, sent_at, delivered_at, action_url, expires_at, created_at, created_by, updated_at, updated_by, deleted_at, read_at) FROM stdin;
\.


--
-- Data for Name: organizations; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.organizations (organization_id, name, registration_number, website, email, phone, fax, physical_address_id, postal_address_id, contact_id, date_incorporation, place_incorporation, created_by, updated_by, created_at, updated_at, deleted_at) FROM stdin;
a315f832-da31-43dc-b9c3-251929332ab5	Malawi Telecom Ltd	MW-REG-001	https://www.mtl.mw	<EMAIL>	+26511234567	+26511234568	\N	\N	\N	2000-01-15	Blantyre	\N	\N	2025-07-29 13:45:45.190229	2025-07-29 13:45:45.190229	\N
741b2fc1-dc65-47ba-b317-f24d152a192a	Blantyre Electronics Inc	MW-REG-002	https://www.bei.mw	<EMAIL>	+26512345678	\N	\N	\N	\N	2005-06-23	Blantyre	\N	\N	2025-07-29 13:45:45.190229	2025-07-29 13:45:45.190229	\N
d58b3639-5c24-40e8-a9d2-652325bfcb41	Lilongwe Digital Solutions	MW-REG-003	https://www.lilodigi.mw	<EMAIL>	+26519876543	+26519876544	\N	\N	\N	2012-09-10	Lilongwe	\N	\N	2025-07-29 13:45:45.190229	2025-07-29 13:45:45.190229	\N
f8318b2a-028b-41d8-8dd7-58686bd5f9ec	Zomba Communication Group	MW-REG-004	https://www.zcg.mw	<EMAIL>	+26514567890	\N	\N	\N	\N	2018-03-05	Zomba	\N	\N	2025-07-29 13:45:45.190229	2025-07-29 13:45:45.190229	\N
bdc2cc6f-7681-48c9-a16c-1c1d1f219f98	Mzuzu Broadcasting Corp	MW-REG-005	https://www.mbc.mw	<EMAIL>	+26510987654	\N	\N	\N	\N	2020-11-30	Mzuzu	\N	\N	2025-07-29 13:45:45.190229	2025-07-29 13:45:45.190229	\N
\.


--
-- Data for Name: payments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.payments (payment_id, invoice_number, amount, currency, status, payment_type, description, issue_date, due_date, paid_date, payment_method, notes, transaction_reference, proof_of_payment_url, proof_of_payment_notes, proof_of_payment_uploaded_at, invoice_id, entity_type, entity_id, user_id, created_by, updated_by, created_at, updated_at, deleted_at, application_id) FROM stdin;
\.


--
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.permissions (permission_id, name, description, category, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
7fe3e071-1430-43b0-b7b8-54f3ffc6e5e9	user:create	Create new users	User Management	2025-07-29 13:45:41.561742	2025-07-29 13:45:41.561742	\N	\N	\N
a82e63d4-ce9e-4e93-b7d0-32097928c2ab	user:read	View user information	User Management	2025-07-29 13:45:41.578623	2025-07-29 13:45:41.578623	\N	\N	\N
a06cbbee-232f-4478-9610-8367b4f9ab65	user:update	Update user information	User Management	2025-07-29 13:45:41.584718	2025-07-29 13:45:41.584718	\N	\N	\N
8a986107-b9b2-48dd-8379-f7cdf456e017	user:delete	Delete users	User Management	2025-07-29 13:45:41.589642	2025-07-29 13:45:41.589642	\N	\N	\N
7fbad1e9-9da6-49ff-843f-79adf7028315	role:create	Create new roles	Role Management	2025-07-29 13:45:41.594753	2025-07-29 13:45:41.594753	\N	\N	\N
502f1174-ec14-449a-8ae8-98ffd75619c3	role:read	View role information	Role Management	2025-07-29 13:45:41.600345	2025-07-29 13:45:41.600345	\N	\N	\N
a6834152-8ad7-49ed-b72f-5f05db23d33d	role:update	Update role information	Role Management	2025-07-29 13:45:41.604496	2025-07-29 13:45:41.604496	\N	\N	\N
e81b2e5b-4cd5-495c-94dd-38fc25a383a1	role:delete	Delete roles	Role Management	2025-07-29 13:45:41.608857	2025-07-29 13:45:41.608857	\N	\N	\N
4330a9c9-960f-4337-997e-fddcbff70493	permission:create	Create new permissions	Permission Management	2025-07-29 13:45:41.613389	2025-07-29 13:45:41.613389	\N	\N	\N
75785afe-a63d-4e81-bbc4-c3ad88326a81	permission:read	View permission information	Permission Management	2025-07-29 13:45:41.619335	2025-07-29 13:45:41.619335	\N	\N	\N
08364d9b-2c7b-4afc-879e-7a7d3107df8b	permission:update	Update permission information	Permission Management	2025-07-29 13:45:41.622989	2025-07-29 13:45:41.622989	\N	\N	\N
ab9f042e-1361-4fbd-9248-7954cf5315c4	permission:delete	Delete permissions	Permission Management	2025-07-29 13:45:41.625619	2025-07-29 13:45:41.625619	\N	\N	\N
046821b6-615a-4622-8f85-87931ba716b5	license:create	Create new licenses	License Management	2025-07-29 13:45:41.629203	2025-07-29 13:45:41.629203	\N	\N	\N
92c36138-32a0-405e-b742-01284ec55f8a	license:read	View license information	License Management	2025-07-29 13:45:41.633943	2025-07-29 13:45:41.633943	\N	\N	\N
7150cac2-b300-4124-bd25-8734305e993c	license:update	Update license information	License Management	2025-07-29 13:45:41.638829	2025-07-29 13:45:41.638829	\N	\N	\N
0102178b-a2d2-4767-a2ef-caae92e6de81	license:delete	Delete licenses	License Management	2025-07-29 13:45:41.642169	2025-07-29 13:45:41.642169	\N	\N	\N
c37659bd-955a-47e0-b1f8-8bf59da0bba9	license:approve	Approve license applications	License Management	2025-07-29 13:45:41.645359	2025-07-29 13:45:41.645359	\N	\N	\N
58b38f59-b3e4-43d6-9e73-b3824861a4fe	license:reject	Reject license applications	License Management	2025-07-29 13:45:41.648977	2025-07-29 13:45:41.648977	\N	\N	\N
fc8cc641-904b-4d5e-a576-4e700bdd2b8e	license_type:create	Create new license types	License Types Management	2025-07-29 13:45:41.654259	2025-07-29 13:45:41.654259	\N	\N	\N
ab449e9a-9346-4d2b-baf7-fd1e79edbb40	license_type:read	View license types	License Types Management	2025-07-29 13:45:41.65852	2025-07-29 13:45:41.65852	\N	\N	\N
96937ee3-d213-4035-b4f6-6c5afb1f8d48	license_type:update	Update license types	License Types Management	2025-07-29 13:45:41.662154	2025-07-29 13:45:41.662154	\N	\N	\N
f9de64e1-db18-4db6-8fe7-0bcf9b2a7708	license_type:delete	Delete license types	License Types Management	2025-07-29 13:45:41.665271	2025-07-29 13:45:41.665271	\N	\N	\N
f95c089d-8172-4170-ad7f-8b0d7db13347	license_category:create	Create new license categories	License Categories Management	2025-07-29 13:45:41.670418	2025-07-29 13:45:41.670418	\N	\N	\N
edcbe658-40c2-420b-adf4-42051bbe52c8	license_category:read	View license categories	License Categories Management	2025-07-29 13:45:41.673898	2025-07-29 13:45:41.673898	\N	\N	\N
4b8c6178-03ca-47d4-87db-8f810f059ecc	license_category:update	Update license categories	License Categories Management	2025-07-29 13:45:41.677491	2025-07-29 13:45:41.677491	\N	\N	\N
08b00ebb-ab24-4a72-9c65-143a6b5c867c	license_category:delete	Delete license categories	License Categories Management	2025-07-29 13:45:41.680439	2025-07-29 13:45:41.680439	\N	\N	\N
c74ee08a-aaa8-4dd5-b055-7fcd00649faa	application:create	Create new applications	Application Management	2025-07-29 13:45:41.685679	2025-07-29 13:45:41.685679	\N	\N	\N
692a3cd1-f8f0-4371-8248-66b2cf34b6f8	application:read	View application information	Application Management	2025-07-29 13:45:41.689821	2025-07-29 13:45:41.689821	\N	\N	\N
fb064bd4-3933-4565-ba30-8f36f9d75e30	application:update	Update application information	Application Management	2025-07-29 13:45:41.693506	2025-07-29 13:45:41.693506	\N	\N	\N
3ec35e7d-7de0-413a-b99a-d52568f7977e	application:delete	Delete applications	Application Management	2025-07-29 13:45:41.696746	2025-07-29 13:45:41.696746	\N	\N	\N
c1ef8f3f-0626-497e-85f1-adc8d60a42e4	application:evaluate	Evaluate applications	Application Management	2025-07-29 13:45:41.701654	2025-07-29 13:45:41.701654	\N	\N	\N
8a64b712-811c-4374-9115-31b2b6eade4b	application:submit	Submit applications	Application Management	2025-07-29 13:45:41.705269	2025-07-29 13:45:41.705269	\N	\N	\N
de11c81a-9d9d-4770-9a38-6c1c4a47779e	application:approve	Approve applications	Application Management	2025-07-29 13:45:41.708614	2025-07-29 13:45:41.708614	\N	\N	\N
44dd4463-0079-4576-9d0e-a51bf0335741	application:reject	Reject applications	Application Management	2025-07-29 13:45:41.712391	2025-07-29 13:45:41.712391	\N	\N	\N
5e88688b-4441-433f-aaaf-efb610fd8bac	financial:read	View financial information	Financial Management	2025-07-29 13:45:41.717853	2025-07-29 13:45:41.717853	\N	\N	\N
a0e13577-dfed-453c-b738-233517c30519	financial:update	Update financial information	Financial Management	2025-07-29 13:45:41.721837	2025-07-29 13:45:41.721837	\N	\N	\N
64fb308c-349b-4852-aba8-64f6e229d11b	financial:reports	Generate financial reports	Financial Management	2025-07-29 13:45:41.725698	2025-07-29 13:45:41.725698	\N	\N	\N
5ed56e01-356b-402c-b8c0-2eb7b41ded6a	invoice:create	Create invoices	Financial Management	2025-07-29 13:45:41.729326	2025-07-29 13:45:41.729326	\N	\N	\N
66291ff2-b7b4-4fd1-8903-ea595173eb71	invoice:read	View invoices	Financial Management	2025-07-29 13:45:41.734886	2025-07-29 13:45:41.734886	\N	\N	\N
2436b40c-d468-437f-b862-422355979b0e	invoice:update	Update invoices	Financial Management	2025-07-29 13:45:41.741406	2025-07-29 13:45:41.741406	\N	\N	\N
192ef84f-617d-44b6-94b8-2240fb96aaf5	invoice:delete	Delete invoices	Financial Management	2025-07-29 13:45:41.746705	2025-07-29 13:45:41.746705	\N	\N	\N
642f2728-6ffb-49b1-ae92-df16d8361c35	payment:create	Create payments	Financial Management	2025-07-29 13:45:41.753297	2025-07-29 13:45:41.753297	\N	\N	\N
8eeb404f-bfcc-47bc-975c-dfac51ee14bb	payment:read	View payments	Financial Management	2025-07-29 13:45:41.759928	2025-07-29 13:45:41.759928	\N	\N	\N
bfefdccb-9f9d-4702-a2a5-23630e8b7429	payment:update	Update payments	Financial Management	2025-07-29 13:45:41.769316	2025-07-29 13:45:41.769316	\N	\N	\N
7b03d8f6-5c1e-48da-bfbe-b65c0b1ac558	payment:delete	Delete payments	Financial Management	2025-07-29 13:45:41.779805	2025-07-29 13:45:41.779805	\N	\N	\N
915498a1-1564-49ba-9c14-c8128ee38db0	document:create	Upload documents	Document Management	2025-07-29 13:45:41.788957	2025-07-29 13:45:41.788957	\N	\N	\N
52adf122-d1af-4c4d-b123-97e2550fdb5b	document:read	View documents	Document Management	2025-07-29 13:45:41.795716	2025-07-29 13:45:41.795716	\N	\N	\N
3eeca83f-9ae6-482a-8c90-858cd1d2ff59	document:update	Update documents	Document Management	2025-07-29 13:45:41.804579	2025-07-29 13:45:41.804579	\N	\N	\N
4887f9f9-3ab7-4aa4-965f-edec49cae314	document:delete	Delete documents	Document Management	2025-07-29 13:45:41.812986	2025-07-29 13:45:41.812986	\N	\N	\N
293af26d-7db7-4464-a057-fc9c0922773f	document:download	Download documents	Document Management	2025-07-29 13:45:41.820922	2025-07-29 13:45:41.820922	\N	\N	\N
cc4c9507-a7b6-4c48-bb56-4fd65d60dfe0	identification_type:create	Create identification types	Identification Types Management	2025-07-29 13:45:41.82635	2025-07-29 13:45:41.82635	\N	\N	\N
ea222a32-3bf9-4a91-9865-3bf4107e68af	identification_type:read	View identification types	Identification Types Management	2025-07-29 13:45:41.831237	2025-07-29 13:45:41.831237	\N	\N	\N
081cab8f-6cb6-447c-b8eb-ddb5d472499e	identification_type:update	Update identification types	Identification Types Management	2025-07-29 13:45:41.837415	2025-07-29 13:45:41.837415	\N	\N	\N
d41c0f8e-7e08-4eb1-b55e-c822d24aebc7	identification_type:delete	Delete identification types	Identification Types Management	2025-07-29 13:45:41.8411	2025-07-29 13:45:41.8411	\N	\N	\N
cfff2c0a-fa8d-4dc2-8b49-4d3e9b55fe03	contact:create	Create contacts	Contact Management	2025-07-29 13:45:41.845676	2025-07-29 13:45:41.845676	\N	\N	\N
0b35c180-535b-45e7-b128-d28c07bc9f3d	contact:read	View contacts	Contact Management	2025-07-29 13:45:41.850031	2025-07-29 13:45:41.850031	\N	\N	\N
3c89cdde-2eb1-4bde-8f0b-7fd63b0bb5ef	contact:update	Update contacts	Contact Management	2025-07-29 13:45:41.858167	2025-07-29 13:45:41.858167	\N	\N	\N
5efe6187-955e-4cdb-91b0-b3cf48103e4c	contact:delete	Delete contacts	Contact Management	2025-07-29 13:45:41.862068	2025-07-29 13:45:41.862068	\N	\N	\N
0d27fd63-ff42-4639-aa03-8b829d8978ac	applicant:create	Create applicants	Applicant Management	2025-07-29 13:45:41.866963	2025-07-29 13:45:41.866963	\N	\N	\N
3cf4db6f-1ef1-4171-87f4-265070ec681e	applicant:read	View applicants	Applicant Management	2025-07-29 13:45:41.872391	2025-07-29 13:45:41.872391	\N	\N	\N
caca1a73-9de4-4308-b430-407483177e4b	applicant:update	Update applicants	Applicant Management	2025-07-29 13:45:41.876379	2025-07-29 13:45:41.876379	\N	\N	\N
5fcad0f7-25e5-4678-88c7-df2a31c5c679	applicant:delete	Delete applicants	Applicant Management	2025-07-29 13:45:41.879922	2025-07-29 13:45:41.879922	\N	\N	\N
74a7acb8-42ab-4137-9b22-b836c1224d0e	evaluation:create	Create evaluations	Evaluation Management	2025-07-29 13:45:41.884252	2025-07-29 13:45:41.884252	\N	\N	\N
c62a7da6-247c-4528-b20d-b54114582f11	evaluation:read	View evaluations	Evaluation Management	2025-07-29 13:45:41.889097	2025-07-29 13:45:41.889097	\N	\N	\N
1a00b248-6607-45b8-ac91-81df06dec6a6	evaluation:update	Update evaluations	Evaluation Management	2025-07-29 13:45:41.892601	2025-07-29 13:45:41.892601	\N	\N	\N
3671cdeb-54c4-4a1c-b737-ed45eb59f752	evaluation:delete	Delete evaluations	Evaluation Management	2025-07-29 13:45:41.895468	2025-07-29 13:45:41.895468	\N	\N	\N
3c9da83c-9f23-4fff-aa04-a0dcbe45bc98	evaluation:submit	Submit evaluations	Evaluation Management	2025-07-29 13:45:41.899249	2025-07-29 13:45:41.899249	\N	\N	\N
ef99445f-99a0-4ba4-aa62-a8a8b0fe095c	notification:create	Create notifications	Notification Management	2025-07-29 13:45:41.905528	2025-07-29 13:45:41.905528	\N	\N	\N
74d4195b-c21b-4d25-9460-cee11dd518ee	notification:read	View notifications	Notification Management	2025-07-29 13:45:41.909444	2025-07-29 13:45:41.909444	\N	\N	\N
c3e111c4-6172-4e99-ae44-fde9a1d11b90	notification:update	Update notifications	Notification Management	2025-07-29 13:45:41.912724	2025-07-29 13:45:41.912724	\N	\N	\N
33362c71-d89f-4182-87c5-47187b974b03	notification:delete	Delete notifications	Notification Management	2025-07-29 13:45:41.917544	2025-07-29 13:45:41.917544	\N	\N	\N
c67b5b1e-18cb-4c5e-a8a9-130c8e30cfec	notification:send	Send notifications	Notification Management	2025-07-29 13:45:41.922108	2025-07-29 13:45:41.922108	\N	\N	\N
04fc3f29-bac1-4314-aaf8-27eac2f0fc76	report:generate	Generate reports	Reports Management	2025-07-29 13:45:41.925999	2025-07-29 13:45:41.925999	\N	\N	\N
9390ff55-a1fe-4e05-bc48-569edeb36e94	report:view	View reports	Reports Management	2025-07-29 13:45:41.929498	2025-07-29 13:45:41.929498	\N	\N	\N
5f0780a6-406a-4264-937b-0309cb79296f	report:export	Export reports	Reports Management	2025-07-29 13:45:41.93269	2025-07-29 13:45:41.93269	\N	\N	\N
9318aedd-125b-4345-9f5b-20c2ccaab193	report:schedule	Schedule reports	Reports Management	2025-07-29 13:45:41.939555	2025-07-29 13:45:41.939555	\N	\N	\N
19a17fcb-b53d-4367-8c99-3e0afa4daee1	system:settings	Manage system settings	System Administration	2025-07-29 13:45:41.944409	2025-07-29 13:45:41.944409	\N	\N	\N
cd0c0418-8288-4783-a7c2-66208091d0a8	system:audit	View audit logs	System Administration	2025-07-29 13:45:41.948451	2025-07-29 13:45:41.948451	\N	\N	\N
2d77a09e-9307-4f89-a214-f69ca88bda8d	system:backup	Manage system backups	System Administration	2025-07-29 13:45:41.95445	2025-07-29 13:45:41.95445	\N	\N	\N
856c27b0-a1e6-4800-925e-31abf49145f0	system:maintenance	Perform system maintenance	System Administration	2025-07-29 13:45:41.958218	2025-07-29 13:45:41.958218	\N	\N	\N
505541a9-9867-4f59-98ca-bc4081f3ae42	postal_code:create	Create postal codes	Postal Code Management	2025-07-29 13:45:41.964336	2025-07-29 13:45:41.964336	\N	\N	\N
b59993a9-55ca-4cd5-8639-cae2239f5428	postal_code:read	View postal codes	Postal Code Management	2025-07-29 13:45:41.971538	2025-07-29 13:45:41.971538	\N	\N	\N
5ac2b779-ca1c-4e59-baca-f721e2d4bba9	postal_code:update	Update postal codes	Postal Code Management	2025-07-29 13:45:41.975687	2025-07-29 13:45:41.975687	\N	\N	\N
4162ca34-07a9-4aee-8319-b9de3f11f138	postal_code:delete	Delete postal codes	Postal Code Management	2025-07-29 13:45:41.980112	2025-07-29 13:45:41.980112	\N	\N	\N
f9ce287e-47e6-4767-9e67-a25ef8621cf7	employee:create	Create employees	Employee Management	2025-07-29 13:45:41.987571	2025-07-29 13:45:41.987571	\N	\N	\N
dfcd4e31-2cdd-41c5-b4c5-c0b10fb96517	employee:read	View employees	Employee Management	2025-07-29 13:45:41.992318	2025-07-29 13:45:41.992318	\N	\N	\N
4dd73892-65a2-456a-8b36-a075cb538db2	employee:update	Update employees	Employee Management	2025-07-29 13:45:41.996473	2025-07-29 13:45:41.996473	\N	\N	\N
dabdbe67-1bd2-4f85-92ba-27ba7a4f9f72	employee:delete	Delete employees	Employee Management	2025-07-29 13:45:42.004347	2025-07-29 13:45:42.004347	\N	\N	\N
36390a87-7010-4c1a-8fe5-c55786f99223	address:create	Create addresses	Address Management	2025-07-29 13:45:42.009789	2025-07-29 13:45:42.009789	\N	\N	\N
7f7271ed-cdbc-4feb-9be8-4b68dda7f40e	address:read	View addresses	Address Management	2025-07-29 13:45:42.01468	2025-07-29 13:45:42.01468	\N	\N	\N
82eeddea-b55b-4263-9539-e63730d21d4b	address:update	Update addresses	Address Management	2025-07-29 13:45:42.021041	2025-07-29 13:45:42.021041	\N	\N	\N
72e44588-1711-4a2c-b75f-34d3936a965d	address:delete	Delete addresses	Address Management	2025-07-29 13:45:42.025684	2025-07-29 13:45:42.025684	\N	\N	\N
8306c04b-693d-4d80-a381-f8d8d3094332	bulk:import	Import data in bulk	Data Management	2025-07-29 13:45:42.029865	2025-07-29 13:45:42.029865	\N	\N	\N
f34c65cf-1e6d-4b33-884d-4924373d9f58	bulk:export	Export data in bulk	Data Management	2025-07-29 13:45:42.034358	2025-07-29 13:45:42.034358	\N	\N	\N
72a1abf5-8e7b-4671-bfc1-05d1dcf259bf	data:archive	Archive old data	Data Management	2025-07-29 13:45:42.040017	2025-07-29 13:45:42.040017	\N	\N	\N
3229d110-5441-4283-b4f2-442c1341d4be	data:restore	Restore archived data	Data Management	2025-07-29 13:45:42.044277	2025-07-29 13:45:42.044277	\N	\N	\N
\.


--
-- Data for Name: postal_codes; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.postal_codes (postal_code_id, region, district, location, postal_code, created_at, updated_at, deleted_at) FROM stdin;
13802289-1b99-4be2-af9d-e5829b2d8a68	Northern	Chitipa	Chitipa BOMA	101100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9552d5b4-fb01-4b19-befb-20b3ccd45735	Northern	Chitipa	TA Kameme	101101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
62c1190f-03ea-4d06-b8bf-18e1dda2a44e	Northern	Chitipa	STA Bulambya Songwe	101102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
46ae32e5-15f9-40f4-bb79-14d281a4c3ce	Northern	Chitipa	TA Mwabulambya	101103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c28a17e0-f903-45d6-b228-0352b2d04fcb	Northern	Chitipa	TA Mwenemisuku	101104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8985c056-593b-4e2a-8f83-3853b4b57652	Northern	Chitipa	STA Lwangwa	101105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b2df5d8b-2e88-4d82-8ee1-97fd5a5cb81c	Northern	Chitipa	TA Mwenewenya	101106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4cd70976-4e44-4558-b651-a1d405f6251d	Northern	Chitipa	STA Nthengatenga	101107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ce4c6f56-83c3-4394-80c9-8ace0bddde17	Northern	Chitipa	TA Nthalire	101108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4b521104-6b55-4a07-9809-e9df937f82f5	Northern	Chitipa	STA Wavikaza	101109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
71d84c01-2563-4794-b22d-abeab95d6905	Northern	Karonga	Karonga	102100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b0bd778f-638f-4812-b96d-447f6622e2b8	Northern	Karonga	Songwe	102101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b249a1ef-19a9-4d9c-b34a-8ee40fe550d8	Northern	Karonga	TA Mwakaboko	102102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e566a3fb-3ff7-43a4-a513-3f1b7f853ae0	Northern	Karonga	TA Kilupula	102103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
42d0dd3a-a8cc-4e37-88dc-398be737ac80	Northern	Karonga	TA Kyungu	102104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6b049b6d-1ff3-4d4e-8534-52ee050979aa	Northern	Karonga	TA Mwirang'ombe	102105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
06992680-a894-43d1-91df-e3feacaab11f	Northern	Karonga	TA Wasambo	102106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2b11145f-c7be-4cf3-8673-d6542306c228	Northern	Rumphi	Rumphi BOMA	103100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
52776823-87f3-4944-b08f-24a7ad39eaff	Northern	Rumphi	TA Mwalweni	103101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0b18ea05-a5c1-4229-a026-052a32867fd9	Northern	Rumphi	STA Kachulu	103102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
226c8d4d-bfad-4208-a22c-763a5173d861	Northern	Rumphi	STA Njikula	103103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fd86ef65-7bec-4cd3-90df-d396cff603b1	Northern	Rumphi	TA Mwamlowe	103104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
51f27e8a-f739-4d72-9ab6-4f80fcafc23e	Northern	Rumphi	STA Chapinduka	103105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
05ece9ab-fe03-40cc-88c6-fb08dc7d2bc8	Northern	Rumphi	TA Mwankhunikira	103106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d5b26905-5e96-4fa8-a321-31bd4f4f7c7b	Northern	Rumphi	TA Mwahenga	103107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f552ce42-928c-47c5-83f2-be4ea6fae08b	Northern	Rumphi	STA Chisovya	103108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1b1fb312-9711-4192-b46e-00de80dadaed	Northern	Rumphi	TA Chikulamayembe	103109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
76ad8119-f509-4082-bab2-007c47c3bab3	Northern	Rumphi	TA Katumbi	103110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4827bcf9-ca20-47b7-b445-a3e129310372	Northern	Rumphi	STA Zolekere	103111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a1dce80b-7ab6-495b-bcf9-5de3896cccae	Northern	Rumphi	Nyika National Park	103112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a880db72-cc61-408e-98e9-a4084fe1910c	Northern	Mzimba	Mzimba BOMA	104100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d77796bc-92d7-46dd-875f-e48666e0442f	Northern	Mzimba	TA Jaravikuba Munthali	104101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d5dbcbd6-dec2-4ded-b693-3317e4a809c0	Northern	Mzimba	TA Mtwalo	104102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d6bcecb1-45af-4c7f-ada5-70851d00aa7b	Northern	Mzimba	Ekwendeni Township	104103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b08ee17b-8eb3-459d-8eb7-d55b3d16f28e	Northern	Mzimba	TA Mpherembe	104104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3dac5cbe-291b-4577-ab76-a6eafe4fe09d	Northern	Mzimba	TA Chindi	104105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
aef939c9-5b3e-41ce-b114-1fdb2a3863e0	Northern	Mzimba	TA Kampingo Sibande	104106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a12da0d9-e08b-49ab-8b9c-0f5be21e0c71	Northern	Mzimba	TA M'mbelwa	104107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
de0b6ad0-b3ba-4cb4-9403-ca79eb030922	Northern	Mzimba	TA Mzukuzuku	104108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
afd8f061-4534-4a0e-866a-e13dfe973950	Northern	Mzimba	TA Mzikubola	104109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3a9d5a10-fe3f-4833-bcfc-be43a293c64e	Northern	Mzimba	STA Levi Jere	104110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4f02efad-a9ac-43b5-b171-f77fc4c9529b	Northern	Mzimba	Jenda Township	104111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6c03a3b9-e85b-46d2-9f05-e4a15c56356b	Northern	Mzimba	TA Mabulabo	104112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
35cc7f0f-b21c-441a-a65c-93781f6c7bf5	Northern	Mzimba	TA Khosolo Gwaza Jere	104113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0f62ce1d-2e88-4e69-9a90-a50b26fd1ad1	Northern	Mzimba	Vwaza Marsh Reserve	104114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
59f2907f-0fb0-424a-bfb2-5d0a60e9c869	Northern	Mzuzu	Mzuzu CBD	105200	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cd1ad612-3162-4563-b4e7-1fed6ff7f42f	Northern	Mzuzu	Khorongo	105201	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
19e6e17d-0898-4466-800d-2af4f8a40091	Northern	Mzuzu	Lupaso	105202	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
406ee979-0300-47a8-9230-8d366a66daeb	Northern	Mzuzu	Luwinga	105203	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9324690b-0649-421b-8bab-97846b5cab4d	Northern	Mzuzu	Mchengautuwa	105204	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b9505ab2-f7c3-4771-85d4-7d1586e82c32	Northern	Mzuzu	Chibanja	105205	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
20b79276-86c8-432c-9eaa-fa590b37bcff	Northern	Mzuzu	Katoto	105206	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
24af25ef-05ba-4228-a7bf-60052203228a	Northern	Mzuzu	Zolozolo	105207	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1e56f015-62cb-41aa-8f6a-4d319c056fac	Northern	Mzuzu	Hilltop	105208	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a291b07f-a9d1-4826-9dc8-96cee55a10aa	Northern	Mzuzu	Chiputula	105209	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e87ff9a4-0d5d-4b9f-a5d4-c55e8544cc80	Northern	Mzuzu	Mzilawaingwe	105210	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9bfb0652-2c02-4f77-b825-5eb066926213	Northern	Mzuzu	Katawa	105211	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
44e2aeba-a765-430c-92f1-1119157df199	Northern	Mzuzu	Chasefu	105212	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
eef12fe5-da21-4c36-a0ca-d19e8aa6dec8	Northern	Mzuzu	Lunyangwa	105213	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8d317099-6f76-49df-8e5d-d93f4c1294d1	Northern	Mzuzu	Kaning'ina	105214	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6a0683d2-0c6e-446e-a3e8-5519c0ded87e	Northern	Mzuzu	Masasa	105215	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d0c127a2-9a8a-4ed7-b6ca-b110435d8c60	Northern	Mzuzu	Msongwe	105216	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7b297bb7-ba28-4d58-a80f-b1caacc7efc5	Northern	Nkhata Bay	Nkhata Bay BOMA	106100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2f12a2a4-80c2-4007-a621-ea8b7190851c	Northern	Nkhata Bay	STA Mkondowe	106101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
960d773b-7095-476b-b7fa-b8cf69cb5b36	Northern	Nkhata Bay	TA Boghoyo	106102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fe412039-510c-4423-8961-3f7b0baaa341	Northern	Nkhata Bay	TA M'bwana	106103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a198f870-f755-4adb-a5e3-f982bc3e0e4e	Northern	Nkhata Bay	STA Nyaluwanga	106104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
39e3d0d8-5c07-4db2-8f1e-4ce3ea812a2c	Northern	Nkhata Bay	TA Timbiri	106105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f799d395-c778-458b-8801-7ba21e65ce06	Northern	Nkhata Bay	TA Mkumbira	106106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cc4fc4d1-a27e-4c64-82e4-435c5aa66440	Northern	Nkhata Bay	TA Mankhambira	106107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
23a80fb7-32f4-4da6-852b-d7a9c67a8a93	Northern	Nkhata Bay	TA Fukamalaza	106108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cabab94c-5cbd-481e-b1de-4b57e03361da	Northern	Nkhata Bay	TA Malanda	106109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d3203e94-b5e8-42bc-a213-84fdc931424f	Northern	Nkhata Bay	TA Kabunduli	106110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
af2fea75-3600-409a-bc95-8e0114d8e5f4	Northern	Nkhata Bay	TA Malenga Mzoma	106111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f6fd5135-3281-44d5-8531-638ef6aa0841	Northern	Nkhata Bay	TA Fukamapiri	106112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3174af5e-084e-470f-801e-d6fcb48f35ea	Northern	Nkhata Bay	TA Zilakoma	106113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
463b420f-ac46-45b5-9d13-99ab5bea0bea	Northern	Likoma	Likoma BOMA	107100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9ebf1a31-bb3b-4c7d-8ee8-76af7235b10f	Northern	Likoma	TA Mkumpha	107101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dfcd46ac-12b1-43ea-b252-45c7abd068ee	Central	Kasungu Municipality	Kasungu Central Township	201300	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2f17ff9e-85bc-4859-a010-91e700e9c1af	Central	Kasungu Municipality	Chipala	201301	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ab491509-3954-4119-bd54-b4db2e4d75dd	Central	Kasungu Municipality	Mponda	201302	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ebc89e1f-2fde-43c4-aa50-75c9c7e36bc8	Central	Kasungu Municipality	Bunda	201303	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4dd3c6b5-bcbb-49c4-b8c7-52f676a6dca2	Central	Kasungu Municipality	Belele	201304	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5d040aa7-2402-4c26-af55-d9aac6b45da7	Central	Kasungu Municipality	Chankhanga	201305	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
32513a89-c5e8-40e1-ac8c-cb9909df714e	Central	Kasungu Municipality	Kasalika	201306	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
347de0a7-1bcf-4687-b2a2-0238827b817f	Central	Kasungu Municipality	Kapalankhwazi	201307	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
246198b1-1053-43a0-9eb2-29161aeebaa4	Central	Kasungu Municipality	Chitete	201308	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c9f9d67c-ca67-4ced-be28-918d9afd0920	Central	Kasungu Municipality	Kamvunguti	201309	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
70c1ae29-8fe3-4812-bfe3-e7d1ece8bbfc	Central	Kasungu Municipality	Chimbuna	201310	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1e94125b-a119-4474-b2e3-3a3333594298	Central	Kasungu Municipality	Gundani	201311	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3a36e7bc-ca93-4a77-ba4c-2d397c89260e	Central	Kasungu Rural	STA Chisikwa	201100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ab57a344-3592-4886-a7f1-00d159df7629	Central	Kasungu Rural	TA Kaluluma	201101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
53d952e1-a318-4b30-a781-fa37fdb893d3	Central	Kasungu Rural	STA Chisinga	201102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
db931e71-c2c3-4c63-8c56-e837d05a4ff9	Central	Kasungu Rural	TA Chisemphere	201103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
861c1455-1100-49d1-a463-60ebb600f318	Central	Kasungu Rural	TA Chulu	201104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8c095a9b-26e0-4633-85f7-474afefaef8e	Central	Kasungu Rural	STA M'nyanja	201105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
612ec037-c047-4fd2-9657-47bac06a7131	Central	Kasungu Rural	TA Simlemba	201106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
88123d54-162f-4553-a7bf-72b693a0547f	Central	Kasungu Rural	STA Mphomwa	201107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5c73f322-595b-414f-bc07-1c436a6123e2	Central	Kasungu Rural	TA Kaphaizi	201108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f73c177f-890f-4b00-bb11-f30dc45c0343	Central	Kasungu Rural	TA Mwase	201109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ba8ffd57-55ef-4dee-8f00-03fd802f0ec0	Central	Kasungu Rural	TA Kaomba	201110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2cb0a1fe-e0e6-41bd-afcb-a6aca42169a3	Central	Kasungu Rural	TA Wimbe	201111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c9529ada-981e-4d86-9e87-6f3361a36613	Central	Kasungu Rural	TA Kapelula	201112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
599c9740-1c44-44cc-9701-c709a357fac6	Central	Kasungu Rural	STA Kapichira	201113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
25ad565b-c48c-4b17-b7f8-bb9fb4e4c8bc	Central	Kasungu Rural	STA Mdunga	201114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
099782dd-c879-4766-8ebf-6c9143e46e3b	Central	Kasungu Rural	TA Chilowamatambe	201115	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
70640fdf-0233-4a83-a5c4-ded6d7db8d90	Central	Kasungu Rural	STA Chambwe	201116	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
14b9ae7c-a7cf-4680-8353-0e15f7834b74	Central	Kasungu Rural	STA Mangwazu	201117	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0d1a07fc-541a-49de-8240-f7d3a961a190	Central	Kasungu Rural	STA Mawawa	201118	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5793a811-38fe-4b18-9b8c-b922fb39bc33	Central	Kasungu Rural	TA Lukwa	201119	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7370ed43-19b9-4b2c-9dbd-39db8754d0d9	Central	Kasungu Rural	TA Njombwa	201120	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
75b73dff-db00-459f-b4d1-97a20fb32396	Central	Kasungu Rural	TA Kawamba	201121	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8e352b24-bbc4-4728-b8dc-381fc0fb2f5b	Central	Kasungu Rural	STA Nthunduwala	201122	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
54b00a6a-bb5c-4a1d-80a2-5a90471f64d3	Central	Kasungu Rural	STA Chaima	201123	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ad9d419f-9a0c-4044-a721-9e5512dddfe3	Central	Kasungu Rural	TA Chidzuma	201124	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2808d552-3f9e-460e-b646-cf9299875888	Central	Kasungu Rural	TA Santhe	201125	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5559cd87-c677-4636-9f3d-02976213c2dc	Central	Kasungu Rural	Kasungu National Park	201126	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
39fb1490-1b95-46ec-99da-d7f960ce33eb	Central	Nkhotakota	Nkhotakota BOMA	202100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0e8279d2-6371-4d26-a17d-901aa94fcbf9	Central	Nkhotakota	TA Kafuzila	202101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
149f5660-630f-4364-b787-d3ca975af100	Central	Nkhotakota	TA Kanyenda	202102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
81dacf8e-f9ea-411a-98a8-1ec34446b3a3	Central	Nkhotakota	Dwangwa Township	202103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
20f139a7-7fdb-4f7f-8740-082f5857740d	Central	Nkhotakota	TA Mphonde	202104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
decd1cc2-df44-421a-8a33-5ad572ee77f8	Central	Nkhotakota	TA Malenga Chanzi	202105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d59dc1fd-28f7-4a5a-b158-b585f812bf74	Central	Nkhotakota	STA Kalimanjira	202106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
002567b6-e771-4e38-b5f5-cc6bb2edfb44	Central	Nkhotakota	TA Mwadzama	202107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
09c05bda-3a6b-4e45-ab28-8b1c027414f7	Central	Nkhotakota	TA Mwansambo	202108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8853ffd6-be57-4efb-90fe-8d0aafca900f	Central	Nkhotakota	Nkhotakota Game Reserve	202109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
488168f8-0039-447e-8080-f59435f53162	Central	Ntchisi	Ntchisi BOMA	203100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
07f94d2f-b29e-4a07-9e20-33260f6669ae	Central	Ntchisi	TA Chilooko	203101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
15598ecf-cf97-45af-986d-760007bf6100	Central	Ntchisi	TA Nthondo	203102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a4887d22-63c1-475b-848b-f807a2ffb6ce	Central	Ntchisi	TA Malenga	203103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
efd0c00a-0d8d-4919-a863-a674dd459007	Central	Ntchisi	TA Kasakula	203104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a94934d9-bc1d-4dba-b4cd-7183b328ae68	Central	Ntchisi	TA Kalumo	203105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6b875c5b-506d-4b85-930a-4c6ad6291c2c	Central	Ntchisi	TA Vuso Jere	203106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dc91731a-525f-4182-ad42-50c2f11e05e6	Central	Ntchisi	TA Chikho	203107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ca021916-8c29-412b-9c54-5f6b136cd08e	Central	Dowa	Dowa BOMA	204100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
21af3451-1dd7-4b53-93ac-07191c4e2cd7	Central	Dowa	TA Chakhaza	204101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8f91c168-b9bc-418c-a444-b469d9bf2502	Central	Dowa	TA Dzoole	204102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2a51b189-372b-4da1-9684-f8a79d2bfbb0	Central	Dowa	TA Kayembe	204103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
72931a54-e876-4e8e-97f9-1b166a3534fa	Central	Dowa	TA Mponela	204104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8acfc0b9-768d-4d77-b533-62b007b817c1	Central	Dowa	Mponela Township	204105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1ddcf60f-d807-42ee-adb3-31eb4ce5b185	Central	Dowa	TA Msakambewa	204106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e86b8887-b895-44d8-9a0c-b3512dfbe62b	Central	Dowa	TA Mkukula	204107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
524fed89-c36d-4215-a5a3-f4e56c0b600f	Central	Dowa	Lumbadzi Township	204108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
89703c9b-6324-4315-9f3c-214ac28bd297	Central	Dowa	TA Chiwere	204109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
69e1eb80-b4df-484c-8f09-ae4daa3e9401	Central	Mchinji	Mchinji BOMA	205100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a400e684-e4ce-4223-aeb1-403be0c55be4	Central	Mchinji	STA Gumba	205101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dfc20a40-ef95-42bc-91ec-440d328d57fe	Central	Mchinji	TA Mkanda	205102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bebe5fa7-efca-4c4e-a300-7defe20d4899	Central	Mchinji	TA Dambe	205103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3c4e78f7-0e6c-4104-8f8a-bb83cbb704c2	Central	Mchinji	STA Pitala	205104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
15a56da9-da0c-4a00-b84b-3651123432f9	Central	Mchinji	TA Kazyozyo	205105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fde1dbe7-e76c-43d8-876c-77a66552e54c	Central	Mchinji	STA Kapunula	205106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2ace4ac1-f529-4262-a99c-02bba6260c6d	Central	Mchinji	TA Kapondo	205107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
82eaf14c-f87d-40a9-a946-e14c7d642c79	Central	Mchinji	TA Mduwa	205108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c74df457-c497-4313-a7d6-76cd03f1ed91	Central	Mchinji	STA Nyoka	205109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f625e9e8-ec66-49e3-ad7c-49ff63283542	Central	Mchinji	TA Mlonyeni	205110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ecfa9cad-a88e-4346-8859-23ebdfa68482	Central	Mchinji	TA Zulu	205111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4de65f41-7835-47fe-9103-ffad79aeb25a	Central	Mchinji	TA Simphasi	205112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
14d4e826-6597-40eb-aa6f-aca6dc857268	Central	Mchinji	TA Mavwere	205113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
92a9777c-6d37-4e67-a72d-19b35874c9b0	Central	Lilongwe Rural	TA Khongoni	206101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8b28277f-d0d3-4192-b18b-6e346e726436	Central	Lilongwe Rural	TA Kabudula	206102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4b7c4c4f-1012-41cf-9e24-bfe1568a73f5	Central	Lilongwe Rural	TA Mtema	206103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f633c41e-d9a3-4f4e-a0d5-51da5ccaac5e	Central	Lilongwe Rural	STA Mbang'ombe	206104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
492d62cf-72d4-4099-8d0b-0ac94e28e747	Central	Lilongwe Rural	TA Chitukula	206105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
db273a88-ce3a-40cf-8bd4-be14a97d7a53	Central	Lilongwe Rural	TA Chimutu	206106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ed7e020c-543f-49aa-b81c-b4976d7c2d73	Central	Lilongwe Rural	STA Chitekwele	206107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
93b0064d-007f-4f7e-8192-3e9d9ea47bab	Central	Lilongwe Rural	TA Mazengera	206108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c0814bc0-526f-4939-8836-20923a26741d	Central	Lilongwe Rural	TA Kalumbu	206109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2144363c-1372-4746-95ec-b7386917444f	Central	Lilongwe Rural	TA Chadza	206110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a7119ba8-91da-47ae-b15d-462c986de15f	Central	Lilongwe Rural	TA Kalumba	206111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f39f11df-9d66-44f7-ac9b-28383b4cc117	Central	Lilongwe Rural	TA Tsabango	206112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5dbafc51-ecab-484b-8331-0113ea82c7de	Central	Lilongwe Rural	TA Njewa	206113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4a9886df-da47-4928-b8e2-4bf09f321fd8	Central	Lilongwe Rural	TA Malili	206114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
df7913a3-c322-4632-993c-b72bce3c8744	Central	Lilongwe Rural	TA Kalolo	206115	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9180062b-4775-4d8c-a315-e0c13f1d8d88	Central	Lilongwe Rural	TA Masumbankhunda	206116	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f97bbd2e-ae50-4663-9ebf-f7d0b10d1277	Central	Lilongwe Rural	TA Chiseka	206117	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
760951cf-ca44-4621-9dbb-a28c7476244d	Central	Lilongwe Rural	TA Masula	206118	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5d50c231-0621-439e-a409-3704b6d333c5	Central	Lilongwe Urban	Area 1	207201	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
63ca4e0e-c471-40de-881e-370c6fc10989	Central	Lilongwe Urban	Area 2	207202	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
93c6336e-adae-4601-a64e-5f9fda34bdf6	Central	Lilongwe Urban	Area 3	207203	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0b0b445a-2f3f-4ae3-b45e-a8dc91e2b5fa	Central	Lilongwe Urban	Area 4	207204	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
94831bf5-0f3b-4aba-9829-3c8cbda9f635	Central	Lilongwe Urban	Area 5	207205	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0d0dbe82-c51c-4893-aa4b-184e4ce97f32	Central	Lilongwe Urban	Area 6	207206	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
11e22eef-af70-4246-8532-215af6706a73	Central	Lilongwe Urban	Area 7	207207	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2e5280b5-a75f-4b69-8877-284094db01c5	Central	Lilongwe Urban	Area 8	207208	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9dc674d7-daaa-4510-8ecd-84b67ec6d67f	Central	Lilongwe Urban	Area 9	207209	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a737c58d-c090-4152-8d73-c9b344d2688d	Central	Lilongwe Urban	Area 10	207210	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b2c568cd-cf7d-4b44-a41e-a4babe38fd14	Central	Lilongwe Urban	Area 11	207211	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c174d987-ff25-41e0-b8da-9e9d3b17fc8d	Central	Lilongwe Urban	Area 12	207212	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
49428420-aaa5-45e8-b09a-00e458849dd8	Central	Lilongwe Urban	Area 13	207213	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b79ae8c7-062a-4844-940d-259b151ce359	Central	Lilongwe Urban	Area 14	207214	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e1bdf347-860f-4c79-bb2f-2113865eb2ca	Central	Lilongwe Urban	Area 15	207215	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7038c990-5d74-4182-b2fd-1688e05cb5ce	Central	Lilongwe Urban	Area 16	207216	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9e136c41-92a3-427b-a011-46e5dfa720d6	Central	Lilongwe Urban	Area 17	207217	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8d2724fa-cdc5-47a6-ab51-e5d63c0b5ddf	Central	Lilongwe Urban	Area 18	207218	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6bb1496f-c957-4f14-b21c-6dbf00d1b27e	Central	Lilongwe Urban	Area 19	207219	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
be416652-fe62-4a81-99ad-73694e67f0e2	Central	Lilongwe Urban	Area 20	207220	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8a80d082-49d6-49e4-94b4-476b66028bfa	Central	Lilongwe Urban	Area 21	207221	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f4cc5102-1e01-49d1-9f34-3b430e24b050	Central	Lilongwe Urban	Area 22	207222	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8055dedf-b042-49d3-829b-039e07b81ea7	Central	Lilongwe Urban	Area 23	207223	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
15ef19a4-fdb0-4122-808c-ac8cb7caf27f	Central	Lilongwe Urban	Area 24	207224	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6df08f7e-3505-4776-9025-3bf8eb686df6	Central	Lilongwe Urban	Area 25	207225	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bbf0967b-9d56-48d0-b1b0-018f4e6211a8	Central	Lilongwe Urban	Area 26	207226	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
951c86f1-3a3a-4f17-8c91-a9b3e39d3ee7	Central	Lilongwe Urban	Area 27	207227	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
df25fa62-4272-4163-a36d-046956a3b2e0	Central	Lilongwe Urban	Area 28	207228	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a5b888ad-bd76-47c2-a5ca-0379bbe65bca	Central	Lilongwe Urban	Area 29	207229	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
24fe2243-c366-44ef-88a6-81af29b53905	Central	Lilongwe Urban	Area 30	207230	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
72bcc750-4460-49e5-b9db-3f5fae2ccaf6	Central	Lilongwe Urban	Area 31	207231	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5f7117d2-9bba-4286-85a6-cd5df7ffa1c0	Central	Lilongwe Urban	Area 32	207232	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0bf59bb6-86b4-4188-86ab-b6dfe6e95f0a	Central	Lilongwe Urban	Area 33	207233	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3df4ddc4-c080-4196-8dbc-e8e33db503dc	Central	Lilongwe Urban	Area 34	207234	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b440f359-0d1f-433e-a4b8-b998e68fc8da	Central	Lilongwe Urban	Area 35	207235	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
78a1359a-d299-4ad6-b33d-e269380d68d0	Central	Lilongwe Urban	Area 36	207236	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0db5d910-88a7-4441-8dfd-87625fd1b280	Central	Lilongwe Urban	Area 37	207237	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
475929c1-38fb-405b-9731-238caf42c9cf	Central	Lilongwe Urban	Area 38	207238	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
57438506-fbe5-4695-a326-5ed29a4fc683	Central	Lilongwe Urban	Area 39	207239	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cd8e9c0b-9221-4c77-9e4d-da35c730fca7	Central	Lilongwe Urban	Area 40	207240	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fa488cd9-651d-4dd8-81c2-3c6758cdde88	Central	Lilongwe Urban	Area 41	207241	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ad9c20b7-4e76-438d-b2e6-afae7cf5de4f	Central	Lilongwe Urban	Area 42	207242	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b1a3afe0-19e4-4318-8af3-8f4dfc279844	Central	Lilongwe Urban	Area 43	207243	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d4d85cb7-304d-4e58-ac3d-4239aefe717b	Central	Lilongwe Urban	Area 44	207244	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7144b8a0-f756-496c-918f-4ddadb61ff6f	Central	Lilongwe Urban	Area 45	207245	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2f898f9a-396a-431f-ad0e-6019a1388679	Central	Lilongwe Urban	Area 46	207246	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0f91cc5c-2595-45d5-96fa-3b2f3c08c510	Central	Lilongwe Urban	Area 47	207247	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
82e041d3-7dde-48ac-94fe-38df27897936	Central	Lilongwe Urban	Area 48	207248	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b9ee8921-129e-48f7-b8de-7779b2597173	Central	Lilongwe Urban	Area 49	207249	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
974eb31c-0a18-4850-866f-599963c50a7e	Central	Lilongwe Urban	Area 50	207250	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
54aa4e8b-d645-4915-b27c-e9158e0e42b9	Central	Lilongwe Urban	Area 51	207251	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
067f1084-eee7-43c1-996a-5898f9dafa6e	Central	Lilongwe Urban	Area 52	207252	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
82fcf33c-b5f7-4960-8f40-b4d9f1505aa5	Central	Lilongwe Urban	Area 53	207253	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8e01767c-9465-48f9-bc44-5070430c1b4b	Central	Lilongwe Urban	Area 54	207254	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8a385ca5-cc2e-47e0-8c8b-54ca05da3783	Central	Lilongwe Urban	Area 55	207255	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
416c11b8-e707-48dd-86ef-0ce40011506b	Central	Lilongwe Urban	Area 56	207256	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f9aee43a-2c6d-460d-bc71-6d80d396a1ef	Central	Lilongwe Urban	Area 57	207257	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9412460e-50cf-441d-b4db-94209f82c70f	Central	Lilongwe Urban	Area 58	207258	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
44aed34b-b497-4249-b237-52840112e30a	Central	Salima	Salima Township	208100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b4cf6aa7-b57a-4c38-94f3-d50acfe5e26f	Central	Salima	TA Msosa	208101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a151add5-5ff6-4deb-830f-da488d6f53d6	Central	Salima	TA Khombedza	208102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a126a1ed-cfda-4fbc-8680-0cce39621196	Central	Salima	TA Mwanza	208103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
842e06d9-f094-40e7-a7ac-300732fe665a	Central	Salima	TA Kuluunda	208104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e00838bd-f0fa-4387-8b90-a2eced4929b0	Central	Salima	TA Karonga	208105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
430c5a2d-149f-4b5b-9bec-27ea88e890a0	Central	Salima	TA Maganga	208106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6f9fdfc9-f087-4de7-a75f-3dba5ddc7e39	Central	Salima	TA Kambwiri	208107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b9bcdc2a-335d-411e-af39-8551a5981c04	Central	Salima	TA Pemba	208108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
71282117-1603-4157-a428-852c3d9ac5ff	Central	Salima	TA Ndindi	208109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2ea8099c-dd95-4b6b-b9c6-b30c6d8b459b	Central	Salima	Chipoka Township	208110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8ddf92cf-b223-4062-ba11-e9182d4e451e	Central	Salima	TA Kambalame	208111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fbec62bc-4f41-42c7-940f-6ff689eff09d	Central	Dedza	Dedza Township	209100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
63c60051-69f2-4ee4-bb24-ab4323082257	Central	Dedza	TA Tambala	209101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b6847aca-9726-4a80-8d31-e8eb85d2c72d	Central	Dedza	TA Chauma	209102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c4229d7d-b7aa-4c2e-bbf5-13ce45e3a373	Central	Dedza	TA Kasumbu	209103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
efaf23ce-7116-4a8e-be10-88fcdaaed933	Central	Dedza	TA Kachindamoto	209104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
92009e9b-db7a-4231-a38c-f231b83459a9	Central	Dedza	TA Kamenya Gwaza	209105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8245bb0d-9c77-4da3-a68e-83f8294fa942	Central	Dedza	TA Kaphuka	209106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0afc90c4-a9a7-4da9-893b-535f301bedc9	Central	Dedza	TA Chilikumwendo	209107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3d7cb94d-dff9-473c-a323-adce404e00f1	Central	Dedza	TA Kachere	209108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dd13fc5c-80a6-4335-92b5-70f6fba5bfb6	Central	Ntcheu	Ntcheu Township	210100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3915a675-8147-4672-9f1e-fdcdab24bf9c	Central	Ntcheu	TA Masasa	210101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0196be61-c152-4661-80e3-6990f94231af	Central	Ntcheu	TA Chakhumbira	210102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ad589e87-042a-4205-b90e-f6c4eb513c34	Central	Ntcheu	TA Goodson Ganya	210103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4109df7b-c12c-4da8-9bfb-df0b6956af9d	Central	Ntcheu	TA Njolomole	210104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
48ddefe1-0945-42f0-a443-703a85ac3098	Central	Ntcheu	TA Kwataine	210105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9c22c225-001e-4c4f-a8e8-d3f38d46ccb9	Central	Ntcheu	TA Makwangwala	210106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1c769a01-9751-4f9b-a927-be029efb64df	Central	Ntcheu	STA Mkutumula	210107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ea41fff2-a242-4d34-a26d-cdc01dd6a879	Central	Ntcheu	TA Champiti	210108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cd157835-c81f-4c76-b19a-5612147adcd5	Central	Ntcheu	TA Mpando	210109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0ce1f7b2-4173-4176-885d-7ae585fc915b	Central	Ntcheu	TA Phambala	210110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5e38031f-f06f-48f7-8a8b-e850b1741b82	Central	Ntcheu	STA Tsikulamowa	210111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dabfdb1e-5583-4528-875f-4c975f4aef68	Southern	Mangochi Town	Mangochi Central Township	301400	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ac3c8a20-6ff6-4ef6-b187-90acb847c4b9	Southern	Mangochi Town	Koche	301401	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9e54dc47-e6be-452d-ae76-422a99c2be09	Southern	Mangochi Town	Thundu	301402	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c3287201-27c9-464d-bffe-b6f72a360382	Southern	Mangochi Town	Chigawe	301403	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9df9c0ec-fab6-4898-bf4c-b2fcc57109a6	Southern	Mangochi Town	Mikomwa	301404	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3654c393-8b2b-4998-9fcf-cc14cb63366b	Southern	Mangochi Town	Nkanamwano	301405	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5181feda-6ede-4d4e-9dd4-31ab71d28620	Southern	Mangochi Town	Kalungu	301406	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f44d932e-022f-4dd1-92ef-fab67ac8762e	Southern	Mangochi Town	Msukamwere	301407	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
104733a5-3ff3-4ad9-a70f-f1e24e5d853c	Southern	Mangochi Town	Mtumbwasi	301408	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b8a3ca88-67d3-4f29-afc4-c51cc5d2b069	Southern	Mangochi Town	Mwasa	301409	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
24c62d6e-47aa-4988-8a03-21475a081f72	Southern	Mangochi Town	Ndege	301410	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
20b93642-3e62-460e-935d-4dafb1eb2c02	Southern	Mangochi Town	Chikole	301411	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
73e1ef5f-c22e-4413-b3a8-3561d99117d5	Southern	Mangochi Town	Msikisi	301412	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
66c3af6a-1729-4176-83f8-ee1ec0a0670a	Southern	Mangochi Rural	STA Lulanga	301100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
308d9b27-5268-4de7-ad16-beb4e4af7aa0	Southern	Mangochi Rural	TA Makanjila	301101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a9f99d4b-cc2d-473c-a276-2616b9e02cbf	Southern	Mangochi Rural	TA Namabvi	301102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
49460035-45e1-4b0d-a363-d4bb85f110ca	Southern	Mangochi Rural	TA Katuli	301103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4d07fceb-fb1d-4e50-9b1e-8c0b66c58d8d	Southern	Mangochi Rural	TA Chowe	301104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f97c549f-5562-4274-b4f6-64fdf41eda16	Southern	Mangochi Rural	TA Jalasi	301105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
36107fbc-bb58-4f09-bfa8-f1a455c99ebd	Southern	Mangochi Rural	TA Mbwana Nyambi	301106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0638ae1e-de6d-4067-8a4b-4486c53293aa	Southern	Mangochi Rural	STA Chiunda	301107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d684a91c-0c6d-4213-921f-26715e3790a1	Southern	Mangochi Rural	TA Nankumba	301108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
90b1ff30-c317-430d-8123-b4ba23aca1da	Southern	Mangochi Rural	Monkey Bay Township	301109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9fe038af-152c-463c-a6fa-789f803ee9f6	Southern	Mangochi Rural	TA Mponda	301110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7954c769-6f7f-4124-877b-7fcff25e5029	Southern	Mangochi Rural	STA Ntonda	301111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5b14ed2a-f8f1-43ab-bcdb-118cad00051c	Southern	Mangochi Rural	TA Chilipa	301112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5332b2de-75eb-4d61-80d7-5d68af47d014	Southern	Mangochi Rural	TA Chimwala	301113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d2c09ddf-33e9-4cae-87e1-3ba709ef1391	Southern	Mangochi Rural	Lake Malawi National Park	301114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b026caf9-a16f-47d5-8677-b0f2d4073a60	Southern	Balaka	Balaka	302100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bf352e89-002e-42ed-87c2-1f1ed5b89060	Southern	Balaka	STA Kachenga	302101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0d9b6afa-b78a-412d-b9e3-9c803c0bf528	Southern	Balaka	TA Kalembo	302102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a549b76f-9279-41ba-bb15-633dc2efd740	Southern	Balaka	TA Amidu	302103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bb17fe27-58c5-4aba-9837-6f60626a49c7	Southern	Balaka	STA Toleza	302104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0fa4daeb-a45d-4153-a1b2-69f3a60e95e8	Southern	Balaka	STA Matola	302105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0efc9b98-6b50-4fe5-8d42-f28bbc393865	Southern	Balaka	TA Sawali	302106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b57df0bf-74b8-4a87-9956-6c31447d68b6	Southern	Balaka	TA Msamala	302107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8246ab37-c747-4d95-b47a-8e6cd0e4dbef	Southern	Balaka	TA Chanthunya	302108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2d069cc9-55bb-4d9b-a8a8-5b637fb49fba	Southern	Balaka	STA Phalula	302109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9c83dcc4-f2b8-49bd-a3c4-4d7fcef9f7da	Southern	Balaka	TA Nkaya	302110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ade95dd1-6ee0-47f7-9728-d9195af837be	Southern	Machinga	Machinga BOMA	303100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b021c432-1ab3-45f5-bc42-9ca9a921ab65	Southern	Machinga	Liwonde Township	303101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
70897965-ccda-44d7-bdc6-acb296404798	Southern	Machinga	TA Nyambi	303102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0b641cd4-44ec-4563-bb7d-6312a96f2d03	Southern	Machinga	STA Chesale	303103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
416d969e-b885-4caa-820b-0de0007bf6bb	Southern	Machinga	TA Ngokwe	303104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c35cb268-2fe6-4481-a539-84aea6828fd4	Southern	Machinga	TA Chikweo	303105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
77726269-8b3c-4e54-a298-c9a546580a9b	Southern	Machinga	STA Nchinguza	303106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ef58bf90-4112-4f32-8282-ff74f49d7d5e	Southern	Machinga	TA Kapoloma	303107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
30c911fb-41fd-43f0-878f-9946665bfca0	Southern	Machinga	TA Nkoola	303108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2898efe6-7dd7-40a9-bc67-0a90647374d6	Southern	Machinga	TA Chiwalo	303109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a69e6147-0886-4286-82aa-e983c76e0e30	Southern	Machinga	TA Liwonde	303110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5394ae6d-482b-408c-9410-2b3baf1baf99	Southern	Machinga	TA Kawinga	303111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d09398ab-d6e6-4811-b10d-e263b5e8fd4e	Southern	Machinga	TA Mlomba	303112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f4fd41a3-27d4-4e4c-a2ad-51674e19b3f8	Southern	Machinga	STA Nsanama	303113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3adf0fd5-3272-42d9-b3b7-0cd6c9c62dc7	Southern	Machinga	TA Mposa	303114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d31df2b4-ddb9-4cb0-92c7-6b86a492a585	Southern	Machinga	TA Chamba	303115	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
044454ca-8f80-4626-b2fe-a40226052e35	Southern	Machinga	TA Sitola	303116	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1be3b988-c2cd-482e-9c0f-76015dadaf4c	Southern	Machinga	TA Mkula	303117	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
859873ad-9ef4-4e15-8c84-6a0dcd1e3b70	Southern	Machinga	Liwonde National Park	303118	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6a116cee-c160-4a62-9a24-035c9aefc2af	Southern	Zomba Rural	STA Nkapita	304100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
38020092-cbea-4d4a-ab7c-94360f418f6c	Southern	Zomba Rural	TA Malemia	304101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a0c48603-12bf-494c-9e36-a9026579d330	Southern	Zomba Rural	TA Kuntumanji	304102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ebdc57c5-20e2-4354-a995-7ed7807b164a	Southern	Zomba Rural	TA Mkumbira	304103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
399d31cc-55a0-42fe-a2b5-27fddbc0e907	Southern	Zomba Rural	STA Nkagula	304104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c8a97d47-1469-4bc6-be68-1a8db9b4e01f	Southern	Zomba Rural	TA Mlumbe	304105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5e68a0f0-d8ef-4e37-8ae3-f7b59789b821	Southern	Zomba Rural	STA Ntholowa	304106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2687deda-a5d8-4630-91ea-6e11eaa7bee2	Southern	Zomba Rural	TA Chikowi	304107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2b4969b0-99d0-4799-bf1f-f65a8848c810	Southern	Zomba Rural	TA Mwambo	304108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
091f0059-62d2-44c4-b568-7be1c89782f4	Southern	Zomba Rural	STA Ngwelero	304109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4160deae-9ae2-42f1-b83b-911bb78940ed	Southern	Zomba Rural	TA Mbiza	304110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4544b8ce-29e0-44e5-82fd-c6a6a1f62737	Southern	Zomba Rural	Zomba Mountain Reserve	304111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
90f4b062-ee88-4fc7-b7f8-fc9e9ef3546c	Southern	Zomba Urban	Zomba CBD	305200	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cc718e27-75fb-4c43-8a9d-8a012723c9f5	Southern	Zomba Urban	Ndola	305201	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3a34f5b3-bb2b-435c-813c-896e0f77469c	Southern	Zomba Urban	Zakazaka	305202	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d4641b3b-7334-4297-88ba-19e5e6a1611d	Southern	Zomba Urban	St Mary	305203	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
23b8a00f-2935-42e3-a8fa-6e51a9c6798f	Southern	Zomba Urban	Nandolo	305204	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d248a6a1-95e3-4385-a0f6-f33a0e89594c	Southern	Zomba Urban	Chirunga	305205	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
305cadca-1b88-4af3-bbd3-ae41a9731ab3	Southern	Zomba Urban	Masongola	305206	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ed7fb029-317c-43c4-a805-c2745378ec9b	Southern	Zomba Urban	Mangasanja	305207	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8c456518-19fb-40aa-af6b-f68cc6d40ca8	Southern	Zomba Urban	Mulunguzi	305208	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b0393c96-cc7c-49e9-a09d-3bc90275db44	Southern	Zomba Urban	Old Naisi	305209	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
cc586d52-48b3-494c-86f0-28cb41b1d459	Southern	Zomba Urban	Malonje	305210	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
16e1ea29-7d68-46d9-90f2-447be2799187	Southern	Zomba Urban	Chinamwali	305211	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9777285a-fc80-4532-94d5-ae00cfb41f7f	Southern	Zomba Urban	Matawale	305212	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
690ab521-5a19-464e-aaa2-e17822c18640	Southern	Zomba Urban	Thom Allan	305213	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c7115697-d098-49ad-a03d-99f031c9934d	Southern	Zomba Urban	Mpunga	305214	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
70257423-c1d0-4a19-89ad-12544f7d5822	Southern	Zomba Urban	Sadzi	305215	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4565af5c-b773-483a-be9e-737808ef03e6	Southern	Zomba Urban	Mponda Bwino	305216	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f1b150b9-755a-470f-aea2-c7fcbbc4823a	Southern	Zomba Urban	Three Miles	305217	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c06be946-86f5-4ca1-866e-5e8138c89073	Southern	Zomba Urban	Mtiya	305218	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bd7d6480-afe9-4c28-a5b0-7de9e793c6ba	Southern	Zomba Urban	Cobbe	305219	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bf7451c7-17be-4dd9-b702-1e6fd031cbb7	Southern	Chiradzulu	Chiradzulu BOMA	306100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6fed0169-e698-4818-8b00-65d02bb6f2de	Southern	Chiradzulu	TA Chitera	306101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1aeff08e-1026-4737-859a-c7a43da9c7ce	Southern	Chiradzulu	TA Mpama	306102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ca00aab0-8936-400c-931c-da3696207e0d	Southern	Chiradzulu	TA Nchema	306103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f77318a6-5efb-4188-a188-e4cc424e4e72	Southern	Chiradzulu	STA Sandaraki	306104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
984a34b1-e7e4-4d9d-973d-3af78c608a6e	Southern	Chiradzulu	TA Kadewere	306105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
539693c7-90bd-4c54-a1cf-d9af1a60c8ba	Southern	Chiradzulu	STA Onga	306106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8914340e-3b70-461c-8115-33340f33a85b	Southern	Chiradzulu	TA Likoswe	306107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
78bd3c62-ebc1-46ab-9b11-d97a94a8f1e6	Southern	Chiradzulu	STA Mpunga	306108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a8576b37-a9b1-475e-a103-be0f601a405a	Southern	Chiradzulu	TA Nkalo	306109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
67fc3764-8c73-470c-ad84-276c1deaec71	Southern	Phalombe	Phalombe BOMA	307100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4eefa05e-1c63-4d02-981f-ed5fa01320af	Southern	Phalombe	Migowi Township	307101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
982a496e-00eb-4029-b5b4-bea472ab51cb	Southern	Phalombe	TA Chiwalo	307102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
54b7c643-ce8b-4ca9-99a5-1cdc64f5db38	Southern	Phalombe	TA Jenala	307103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7be362a4-8572-49ae-bfb4-6689dbe70b6b	Southern	Phalombe	TA Mkhumba	307104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
abf84f6c-2733-42e0-95ef-b2efa9f8151a	Southern	Phalombe	TA Kaduya	307105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e7d64949-ebeb-47ba-abf9-b3b72220d890	Southern	Phalombe	TA Nazombe	307106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
640ccd00-36d0-4e45-8a2f-dbf3d10a9890	Southern	Phalombe	TA Nkhulambe	307107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6e62fc49-d987-4e49-afaa-f0fb90e18a3a	Southern	Mulanje	Mulanje Boma	308100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ddf61710-6afa-48f8-96a1-32e8e7bf4c96	Southern	Mulanje	TA Juma	308101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4da22fa6-4255-4db8-8b90-fc7672ad7854	Southern	Mulanje	TA Nkanda	308102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6f141c2a-8562-46f7-8811-5e7f229cfe3c	Southern	Mulanje	TA Nthiramanja	308103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
68b40b36-de2e-42f1-951a-e16f24029ed3	Southern	Mulanje	TA Chikumbu	308104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
fb2c0ce1-0542-490c-9bb1-c3c8df1bb71e	Southern	Mulanje	STA Tombondiya	308105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
61ef69c2-3add-43e8-8afc-d2b0420bbbcb	Southern	Mulanje	TA Mabuka	308106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
64150d51-b05b-4cfa-8383-821902153e2c	Southern	Mulanje	STA Sunganinzeru	308107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0a9e2bc5-6838-4645-bf5b-97cadb0bdf40	Southern	Mulanje	TA Laston Njema	308108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
65ab369e-b5bf-4290-a003-91abfd3d29de	Southern	Mulanje	Mulanje Mountain Reserve	308109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8e40fd7a-0988-4e61-aa1f-563263f96a95	Southern	Luchenza Municipality	Luchenza Township	309300	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
93d79db7-9a82-4461-b3ba-23bdbede5e07	Southern	Thyolo	Thyolo Boma	310100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7f83c1fe-7d3c-4861-88f1-c8566d353dd3	Southern	Thyolo	TA Bvumbwe	310101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9186e1d0-a79f-4d50-b6da-df35e7bc7615	Southern	Thyolo	TA Thomas	310102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c5781759-a4a4-4eaa-9566-7d87e6bcc280	Southern	Thyolo	TA Mphuka	310103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
879f9e92-c8b0-4a10-8932-0c820573df4d	Southern	Thyolo	STA Boyidi	310104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
683ac757-663e-48e9-9ae4-eeef89f79e6e	Southern	Thyolo	TA Chimaliro	310105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a0146857-9ba6-48c4-8d59-2c88f23e6698	Southern	Thyolo	TA Ngolongoliwa	310106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e90caaef-bc39-471c-bfec-98c6ddf3a928	Southern	Thyolo	TA Nanseta	310107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e77b877a-0d6a-419f-8834-d8bfd6a4232c	Southern	Thyolo	STA Ndalama	310108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
121c5f4e-26ca-477a-a08a-848a96b984a0	Southern	Thyolo	TA Nchilamwela	310109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9424a12f-d1cc-40e6-8767-378da5c7d58f	Southern	Thyolo	TA Kapichi	310110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e13896b6-1d19-439c-9f32-b5a08d00036a	Southern	Thyolo	TA Khwetemule	310111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
64a2f6f6-601e-4f79-aeaa-7e670d0f8646	Southern	Thyolo	TA Changata	310112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
709f9643-cdcf-49aa-afc3-1d2bb9f8a73f	Southern	Thyolo	STA Mbawela	310113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
eaf6529f-e653-4a7b-8b34-fbe5178d5080	Southern	Thyolo	TA Nsabwe	310114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1278bd17-7720-4fc8-8692-ef5a30348d51	Southern	Thyolo	STA Thukuta	310115	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
58dd6a9a-dc84-4a6c-8635-5c40426abc4d	Southern	Blantyre Urban	Blantyre CBD	312200	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2603b802-a286-481f-87b7-2b871eb16970	Southern	Blantyre Urban	Mapanga	312201	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c64fe7ca-9395-427c-a140-9f9129f60bbf	Southern	Blantyre Urban	Kachere	312202	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1ff78146-1cd6-4747-8b0d-5382762638af	Southern	Blantyre Urban	Maone	312203	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
622350fc-fc2f-4f37-b36d-18379e87c6cc	Southern	Blantyre Urban	Nkolokoti	312204	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7a93c814-8143-4355-8b68-d6c89c717eaa	Southern	Blantyre Urban	Ndirande	312205	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dcc4aabc-48c6-43f9-a26f-d7d824894912	Southern	Blantyre Urban	South Lunzu	312206	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5f172987-5e2d-47f4-b417-bf632d6bae7b	Southern	Blantyre Urban	Kameza	312207	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0eebdeed-8cf9-4e63-9473-1b3569257552	Southern	Blantyre Urban	Chirimba	312208	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0dec75cb-8ba7-4c41-84af-75b26deafd0f	Southern	Blantyre Urban	Chemusa	312209	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
38026dc8-2472-41a2-a022-c9998f1a6281	Southern	Blantyre Urban	Mbayani	312210	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0c05c667-70a8-4795-82af-50437763b2a6	Southern	Blantyre Urban	Nyambadwe	312211	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
7fd32ceb-9a48-48c5-97cb-cc30abb79ea3	Southern	Blantyre Urban	Chilomoni	312212	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c16aea33-4570-4234-9d15-d0ee4d0c2e82	Southern	Blantyre Urban	Namiwawa	312213	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
1731386f-8531-4010-af1e-ae3950f4bd30	Southern	Blantyre Urban	Sunnyside	312214	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
48a0eaa3-b9e6-4a69-b724-ff98c6e94b66	Southern	Blantyre Urban	Nancholi	312215	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3a51c3f8-7a47-4745-844c-7bc161650561	Southern	Blantyre Urban	Zingwangwa	312216	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c9f59822-dd20-4c70-9ba8-c09c127c8e93	Southern	Blantyre Urban	Chilobwe	312217	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0ae9b570-75b1-4283-8fc7-6d53b94e864b	Southern	Blantyre Urban	Chimwankhunda	312218	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a530a685-7f19-412a-8d46-4dcbac286810	Southern	Blantyre Urban	Manja	312219	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
46411f32-6fd2-4adf-8408-8d2753ad75c1	Southern	Blantyre Urban	Nkolokosa	312220	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
675d826b-37bd-45cc-9843-e1c493de8f7e	Southern	Blantyre Urban	Chitawira	312221	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b7d3ebaa-f9f0-4c2a-ae66-99ea0117eae1	Southern	Blantyre Urban	Naperi	312222	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8947be48-1d39-4ab0-b9b8-a6e5ba0f1f71	Southern	Blantyre Urban	Mount Pleasant	312223	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2a1c5240-dd02-45c6-b17d-e6451b4e2d56	Southern	Blantyre Urban	Mandala	312224	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
3c57843f-e5e5-46b2-9ec3-52e67a2e91be	Southern	Blantyre Urban	Chichiri	312225	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bf62939b-2f2d-4717-a8f6-5eabaedbbd94	Southern	Blantyre Urban	Chinyonga	312226	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
43d620e1-2ec3-4005-b229-57550ad71dd7	Southern	Blantyre Urban	Kanjedza	312227	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c9719c20-d481-402e-9e67-6020029ed4ab	Southern	Blantyre Urban	Mudi	312228	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
5165800b-1fea-4fae-8402-dac33c6c49f8	Southern	Blantyre Urban	Limbe	312229	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6d7ab69c-afac-40ef-a45c-220076e618d5	Southern	Blantyre Urban	Mpingwe	312230	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c328a87b-ac2d-4e26-ac1c-77f501e24742	Southern	Blantyre Urban	Bangwe	312231	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6f16c1e7-539d-41d7-9dd9-4a52e9a7328d	Southern	Blantyre Urban	Namiyango	312232	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
299465d0-9d7c-431a-b1f1-e6c239bdbfa0	Southern	Blantyre Urban	BCA	312233	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
27bf6661-df30-488c-ad73-bb167287d008	Southern	Blantyre Urban	Misesa	312234	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
25ba4577-faf1-4f16-8c69-deabf56c62ee	Southern	Blantyre Urban	Chiwembe	312235	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
61be5e13-3d38-46cd-903f-db17b03b6ded	Southern	Blantyre Urban	Angelogothere	312236	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6cc29968-c32e-4468-8562-0b328ebb9624	Southern	Blantyre Urban	Che Somba	312237	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c67da82b-9899-4323-92f8-a8328ed3cee5	Southern	Blantyre Urban	Chigumula	312238	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
04981c72-ed53-4cac-82bc-55211270d496	Southern	Blantyre Rural	TA Chigaru	311100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c3e13da0-41af-4b9e-85fc-e19ed93f7a4b	Southern	Blantyre Rural	TA Lundu	311101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
91323686-0f36-4d2c-ba85-d01188f6e44e	Southern	Blantyre Rural	TA Kunthembwe	311102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
f49184de-456c-42be-a966-fcba4bd427a2	Southern	Blantyre Rural	TA Kuntaja	311103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
0c57fbc7-f794-49c7-871f-50b6693879e9	Southern	Blantyre Rural	Chileka	311104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
2ebd6600-1285-4a04-989a-36c007d7628c	Southern	Blantyre Rural	TA Kapeni	311105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
13213960-1dec-4f38-88de-298dad34702b	Southern	Blantyre Rural	Lunzu Township	311106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9f7d30aa-b17d-442a-b716-d9b2c152dc01	Southern	Blantyre Rural	TA Makata	311107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
93f0098d-28d6-4e31-a4b1-333d7b296c8e	Southern	Blantyre Rural	TA Machinjiri	311108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
14433ceb-8f58-424b-a41b-9d2f07ab7fe1	Southern	Blantyre Rural	TA Somba	311109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c827d851-77d2-4b3c-847d-afcd20f631bf	Southern	Blantyre Rural	TA Mpemba	311110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
de8732e8-67b1-4de1-b152-785b2658082c	Southern	Neno	Neno BOMA	313100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
d4e4c7c8-9450-4a44-8119-e62f36f15c54	Southern	Neno	TA Dambe	313101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9cf529ec-8d0a-4f0c-82c8-942180fa5222	Southern	Neno	TA Chekucheku	313102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
41dc6829-840f-4218-aa77-47bc7cf39fa9	Southern	Neno	TA Symon Likongwe	313103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6fea987a-2d28-4861-8dcc-3b3e696e2bb1	Southern	Neno	Zalewa Township	313104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9a0b1222-1d2f-4a95-bdaa-a5d99f93b1c7	Southern	Neno	TA Mlauli	313105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
dcdac110-3bf7-42d5-bc24-6cd99206f8a1	Southern	Mwanza	Mwanza BOMA	314100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
af9b7643-7efa-4cf9-81c5-0ffd4c0748e6	Southern	Mwanza	TA Kanduku	314101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
944547b5-e285-45bf-928c-52f1111c4e8a	Southern	Mwanza	TA Nthache	314102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
23361ac3-84b6-4b3d-af6f-44f80643ecd8	Southern	Mwanza	STA Govati	314103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
be0d5a3e-7ecd-412c-a132-c2cd5623af25	Southern	Chikwawa	Chikwawa BOMA	315100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ef20c9ed-067d-4d41-af34-aa031de755b9	Southern	Chikwawa	TA Chapananga	315101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
c4de3f31-7812-434a-bfec-29163aef8920	Southern	Chikwawa	TA Kasisi	315102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
eb10957f-e073-46d1-ad27-b9d32e564ea3	Southern	Chikwawa	TA Mlilima	315103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
16df683f-8ec3-4a05-b504-24a1229ee79f	Southern	Chikwawa	TA Katunga	315104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
62e5a037-aa91-4554-8397-4db238eeee31	Southern	Chikwawa	TA Maseya	315105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b34075e4-9d19-4893-8df0-3fc9175a7fee	Southern	Chikwawa	STA Ndakwela	315106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
e99e9e02-e5e2-4094-824c-7611d5018692	Southern	Chikwawa	TA Lundu	315107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
07871c8d-d9b1-4359-bd34-9cbaf96dfb63	Southern	Chikwawa	Nchalo Township	315108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
48596d8e-9e72-4fbd-9145-83741bc7270f	Southern	Chikwawa	TA Makhwira	315109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
b9488f7c-33e9-4981-b777-daae1403e1c6	Southern	Chikwawa	TA Ngabu	315110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
00e8e355-b1dc-4c30-82a5-76da697b772b	Southern	Chikwawa	Ngabu Township	315111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ce4ad952-4507-49ec-8fe1-8de3389aa760	Southern	Chikwawa	TA Ngowe	315112	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
8802b7b3-76f5-458c-a46e-9384415c43f2	Southern	Chikwawa	STA Masache	315113	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ded864e7-bf07-49c6-ae27-18ca351c7160	Southern	Chikwawa	Lengwe National Park	315114	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
91b0eb07-3a76-473c-a70d-ecf93e613804	Southern	Chikwawa	Majete Game Reserve	315115	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
84e0fa64-2c86-46b1-a6b7-dea11bf0cab2	Southern	Nsanje	Nsanje BOMA	316100	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ee12d2c5-8808-4545-9ea6-2986c94efd17	Southern	Nsanje	TA Mlolo	316101	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
6c7f0dbf-0e80-4bcb-ad5b-a3662d109eab	Southern	Nsanje	TA Mbenje	316102	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
ab323bae-8151-4c3e-afbd-913427a3beea	Southern	Nsanje	Bangula Township	316103	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
13e657a6-fa96-4930-a10d-b8029675ff97	Southern	Nsanje	TA Tengani	316104	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bb49ff75-b27b-413c-8d2f-36c7cb329a79	Southern	Nsanje	TA Malemia	316105	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
389ed277-7023-4bdb-b5ae-4a53b98ceeb0	Southern	Nsanje	TA Ngabu	316106	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
bd5f8020-38e8-47ca-8aca-31fbad1e3fe4	Southern	Nsanje	TA Makoko	316107	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
4cc31265-eeab-47d5-a811-984c974a07d0	Southern	Nsanje	TA Chimombo	316108	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
de2c6aed-582e-4eb3-8b20-b9370a8bdea2	Southern	Nsanje	TA Nyachikadza	316109	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
a760d848-c819-4783-9dca-ca6dafd75aeb	Southern	Nsanje	TA Ndamera	316110	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
9a5bb86a-85a5-40f1-98ea-cd009a6b254f	Southern	Nsanje	Mwabvi Game Reserve	316111	2025-07-29 13:45:45.211044	2025-07-29 13:45:45.211044	\N
\.


--
-- Data for Name: professional_services; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.professional_services (professional_services_id, application_id, consultants, service_providers, technical_support, maintenance_arrangements, professional_partnerships, outsourced_services, quality_assurance, training_programs, created_at, updated_at, created_by, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: proof_of_payments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.proof_of_payments (proof_id, transaction_reference, amount, currency, payment_method, payment_date, document_path, original_filename, file_size, mime_type, status, notes, review_notes, reviewed_by, reviewed_at, payment_id, submitted_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.role_permissions (role_id, permission_id) FROM stdin;
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	7fe3e071-1430-43b0-b7b8-54f3ffc6e5e9
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	a06cbbee-232f-4478-9610-8367b4f9ab65
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	8a986107-b9b2-48dd-8379-f7cdf456e017
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	7fbad1e9-9da6-49ff-843f-79adf7028315
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	502f1174-ec14-449a-8ae8-98ffd75619c3
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	a6834152-8ad7-49ed-b72f-5f05db23d33d
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	e81b2e5b-4cd5-495c-94dd-38fc25a383a1
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	4330a9c9-960f-4337-997e-fddcbff70493
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	75785afe-a63d-4e81-bbc4-c3ad88326a81
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	08364d9b-2c7b-4afc-879e-7a7d3107df8b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	ab9f042e-1361-4fbd-9248-7954cf5315c4
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	046821b6-615a-4622-8f85-87931ba716b5
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	92c36138-32a0-405e-b742-01284ec55f8a
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	7150cac2-b300-4124-bd25-8734305e993c
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	0102178b-a2d2-4767-a2ef-caae92e6de81
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c37659bd-955a-47e0-b1f8-8bf59da0bba9
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	58b38f59-b3e4-43d6-9e73-b3824861a4fe
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	fc8cc641-904b-4d5e-a576-4e700bdd2b8e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	ab449e9a-9346-4d2b-baf7-fd1e79edbb40
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	96937ee3-d213-4035-b4f6-6c5afb1f8d48
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	f9de64e1-db18-4db6-8fe7-0bcf9b2a7708
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	f95c089d-8172-4170-ad7f-8b0d7db13347
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	edcbe658-40c2-420b-adf4-42051bbe52c8
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	4b8c6178-03ca-47d4-87db-8f810f059ecc
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	08b00ebb-ab24-4a72-9c65-143a6b5c867c
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	fb064bd4-3933-4565-ba30-8f36f9d75e30
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3ec35e7d-7de0-413a-b99a-d52568f7977e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	8a64b712-811c-4374-9115-31b2b6eade4b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	de11c81a-9d9d-4770-9a38-6c1c4a47779e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	44dd4463-0079-4576-9d0e-a51bf0335741
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5e88688b-4441-433f-aaaf-efb610fd8bac
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	a0e13577-dfed-453c-b738-233517c30519
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	64fb308c-349b-4852-aba8-64f6e229d11b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5ed56e01-356b-402c-b8c0-2eb7b41ded6a
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	66291ff2-b7b4-4fd1-8903-ea595173eb71
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	2436b40c-d468-437f-b862-422355979b0e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	192ef84f-617d-44b6-94b8-2240fb96aaf5
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	642f2728-6ffb-49b1-ae92-df16d8361c35
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	8eeb404f-bfcc-47bc-975c-dfac51ee14bb
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	bfefdccb-9f9d-4702-a2a5-23630e8b7429
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	7b03d8f6-5c1e-48da-bfbe-b65c0b1ac558
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	915498a1-1564-49ba-9c14-c8128ee38db0
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	52adf122-d1af-4c4d-b123-97e2550fdb5b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	4887f9f9-3ab7-4aa4-965f-edec49cae314
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	293af26d-7db7-4464-a057-fc9c0922773f
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	cc4c9507-a7b6-4c48-bb56-4fd65d60dfe0
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	ea222a32-3bf9-4a91-9865-3bf4107e68af
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	081cab8f-6cb6-447c-b8eb-ddb5d472499e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	d41c0f8e-7e08-4eb1-b55e-c822d24aebc7
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	cfff2c0a-fa8d-4dc2-8b49-4d3e9b55fe03
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	0b35c180-535b-45e7-b128-d28c07bc9f3d
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3c89cdde-2eb1-4bde-8f0b-7fd63b0bb5ef
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5efe6187-955e-4cdb-91b0-b3cf48103e4c
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	0d27fd63-ff42-4639-aa03-8b829d8978ac
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3cf4db6f-1ef1-4171-87f4-265070ec681e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	caca1a73-9de4-4308-b430-407483177e4b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5fcad0f7-25e5-4678-88c7-df2a31c5c679
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	74a7acb8-42ab-4137-9b22-b836c1224d0e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c62a7da6-247c-4528-b20d-b54114582f11
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	1a00b248-6607-45b8-ac91-81df06dec6a6
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3671cdeb-54c4-4a1c-b737-ed45eb59f752
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	ef99445f-99a0-4ba4-aa62-a8a8b0fe095c
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	74d4195b-c21b-4d25-9460-cee11dd518ee
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c3e111c4-6172-4e99-ae44-fde9a1d11b90
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	33362c71-d89f-4182-87c5-47187b974b03
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	c67b5b1e-18cb-4c5e-a8a9-130c8e30cfec
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	04fc3f29-bac1-4314-aaf8-27eac2f0fc76
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	9390ff55-a1fe-4e05-bc48-569edeb36e94
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5f0780a6-406a-4264-937b-0309cb79296f
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	9318aedd-125b-4345-9f5b-20c2ccaab193
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	19a17fcb-b53d-4367-8c99-3e0afa4daee1
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	cd0c0418-8288-4783-a7c2-66208091d0a8
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	2d77a09e-9307-4f89-a214-f69ca88bda8d
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	856c27b0-a1e6-4800-925e-31abf49145f0
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	505541a9-9867-4f59-98ca-bc4081f3ae42
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	b59993a9-55ca-4cd5-8639-cae2239f5428
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	5ac2b779-ca1c-4e59-baca-f721e2d4bba9
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	4162ca34-07a9-4aee-8319-b9de3f11f138
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	f9ce287e-47e6-4767-9e67-a25ef8621cf7
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	dfcd4e31-2cdd-41c5-b4c5-c0b10fb96517
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	4dd73892-65a2-456a-8b36-a075cb538db2
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	dabdbe67-1bd2-4f85-92ba-27ba7a4f9f72
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	36390a87-7010-4c1a-8fe5-c55786f99223
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	7f7271ed-cdbc-4feb-9be8-4b68dda7f40e
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	82eeddea-b55b-4263-9539-e63730d21d4b
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	72e44588-1711-4a2c-b75f-34d3936a965d
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	8306c04b-693d-4d80-a381-f8d8d3094332
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	f34c65cf-1e6d-4b33-884d-4924373d9f58
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	72a1abf5-8e7b-4671-bfc1-05d1dcf259bf
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	3229d110-5441-4283-b4f2-442c1341d4be
9eeca02d-18ab-41af-848c-a96fd6b21a20	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
9eeca02d-18ab-41af-848c-a96fd6b21a20	046821b6-615a-4622-8f85-87931ba716b5
9eeca02d-18ab-41af-848c-a96fd6b21a20	92c36138-32a0-405e-b742-01284ec55f8a
9eeca02d-18ab-41af-848c-a96fd6b21a20	7150cac2-b300-4124-bd25-8734305e993c
9eeca02d-18ab-41af-848c-a96fd6b21a20	0102178b-a2d2-4767-a2ef-caae92e6de81
9eeca02d-18ab-41af-848c-a96fd6b21a20	c37659bd-955a-47e0-b1f8-8bf59da0bba9
9eeca02d-18ab-41af-848c-a96fd6b21a20	58b38f59-b3e4-43d6-9e73-b3824861a4fe
9eeca02d-18ab-41af-848c-a96fd6b21a20	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
9eeca02d-18ab-41af-848c-a96fd6b21a20	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
9eeca02d-18ab-41af-848c-a96fd6b21a20	fb064bd4-3933-4565-ba30-8f36f9d75e30
9eeca02d-18ab-41af-848c-a96fd6b21a20	3ec35e7d-7de0-413a-b99a-d52568f7977e
9eeca02d-18ab-41af-848c-a96fd6b21a20	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
9eeca02d-18ab-41af-848c-a96fd6b21a20	8a64b712-811c-4374-9115-31b2b6eade4b
9eeca02d-18ab-41af-848c-a96fd6b21a20	de11c81a-9d9d-4770-9a38-6c1c4a47779e
9eeca02d-18ab-41af-848c-a96fd6b21a20	44dd4463-0079-4576-9d0e-a51bf0335741
9eeca02d-18ab-41af-848c-a96fd6b21a20	915498a1-1564-49ba-9c14-c8128ee38db0
9eeca02d-18ab-41af-848c-a96fd6b21a20	52adf122-d1af-4c4d-b123-97e2550fdb5b
9eeca02d-18ab-41af-848c-a96fd6b21a20	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
9eeca02d-18ab-41af-848c-a96fd6b21a20	4887f9f9-3ab7-4aa4-965f-edec49cae314
9eeca02d-18ab-41af-848c-a96fd6b21a20	293af26d-7db7-4464-a057-fc9c0922773f
9eeca02d-18ab-41af-848c-a96fd6b21a20	0b35c180-535b-45e7-b128-d28c07bc9f3d
9eeca02d-18ab-41af-848c-a96fd6b21a20	3cf4db6f-1ef1-4171-87f4-265070ec681e
9eeca02d-18ab-41af-848c-a96fd6b21a20	74a7acb8-42ab-4137-9b22-b836c1224d0e
9eeca02d-18ab-41af-848c-a96fd6b21a20	c62a7da6-247c-4528-b20d-b54114582f11
9eeca02d-18ab-41af-848c-a96fd6b21a20	1a00b248-6607-45b8-ac91-81df06dec6a6
9eeca02d-18ab-41af-848c-a96fd6b21a20	3671cdeb-54c4-4a1c-b737-ed45eb59f752
9eeca02d-18ab-41af-848c-a96fd6b21a20	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
6d265350-2c69-4985-b555-824e50e430d5	92c36138-32a0-405e-b742-01284ec55f8a
6d265350-2c69-4985-b555-824e50e430d5	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
6d265350-2c69-4985-b555-824e50e430d5	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
6d265350-2c69-4985-b555-824e50e430d5	fb064bd4-3933-4565-ba30-8f36f9d75e30
6d265350-2c69-4985-b555-824e50e430d5	8a64b712-811c-4374-9115-31b2b6eade4b
6d265350-2c69-4985-b555-824e50e430d5	66291ff2-b7b4-4fd1-8903-ea595173eb71
6d265350-2c69-4985-b555-824e50e430d5	8eeb404f-bfcc-47bc-975c-dfac51ee14bb
6d265350-2c69-4985-b555-824e50e430d5	915498a1-1564-49ba-9c14-c8128ee38db0
6d265350-2c69-4985-b555-824e50e430d5	52adf122-d1af-4c4d-b123-97e2550fdb5b
6d265350-2c69-4985-b555-824e50e430d5	293af26d-7db7-4464-a057-fc9c0922773f
6d265350-2c69-4985-b555-824e50e430d5	cfff2c0a-fa8d-4dc2-8b49-4d3e9b55fe03
6d265350-2c69-4985-b555-824e50e430d5	0b35c180-535b-45e7-b128-d28c07bc9f3d
6d265350-2c69-4985-b555-824e50e430d5	3c89cdde-2eb1-4bde-8f0b-7fd63b0bb5ef
6d265350-2c69-4985-b555-824e50e430d5	74d4195b-c21b-4d25-9460-cee11dd518ee
ac8768fe-c325-4186-9a37-48e0d28c8823	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
ac8768fe-c325-4186-9a37-48e0d28c8823	046821b6-615a-4622-8f85-87931ba716b5
ac8768fe-c325-4186-9a37-48e0d28c8823	92c36138-32a0-405e-b742-01284ec55f8a
ac8768fe-c325-4186-9a37-48e0d28c8823	7150cac2-b300-4124-bd25-8734305e993c
ac8768fe-c325-4186-9a37-48e0d28c8823	0102178b-a2d2-4767-a2ef-caae92e6de81
ac8768fe-c325-4186-9a37-48e0d28c8823	c37659bd-955a-47e0-b1f8-8bf59da0bba9
ac8768fe-c325-4186-9a37-48e0d28c8823	58b38f59-b3e4-43d6-9e73-b3824861a4fe
ac8768fe-c325-4186-9a37-48e0d28c8823	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
ac8768fe-c325-4186-9a37-48e0d28c8823	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
ac8768fe-c325-4186-9a37-48e0d28c8823	fb064bd4-3933-4565-ba30-8f36f9d75e30
ac8768fe-c325-4186-9a37-48e0d28c8823	3ec35e7d-7de0-413a-b99a-d52568f7977e
ac8768fe-c325-4186-9a37-48e0d28c8823	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
ac8768fe-c325-4186-9a37-48e0d28c8823	8a64b712-811c-4374-9115-31b2b6eade4b
ac8768fe-c325-4186-9a37-48e0d28c8823	de11c81a-9d9d-4770-9a38-6c1c4a47779e
ac8768fe-c325-4186-9a37-48e0d28c8823	44dd4463-0079-4576-9d0e-a51bf0335741
ac8768fe-c325-4186-9a37-48e0d28c8823	915498a1-1564-49ba-9c14-c8128ee38db0
ac8768fe-c325-4186-9a37-48e0d28c8823	52adf122-d1af-4c4d-b123-97e2550fdb5b
ac8768fe-c325-4186-9a37-48e0d28c8823	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
ac8768fe-c325-4186-9a37-48e0d28c8823	4887f9f9-3ab7-4aa4-965f-edec49cae314
ac8768fe-c325-4186-9a37-48e0d28c8823	293af26d-7db7-4464-a057-fc9c0922773f
ac8768fe-c325-4186-9a37-48e0d28c8823	0b35c180-535b-45e7-b128-d28c07bc9f3d
ac8768fe-c325-4186-9a37-48e0d28c8823	3cf4db6f-1ef1-4171-87f4-265070ec681e
ac8768fe-c325-4186-9a37-48e0d28c8823	74a7acb8-42ab-4137-9b22-b836c1224d0e
ac8768fe-c325-4186-9a37-48e0d28c8823	c62a7da6-247c-4528-b20d-b54114582f11
ac8768fe-c325-4186-9a37-48e0d28c8823	1a00b248-6607-45b8-ac91-81df06dec6a6
ac8768fe-c325-4186-9a37-48e0d28c8823	3671cdeb-54c4-4a1c-b737-ed45eb59f752
ac8768fe-c325-4186-9a37-48e0d28c8823	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
243ddf89-bd8d-4b0f-a797-8fa58391cabd	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
243ddf89-bd8d-4b0f-a797-8fa58391cabd	046821b6-615a-4622-8f85-87931ba716b5
243ddf89-bd8d-4b0f-a797-8fa58391cabd	92c36138-32a0-405e-b742-01284ec55f8a
243ddf89-bd8d-4b0f-a797-8fa58391cabd	7150cac2-b300-4124-bd25-8734305e993c
243ddf89-bd8d-4b0f-a797-8fa58391cabd	0102178b-a2d2-4767-a2ef-caae92e6de81
243ddf89-bd8d-4b0f-a797-8fa58391cabd	c37659bd-955a-47e0-b1f8-8bf59da0bba9
243ddf89-bd8d-4b0f-a797-8fa58391cabd	58b38f59-b3e4-43d6-9e73-b3824861a4fe
243ddf89-bd8d-4b0f-a797-8fa58391cabd	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
243ddf89-bd8d-4b0f-a797-8fa58391cabd	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
243ddf89-bd8d-4b0f-a797-8fa58391cabd	fb064bd4-3933-4565-ba30-8f36f9d75e30
243ddf89-bd8d-4b0f-a797-8fa58391cabd	3ec35e7d-7de0-413a-b99a-d52568f7977e
243ddf89-bd8d-4b0f-a797-8fa58391cabd	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
243ddf89-bd8d-4b0f-a797-8fa58391cabd	8a64b712-811c-4374-9115-31b2b6eade4b
243ddf89-bd8d-4b0f-a797-8fa58391cabd	de11c81a-9d9d-4770-9a38-6c1c4a47779e
243ddf89-bd8d-4b0f-a797-8fa58391cabd	44dd4463-0079-4576-9d0e-a51bf0335741
243ddf89-bd8d-4b0f-a797-8fa58391cabd	915498a1-1564-49ba-9c14-c8128ee38db0
243ddf89-bd8d-4b0f-a797-8fa58391cabd	52adf122-d1af-4c4d-b123-97e2550fdb5b
243ddf89-bd8d-4b0f-a797-8fa58391cabd	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
243ddf89-bd8d-4b0f-a797-8fa58391cabd	4887f9f9-3ab7-4aa4-965f-edec49cae314
243ddf89-bd8d-4b0f-a797-8fa58391cabd	293af26d-7db7-4464-a057-fc9c0922773f
243ddf89-bd8d-4b0f-a797-8fa58391cabd	0b35c180-535b-45e7-b128-d28c07bc9f3d
243ddf89-bd8d-4b0f-a797-8fa58391cabd	3cf4db6f-1ef1-4171-87f4-265070ec681e
243ddf89-bd8d-4b0f-a797-8fa58391cabd	74a7acb8-42ab-4137-9b22-b836c1224d0e
243ddf89-bd8d-4b0f-a797-8fa58391cabd	c62a7da6-247c-4528-b20d-b54114582f11
243ddf89-bd8d-4b0f-a797-8fa58391cabd	1a00b248-6607-45b8-ac91-81df06dec6a6
243ddf89-bd8d-4b0f-a797-8fa58391cabd	3671cdeb-54c4-4a1c-b737-ed45eb59f752
243ddf89-bd8d-4b0f-a797-8fa58391cabd	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
243ddf89-bd8d-4b0f-a797-8fa58391cabd	04fc3f29-bac1-4314-aaf8-27eac2f0fc76
243ddf89-bd8d-4b0f-a797-8fa58391cabd	9390ff55-a1fe-4e05-bc48-569edeb36e94
b2932b58-15f0-4674-b414-f1e350fcb2f1	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
b2932b58-15f0-4674-b414-f1e350fcb2f1	92c36138-32a0-405e-b742-01284ec55f8a
b2932b58-15f0-4674-b414-f1e350fcb2f1	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
b2932b58-15f0-4674-b414-f1e350fcb2f1	5e88688b-4441-433f-aaaf-efb610fd8bac
b2932b58-15f0-4674-b414-f1e350fcb2f1	a0e13577-dfed-453c-b738-233517c30519
b2932b58-15f0-4674-b414-f1e350fcb2f1	64fb308c-349b-4852-aba8-64f6e229d11b
b2932b58-15f0-4674-b414-f1e350fcb2f1	5ed56e01-356b-402c-b8c0-2eb7b41ded6a
b2932b58-15f0-4674-b414-f1e350fcb2f1	66291ff2-b7b4-4fd1-8903-ea595173eb71
b2932b58-15f0-4674-b414-f1e350fcb2f1	2436b40c-d468-437f-b862-422355979b0e
b2932b58-15f0-4674-b414-f1e350fcb2f1	192ef84f-617d-44b6-94b8-2240fb96aaf5
b2932b58-15f0-4674-b414-f1e350fcb2f1	642f2728-6ffb-49b1-ae92-df16d8361c35
b2932b58-15f0-4674-b414-f1e350fcb2f1	8eeb404f-bfcc-47bc-975c-dfac51ee14bb
b2932b58-15f0-4674-b414-f1e350fcb2f1	bfefdccb-9f9d-4702-a2a5-23630e8b7429
b2932b58-15f0-4674-b414-f1e350fcb2f1	7b03d8f6-5c1e-48da-bfbe-b65c0b1ac558
b2932b58-15f0-4674-b414-f1e350fcb2f1	04fc3f29-bac1-4314-aaf8-27eac2f0fc76
b2932b58-15f0-4674-b414-f1e350fcb2f1	9390ff55-a1fe-4e05-bc48-569edeb36e94
b2932b58-15f0-4674-b414-f1e350fcb2f1	5f0780a6-406a-4264-937b-0309cb79296f
b2932b58-15f0-4674-b414-f1e350fcb2f1	9318aedd-125b-4345-9f5b-20c2ccaab193
67a1557f-b73b-4c31-9444-258b766500a6	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
67a1557f-b73b-4c31-9444-258b766500a6	046821b6-615a-4622-8f85-87931ba716b5
67a1557f-b73b-4c31-9444-258b766500a6	92c36138-32a0-405e-b742-01284ec55f8a
67a1557f-b73b-4c31-9444-258b766500a6	7150cac2-b300-4124-bd25-8734305e993c
67a1557f-b73b-4c31-9444-258b766500a6	0102178b-a2d2-4767-a2ef-caae92e6de81
67a1557f-b73b-4c31-9444-258b766500a6	c37659bd-955a-47e0-b1f8-8bf59da0bba9
67a1557f-b73b-4c31-9444-258b766500a6	58b38f59-b3e4-43d6-9e73-b3824861a4fe
67a1557f-b73b-4c31-9444-258b766500a6	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
67a1557f-b73b-4c31-9444-258b766500a6	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
67a1557f-b73b-4c31-9444-258b766500a6	fb064bd4-3933-4565-ba30-8f36f9d75e30
67a1557f-b73b-4c31-9444-258b766500a6	3ec35e7d-7de0-413a-b99a-d52568f7977e
67a1557f-b73b-4c31-9444-258b766500a6	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
67a1557f-b73b-4c31-9444-258b766500a6	8a64b712-811c-4374-9115-31b2b6eade4b
67a1557f-b73b-4c31-9444-258b766500a6	de11c81a-9d9d-4770-9a38-6c1c4a47779e
67a1557f-b73b-4c31-9444-258b766500a6	44dd4463-0079-4576-9d0e-a51bf0335741
67a1557f-b73b-4c31-9444-258b766500a6	915498a1-1564-49ba-9c14-c8128ee38db0
67a1557f-b73b-4c31-9444-258b766500a6	52adf122-d1af-4c4d-b123-97e2550fdb5b
67a1557f-b73b-4c31-9444-258b766500a6	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
67a1557f-b73b-4c31-9444-258b766500a6	4887f9f9-3ab7-4aa4-965f-edec49cae314
67a1557f-b73b-4c31-9444-258b766500a6	293af26d-7db7-4464-a057-fc9c0922773f
67a1557f-b73b-4c31-9444-258b766500a6	0b35c180-535b-45e7-b128-d28c07bc9f3d
67a1557f-b73b-4c31-9444-258b766500a6	3cf4db6f-1ef1-4171-87f4-265070ec681e
67a1557f-b73b-4c31-9444-258b766500a6	74a7acb8-42ab-4137-9b22-b836c1224d0e
67a1557f-b73b-4c31-9444-258b766500a6	c62a7da6-247c-4528-b20d-b54114582f11
67a1557f-b73b-4c31-9444-258b766500a6	1a00b248-6607-45b8-ac91-81df06dec6a6
67a1557f-b73b-4c31-9444-258b766500a6	3671cdeb-54c4-4a1c-b737-ed45eb59f752
67a1557f-b73b-4c31-9444-258b766500a6	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
971b3945-1169-4f9c-81e1-b28d749c4363	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
971b3945-1169-4f9c-81e1-b28d749c4363	046821b6-615a-4622-8f85-87931ba716b5
971b3945-1169-4f9c-81e1-b28d749c4363	92c36138-32a0-405e-b742-01284ec55f8a
971b3945-1169-4f9c-81e1-b28d749c4363	7150cac2-b300-4124-bd25-8734305e993c
971b3945-1169-4f9c-81e1-b28d749c4363	0102178b-a2d2-4767-a2ef-caae92e6de81
971b3945-1169-4f9c-81e1-b28d749c4363	c37659bd-955a-47e0-b1f8-8bf59da0bba9
971b3945-1169-4f9c-81e1-b28d749c4363	58b38f59-b3e4-43d6-9e73-b3824861a4fe
971b3945-1169-4f9c-81e1-b28d749c4363	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
971b3945-1169-4f9c-81e1-b28d749c4363	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
971b3945-1169-4f9c-81e1-b28d749c4363	fb064bd4-3933-4565-ba30-8f36f9d75e30
971b3945-1169-4f9c-81e1-b28d749c4363	3ec35e7d-7de0-413a-b99a-d52568f7977e
971b3945-1169-4f9c-81e1-b28d749c4363	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
971b3945-1169-4f9c-81e1-b28d749c4363	8a64b712-811c-4374-9115-31b2b6eade4b
971b3945-1169-4f9c-81e1-b28d749c4363	de11c81a-9d9d-4770-9a38-6c1c4a47779e
971b3945-1169-4f9c-81e1-b28d749c4363	44dd4463-0079-4576-9d0e-a51bf0335741
971b3945-1169-4f9c-81e1-b28d749c4363	915498a1-1564-49ba-9c14-c8128ee38db0
971b3945-1169-4f9c-81e1-b28d749c4363	52adf122-d1af-4c4d-b123-97e2550fdb5b
971b3945-1169-4f9c-81e1-b28d749c4363	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
971b3945-1169-4f9c-81e1-b28d749c4363	4887f9f9-3ab7-4aa4-965f-edec49cae314
971b3945-1169-4f9c-81e1-b28d749c4363	293af26d-7db7-4464-a057-fc9c0922773f
971b3945-1169-4f9c-81e1-b28d749c4363	0b35c180-535b-45e7-b128-d28c07bc9f3d
971b3945-1169-4f9c-81e1-b28d749c4363	3cf4db6f-1ef1-4171-87f4-265070ec681e
971b3945-1169-4f9c-81e1-b28d749c4363	74a7acb8-42ab-4137-9b22-b836c1224d0e
971b3945-1169-4f9c-81e1-b28d749c4363	c62a7da6-247c-4528-b20d-b54114582f11
971b3945-1169-4f9c-81e1-b28d749c4363	1a00b248-6607-45b8-ac91-81df06dec6a6
971b3945-1169-4f9c-81e1-b28d749c4363	3671cdeb-54c4-4a1c-b737-ed45eb59f752
971b3945-1169-4f9c-81e1-b28d749c4363	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
f904969c-a267-4162-be2f-2cbe8bf62ba6	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
f904969c-a267-4162-be2f-2cbe8bf62ba6	046821b6-615a-4622-8f85-87931ba716b5
f904969c-a267-4162-be2f-2cbe8bf62ba6	92c36138-32a0-405e-b742-01284ec55f8a
f904969c-a267-4162-be2f-2cbe8bf62ba6	7150cac2-b300-4124-bd25-8734305e993c
f904969c-a267-4162-be2f-2cbe8bf62ba6	0102178b-a2d2-4767-a2ef-caae92e6de81
f904969c-a267-4162-be2f-2cbe8bf62ba6	c37659bd-955a-47e0-b1f8-8bf59da0bba9
f904969c-a267-4162-be2f-2cbe8bf62ba6	58b38f59-b3e4-43d6-9e73-b3824861a4fe
f904969c-a267-4162-be2f-2cbe8bf62ba6	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
f904969c-a267-4162-be2f-2cbe8bf62ba6	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
f904969c-a267-4162-be2f-2cbe8bf62ba6	fb064bd4-3933-4565-ba30-8f36f9d75e30
f904969c-a267-4162-be2f-2cbe8bf62ba6	3ec35e7d-7de0-413a-b99a-d52568f7977e
f904969c-a267-4162-be2f-2cbe8bf62ba6	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
f904969c-a267-4162-be2f-2cbe8bf62ba6	8a64b712-811c-4374-9115-31b2b6eade4b
f904969c-a267-4162-be2f-2cbe8bf62ba6	de11c81a-9d9d-4770-9a38-6c1c4a47779e
f904969c-a267-4162-be2f-2cbe8bf62ba6	44dd4463-0079-4576-9d0e-a51bf0335741
f904969c-a267-4162-be2f-2cbe8bf62ba6	915498a1-1564-49ba-9c14-c8128ee38db0
f904969c-a267-4162-be2f-2cbe8bf62ba6	52adf122-d1af-4c4d-b123-97e2550fdb5b
f904969c-a267-4162-be2f-2cbe8bf62ba6	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
f904969c-a267-4162-be2f-2cbe8bf62ba6	4887f9f9-3ab7-4aa4-965f-edec49cae314
f904969c-a267-4162-be2f-2cbe8bf62ba6	293af26d-7db7-4464-a057-fc9c0922773f
f904969c-a267-4162-be2f-2cbe8bf62ba6	0b35c180-535b-45e7-b128-d28c07bc9f3d
f904969c-a267-4162-be2f-2cbe8bf62ba6	3cf4db6f-1ef1-4171-87f4-265070ec681e
f904969c-a267-4162-be2f-2cbe8bf62ba6	74a7acb8-42ab-4137-9b22-b836c1224d0e
f904969c-a267-4162-be2f-2cbe8bf62ba6	c62a7da6-247c-4528-b20d-b54114582f11
f904969c-a267-4162-be2f-2cbe8bf62ba6	1a00b248-6607-45b8-ac91-81df06dec6a6
f904969c-a267-4162-be2f-2cbe8bf62ba6	3671cdeb-54c4-4a1c-b737-ed45eb59f752
f904969c-a267-4162-be2f-2cbe8bf62ba6	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
3f94c26c-2ccc-4619-8cd5-84483d46ff08	a82e63d4-ce9e-4e93-b7d0-32097928c2ab
3f94c26c-2ccc-4619-8cd5-84483d46ff08	046821b6-615a-4622-8f85-87931ba716b5
3f94c26c-2ccc-4619-8cd5-84483d46ff08	92c36138-32a0-405e-b742-01284ec55f8a
3f94c26c-2ccc-4619-8cd5-84483d46ff08	7150cac2-b300-4124-bd25-8734305e993c
3f94c26c-2ccc-4619-8cd5-84483d46ff08	0102178b-a2d2-4767-a2ef-caae92e6de81
3f94c26c-2ccc-4619-8cd5-84483d46ff08	c37659bd-955a-47e0-b1f8-8bf59da0bba9
3f94c26c-2ccc-4619-8cd5-84483d46ff08	58b38f59-b3e4-43d6-9e73-b3824861a4fe
3f94c26c-2ccc-4619-8cd5-84483d46ff08	c74ee08a-aaa8-4dd5-b055-7fcd00649faa
3f94c26c-2ccc-4619-8cd5-84483d46ff08	692a3cd1-f8f0-4371-8248-66b2cf34b6f8
3f94c26c-2ccc-4619-8cd5-84483d46ff08	fb064bd4-3933-4565-ba30-8f36f9d75e30
3f94c26c-2ccc-4619-8cd5-84483d46ff08	3ec35e7d-7de0-413a-b99a-d52568f7977e
3f94c26c-2ccc-4619-8cd5-84483d46ff08	c1ef8f3f-0626-497e-85f1-adc8d60a42e4
3f94c26c-2ccc-4619-8cd5-84483d46ff08	8a64b712-811c-4374-9115-31b2b6eade4b
3f94c26c-2ccc-4619-8cd5-84483d46ff08	de11c81a-9d9d-4770-9a38-6c1c4a47779e
3f94c26c-2ccc-4619-8cd5-84483d46ff08	44dd4463-0079-4576-9d0e-a51bf0335741
3f94c26c-2ccc-4619-8cd5-84483d46ff08	915498a1-1564-49ba-9c14-c8128ee38db0
3f94c26c-2ccc-4619-8cd5-84483d46ff08	52adf122-d1af-4c4d-b123-97e2550fdb5b
3f94c26c-2ccc-4619-8cd5-84483d46ff08	3eeca83f-9ae6-482a-8c90-858cd1d2ff59
3f94c26c-2ccc-4619-8cd5-84483d46ff08	4887f9f9-3ab7-4aa4-965f-edec49cae314
3f94c26c-2ccc-4619-8cd5-84483d46ff08	293af26d-7db7-4464-a057-fc9c0922773f
3f94c26c-2ccc-4619-8cd5-84483d46ff08	0b35c180-535b-45e7-b128-d28c07bc9f3d
3f94c26c-2ccc-4619-8cd5-84483d46ff08	3cf4db6f-1ef1-4171-87f4-265070ec681e
3f94c26c-2ccc-4619-8cd5-84483d46ff08	74a7acb8-42ab-4137-9b22-b836c1224d0e
3f94c26c-2ccc-4619-8cd5-84483d46ff08	c62a7da6-247c-4528-b20d-b54114582f11
3f94c26c-2ccc-4619-8cd5-84483d46ff08	1a00b248-6607-45b8-ac91-81df06dec6a6
3f94c26c-2ccc-4619-8cd5-84483d46ff08	3671cdeb-54c4-4a1c-b737-ed45eb59f752
3f94c26c-2ccc-4619-8cd5-84483d46ff08	3c9da83c-9f23-4fff-aa04-a0dcbe45bc98
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.roles (role_id, name, description, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
9c2ca698-1529-438e-a4c3-0ff5e0a41ef9	administrator	Full system access with all permissions	2025-07-29 13:45:42.064801	2025-07-29 13:45:42.064801	\N	\N	\N
9eeca02d-18ab-41af-848c-a96fd6b21a20	manager	Full system access with all permissions	2025-07-29 13:45:42.079348	2025-07-29 13:45:42.079348	\N	\N	\N
6d265350-2c69-4985-b555-824e50e430d5	customer	Customer access for license applications and document management	2025-07-29 13:45:42.087045	2025-07-29 13:45:42.087045	\N	\N	\N
ac8768fe-c325-4186-9a37-48e0d28c8823	officer	Officer access for reviewing applications and managing evaluations	2025-07-29 13:45:42.100375	2025-07-29 13:45:42.100375	\N	\N	\N
243ddf89-bd8d-4b0f-a797-8fa58391cabd	legal	Legal team access for license and application management	2025-07-29 13:45:42.10985	2025-07-29 13:45:42.10985	\N	\N	\N
b2932b58-15f0-4674-b414-f1e350fcb2f1	finance	Financial management, invoicing, payments and reporting access	2025-07-29 13:45:42.117086	2025-07-29 13:45:42.117086	\N	\N	\N
67a1557f-b73b-4c31-9444-258b766500a6	postal	Postal department access for postal service license applications	2025-07-29 13:45:42.127823	2025-07-29 13:45:42.127823	\N	\N	\N
971b3945-1169-4f9c-81e1-b28d749c4363	telecommunications	Telecommunications department access for telecom license applications	2025-07-29 13:45:42.136214	2025-07-29 13:45:42.136214	\N	\N	\N
f904969c-a267-4162-be2f-2cbe8bf62ba6	standards	Standards department access for standards compliance applications	2025-07-29 13:45:42.143277	2025-07-29 13:45:42.143277	\N	\N	\N
3f94c26c-2ccc-4619-8cd5-84483d46ff08	clf	CLF department access for CLF license applications	2025-07-29 13:45:42.149464	2025-07-29 13:45:42.149464	\N	\N	\N
\.


--
-- Data for Name: scope_of_service; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.scope_of_service (scope_of_service_id, application_id, nature_of_service, premises, transport_type, customer_assistance, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: shareholder_details; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shareholder_details (shareholder_id, stakeholder_id, shareholding_percent, description, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: shortcodes; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.shortcodes (shortcode_id, shortcode, application_id, assigned_to, shortcode_length, audience, status, category, description, notes, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: stakeholders; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.stakeholders (stakeholder_id, application_id, first_name, last_name, middle_name, nationality, "position", profile, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: tasks; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.tasks (task_id, task_number, title, description, task_type, status, priority, entity_type, entity_id, assigned_to, assigned_by, assigned_at, due_date, completed_at, review, review_notes, completion_notes, metadata, created_at, created_by, updated_at, updated_by, deleted_at) FROM stdin;
\.


--
-- Data for Name: user_identifications; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.user_identifications (identification_type_id, user_id, identification_value, created_at, updated_at, deleted_at, created_by, updated_by) FROM stdin;
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.user_roles (user_id, role_id) FROM stdin;
50aa1b2e-71ba-4a49-99f0-35fde62c8a06	9c2ca698-1529-438e-a4c3-0ff5e0a41ef9
56588b3f-b594-42d1-b11e-e347582f2c4b	ac8768fe-c325-4186-9a37-48e0d28c8823
d23a5ed8-3302-445c-b23b-a2af1c95ff1b	b2932b58-15f0-4674-b414-f1e350fcb2f1
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.users (user_id, email, password, first_name, last_name, middle_name, phone, department_id, organization_id, status, profile_image, two_factor_next_verification, two_factor_code, two_factor_enabled, two_factor_temp, email_verified_at, created_at, updated_at, deleted_at, created_by, updated_by, last_login) FROM stdin;
50aa1b2e-71ba-4a49-99f0-35fde62c8a06	<EMAIL>	$2b$12$ASz6fp1BmEwyxgmOJhTT6uypWEB5U70u928JCDLeozNkyTz4spazi	System	Administrator	\N	+265999000001	\N	\N	active	\N	\N	\N	f	\N	2025-07-29 13:45:42.492	2025-07-29 13:45:42.498594	2025-07-29 13:45:42.498594	\N	\N	\N	\N
56588b3f-b594-42d1-b11e-e347582f2c4b	<EMAIL>	$2b$12$JvxESkbZYnPCq9F4JVP4KeGBpr1VKXUHcWWMhIkEYZM2IHOLK4S6u	Officer	Evaluator	\N	+265999000002	\N	\N	active	\N	\N	\N	f	\N	2025-07-29 13:45:42.829	2025-07-29 13:45:42.830401	2025-07-29 13:45:42.830401	\N	\N	\N	\N
d23a5ed8-3302-445c-b23b-a2af1c95ff1b	<EMAIL>	$2b$12$wJqnbXkAor/f8Qc2zQzzoOkn3VDEVBWaET.R4NlkEEgWUmHgMVjde	Finance	Officer	\N	+265999000003	\N	\N	active	\N	\N	\N	f	\N	2025-07-29 13:45:43.155	2025-07-29 13:45:43.156106	2025-07-29 13:45:43.156106	\N	\N	\N	\N
7953260d-5d9e-46ad-8a65-aa0112d28eda	<EMAIL>	$2b$12$766zoXIbNiviI1kmLgRjbOXHn6iaD5PeSCf4AWBFz.IXpM4x/dk3K	Director	General	\N	+265999000004	\N	\N	active	\N	\N	\N	f	\N	2025-07-29 13:45:43.476	2025-07-29 13:45:43.477272	2025-07-29 13:45:43.477272	\N	\N	\N	\N
\.


--
-- Name: ceir_certification_bodies PK_02556d4bcde1931411529230a6a; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "PK_02556d4bcde1931411529230a6a" PRIMARY KEY (certification_body_id);


--
-- Name: consumer_affairs_complaint_attachments PK_033a7b94a056a33dbd398e0958a; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_attachments
    ADD CONSTRAINT "PK_033a7b94a056a33dbd398e0958a" PRIMARY KEY (attachment_id);


--
-- Name: proof_of_payments PK_06d3d1c5718f02d708354986f21; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.proof_of_payments
    ADD CONSTRAINT "PK_06d3d1c5718f02d708354986f21" PRIMARY KEY (proof_id);


--
-- Name: roles PK_09f4c8130b54f35925588a37b6a; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT "PK_09f4c8130b54f35925588a37b6a" PRIMARY KEY (role_id);


--
-- Name: shareholder_details PK_0bc37eceb4b49af9077c5838cbc; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shareholder_details
    ADD CONSTRAINT "PK_0bc37eceb4b49af9077c5838cbc" PRIMARY KEY (shareholder_id);


--
-- Name: permissions PK_1717db2235a5b169822e7f753b1; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT "PK_1717db2235a5b169822e7f753b1" PRIMARY KEY (permission_id);


--
-- Name: scope_of_service PK_19ad07553350194e9a8d232a075; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scope_of_service
    ADD CONSTRAINT "PK_19ad07553350194e9a8d232a075" PRIMARY KEY (scope_of_service_id);


--
-- Name: contact_persons PK_1e8d4b5358788fbeae2ab3db8fc; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contact_persons
    ADD CONSTRAINT "PK_1e8d4b5358788fbeae2ab3db8fc" PRIMARY KEY (contact_id);


--
-- Name: departments PK_202cd845b076ed15836884084eb; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "PK_202cd845b076ed15836884084eb" PRIMARY KEY (department_id);


--
-- Name: user_roles PK_23ed6f04fe43066df08379fd034; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT "PK_23ed6f04fe43066df08379fd034" PRIMARY KEY (user_id, role_id);


--
-- Name: organizations PK_256856c7ab20081dd27937d43ed; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "PK_256856c7ab20081dd27937d43ed" PRIMARY KEY (organization_id);


--
-- Name: role_permissions PK_25d24010f53bb80b78e412c9656; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT "PK_25d24010f53bb80b78e412c9656" PRIMARY KEY (role_id, permission_id);


--
-- Name: devices PK_2667f40edb344d6f274a0d42b6f; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.devices
    ADD CONSTRAINT "PK_2667f40edb344d6f274a0d42b6f" PRIMARY KEY (device_id);


--
-- Name: identification_types PK_288155a5d824e1e620ecccee975; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identification_types
    ADD CONSTRAINT "PK_288155a5d824e1e620ecccee975" PRIMARY KEY (identification_type_id);


--
-- Name: ceir_equipment_type_categories PK_2b90e190ab4e8e376abdd4042ea; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_type_categories
    ADD CONSTRAINT "PK_2b90e190ab4e8e376abdd4042ea" PRIMARY KEY (category_id);


--
-- Name: employees PK_2d83c53c3e553a48dadb9722e38; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT "PK_2d83c53c3e553a48dadb9722e38" PRIMARY KEY (user_id);


--
-- Name: activity_notes PK_38f1f6df9f0feac483a84a8ef00; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_notes
    ADD CONSTRAINT "PK_38f1f6df9f0feac483a84a8ef00" PRIMARY KEY (id);


--
-- Name: applicant_disclosure PK_3a739ed7fc21b875c9daebc60c6; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicant_disclosure
    ADD CONSTRAINT "PK_3a739ed7fc21b875c9daebc60c6" PRIMARY KEY (applicant_disclosure_id);


--
-- Name: tasks PK_3feca00d238e5cf50185fab8d46; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "PK_3feca00d238e5cf50185fab8d46" PRIMARY KEY (task_id);


--
-- Name: applications PK_418038704e50c663590feb7f511; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "PK_418038704e50c663590feb7f511" PRIMARY KEY (application_id);


--
-- Name: admin_alerts PK_48c941baa61e8d36434c58d9ef3; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admin_alerts
    ADD CONSTRAINT "PK_48c941baa61e8d36434c58d9ef3" PRIMARY KEY (admin_alert_id);


--
-- Name: applicants PK_584d58cca1c76a68c3ab0ee0904; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicants
    ADD CONSTRAINT "PK_584d58cca1c76a68c3ab0ee0904" PRIMARY KEY (applicant_id);


--
-- Name: shortcodes PK_606d62b3a62d1a7cb97df0baf7a; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "PK_606d62b3a62d1a7cb97df0baf7a" PRIMARY KEY (shortcode_id);


--
-- Name: evaluation_criteria PK_63bf4e19c22b8201c45c5acf95d; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluation_criteria
    ADD CONSTRAINT "PK_63bf4e19c22b8201c45c5acf95d" PRIMARY KEY (criteria_id);


--
-- Name: evaluations PK_67a3d2a8877b7f435590930bec7; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT "PK_67a3d2a8877b7f435590930bec7" PRIMARY KEY (evaluation_id);


--
-- Name: client_systems PK_6d09f7b88edb3318de5fbd2ecc5; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_systems
    ADD CONSTRAINT "PK_6d09f7b88edb3318de5fbd2ecc5" PRIMARY KEY (client_system_id);


--
-- Name: addresses PK_7075006c2d82acfeb0ea8c5dce7; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT "PK_7075006c2d82acfeb0ea8c5dce7" PRIMARY KEY (address_id);


--
-- Name: data_breach_report_attachments PK_714669e0ed66b97bb3e4acc3472; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_attachments
    ADD CONSTRAINT "PK_714669e0ed66b97bb3e4acc3472" PRIMARY KEY (attachment_id);


--
-- Name: ceir_test_reports PK_76be47a0ad8b21125a9b533a735; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "PK_76be47a0ad8b21125a9b533a735" PRIMARY KEY (report_id);


--
-- Name: license_category_documents PK_82c4f1c02126210b0d098ca26f6; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_category_documents
    ADD CONSTRAINT "PK_82c4f1c02126210b0d098ca26f6" PRIMARY KEY (license_category_document_id);


--
-- Name: data_breach_reports PK_8829da3bd97dab83d146161ba8b; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "PK_8829da3bd97dab83d146161ba8b" PRIMARY KEY (report_id);


--
-- Name: payments PK_8866a3cfff96b8e17c2b204aae0; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "PK_8866a3cfff96b8e17c2b204aae0" PRIMARY KEY (payment_id);


--
-- Name: employee_roles PK_8bf7f2fbc9039751cd34d9f9606; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_roles
    ADD CONSTRAINT "PK_8bf7f2fbc9039751cd34d9f9606" PRIMARY KEY (employee_id, role_id);


--
-- Name: ceir_equipment_specifications PK_94802bb56517de3319eced68368; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_specifications
    ADD CONSTRAINT "PK_94802bb56517de3319eced68368" PRIMARY KEY (specification_id);


--
-- Name: users PK_96aac72f1574b88752e9fb00089; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "PK_96aac72f1574b88752e9fb00089" PRIMARY KEY (user_id);


--
-- Name: application_status_history PK_9b798cd59b97e524aa84707e076; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_status_history
    ADD CONSTRAINT "PK_9b798cd59b97e524aa84707e076" PRIMARY KEY (history_id);


--
-- Name: legal_history PK_a38d999e0f649b4dfd5600af488; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legal_history
    ADD CONSTRAINT "PK_a38d999e0f649b4dfd5600af488" PRIMARY KEY (legal_history_id);


--
-- Name: invoices PK_a62eb88a23934fb83945c3e58af; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT "PK_a62eb88a23934fb83945c3e58af" PRIMARY KEY (invoice_id);


--
-- Name: licenses PK_a880a1847889df2d497e93a2950; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "PK_a880a1847889df2d497e93a2950" PRIMARY KEY (license_id);


--
-- Name: consumer_affairs_complaints PK_ad5bd9c1dc5c90d8d242165e56f; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "PK_ad5bd9c1dc5c90d8d242165e56f" PRIMARY KEY (complaint_id);


--
-- Name: postal_codes PK_b015ef7fc0eff14ddf548356b7f; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.postal_codes
    ADD CONSTRAINT "PK_b015ef7fc0eff14ddf548356b7f" PRIMARY KEY (postal_code_id);


--
-- Name: contacts PK_b85c417d6af2e06ff6ba8c8234d; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contacts
    ADD CONSTRAINT "PK_b85c417d6af2e06ff6ba8c8234d" PRIMARY KEY (contact_id);


--
-- Name: professional_services PK_b89c1ae1d7ea496969a4ad53e18; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.professional_services
    ADD CONSTRAINT "PK_b89c1ae1d7ea496969a4ad53e18" PRIMARY KEY (professional_services_id);


--
-- Name: license_types PK_b91f514c255105352f15305e091; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_types
    ADD CONSTRAINT "PK_b91f514c255105352f15305e091" PRIMARY KEY (license_type_id);


--
-- Name: documents PK_bec3c89789f76e330bbe1766b2c; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "PK_bec3c89789f76e330bbe1766b2c" PRIMARY KEY (document_id);


--
-- Name: consumer_affairs_complaint_status_history PK_c64e31395b1ec070f4023e9c929; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_status_history
    ADD CONSTRAINT "PK_c64e31395b1ec070f4023e9c929" PRIMARY KEY (history_id);


--
-- Name: data_breach_report_status_history PK_c8e12b12f036d5b91d73fa40883; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_status_history
    ADD CONSTRAINT "PK_c8e12b12f036d5b91d73fa40883" PRIMARY KEY (history_id);


--
-- Name: audit_trails PK_d5a28ffba4cb06e4b5afe1526f8; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_trails
    ADD CONSTRAINT "PK_d5a28ffba4cb06e4b5afe1526f8" PRIMARY KEY (audit_id);


--
-- Name: ceir_technical_standards PK_dcbc0520152d3513a18f8363624; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_technical_standards
    ADD CONSTRAINT "PK_dcbc0520152d3513a18f8363624" PRIMARY KEY (standard_id);


--
-- Name: user_identifications PK_e2ff9517bb266a66d5e15269126; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "PK_e2ff9517bb266a66d5e15269126" PRIMARY KEY (identification_type_id, user_id);


--
-- Name: notifications PK_eaedfe19f0f765d26afafa85956; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "PK_eaedfe19f0f765d26afafa85956" PRIMARY KEY (notification_id);


--
-- Name: stakeholders PK_eb91958671d10131ae2ecef25e7; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stakeholders
    ADD CONSTRAINT "PK_eb91958671d10131ae2ecef25e7" PRIMARY KEY (stakeholder_id);


--
-- Name: license_categories PK_f2c45a9c8eb09da0e1b2db98838; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_categories
    ADD CONSTRAINT "PK_f2c45a9c8eb09da0e1b2db98838" PRIMARY KEY (license_category_id);


--
-- Name: ceir_certification_bodies REL_1aae2b0b798ff125d71a775824; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "REL_1aae2b0b798ff125d71a775824" UNIQUE (address_id);


--
-- Name: ceir_certification_bodies REL_aa9d3d9acc23dc74d6e3ef9c75; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "REL_aa9d3d9acc23dc74d6e3ef9c75" UNIQUE (contact_id);


--
-- Name: ceir_technical_standards UQ_09073fe1b9ef927694b0ec91011; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_technical_standards
    ADD CONSTRAINT "UQ_09073fe1b9ef927694b0ec91011" UNIQUE (standard_reference);


--
-- Name: data_breach_reports UQ_123aaf68e485f31461802715bb8; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "UQ_123aaf68e485f31461802715bb8" UNIQUE (report_number);


--
-- Name: payments UQ_196649f20ae02c5933b0d7c2dad; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "UQ_196649f20ae02c5933b0d7c2dad" UNIQUE (invoice_number);


--
-- Name: user_identifications UQ_1a84417ce0fa3b00c7c48d3a6d1; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "UQ_1a84417ce0fa3b00c7c48d3a6d1" UNIQUE (identification_type_id, identification_value);


--
-- Name: license_types UQ_237894fd15f879dbb623fbb6782; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_types
    ADD CONSTRAINT "UQ_237894fd15f879dbb623fbb6782" UNIQUE (name);


--
-- Name: applications UQ_3861f3cbf221d63e0884c014ba5; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "UQ_3861f3cbf221d63e0884c014ba5" UNIQUE (application_number);


--
-- Name: permissions UQ_48ce552495d14eae9b187bb6716; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT "UQ_48ce552495d14eae9b187bb6716" UNIQUE (name);


--
-- Name: organizations UQ_4ad920935f4d4eb73fc58b40f72; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "UQ_4ad920935f4d4eb73fc58b40f72" UNIQUE (email);


--
-- Name: identification_types UQ_5015ee586a3d770e4aae2821af1; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identification_types
    ADD CONSTRAINT "UQ_5015ee586a3d770e4aae2821af1" UNIQUE (name);


--
-- Name: client_systems UQ_526f19717ac5af4b1eb318a9e21; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_systems
    ADD CONSTRAINT "UQ_526f19717ac5af4b1eb318a9e21" UNIQUE (system_code);


--
-- Name: ceir_certification_bodies UQ_54f92a64e8f281fccdcf783facf; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "UQ_54f92a64e8f281fccdcf783facf" UNIQUE (registration_number);


--
-- Name: licenses UQ_56339f8003b4673e0533186b68a; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "UQ_56339f8003b4673e0533186b68a" UNIQUE (license_number);


--
-- Name: postal_codes UQ_5e91e7e6cd5261bd8e3f5e56678; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.postal_codes
    ADD CONSTRAINT "UQ_5e91e7e6cd5261bd8e3f5e56678" UNIQUE (postal_code);


--
-- Name: roles UQ_648e3f5447f725579d7d4ffdfb7; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE (name);


--
-- Name: departments UQ_67e3d3ac46c34f0bfdf369771c1; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "UQ_67e3d3ac46c34f0bfdf369771c1" UNIQUE (email);


--
-- Name: ceir_test_reports UQ_80e2d6d7e6392156e7bfa78ace0; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "UQ_80e2d6d7e6392156e7bfa78ace0" UNIQUE (report_number);


--
-- Name: ceir_equipment_type_categories UQ_81a20d830edc74117ea5ced1bf5; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_type_categories
    ADD CONSTRAINT "UQ_81a20d830edc74117ea5ced1bf5" UNIQUE (ceir_standard_code);


--
-- Name: departments UQ_8681da666ad9699d568b3e91064; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "UQ_8681da666ad9699d568b3e91064" UNIQUE (name);


--
-- Name: employees UQ_8878710dc844ecd6f9e587f34fc; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT "UQ_8878710dc844ecd6f9e587f34fc" UNIQUE (employee_number);


--
-- Name: consumer_affairs_complaints UQ_8dc44f9c316cb20682c6f5356af; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "UQ_8dc44f9c316cb20682c6f5356af" UNIQUE (complaint_number);


--
-- Name: departments UQ_91fddbe23e927e1e525c152baa3; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "UQ_91fddbe23e927e1e525c152baa3" UNIQUE (code);


--
-- Name: users UQ_97672ac88f789774dd47f7c8be3; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE (email);


--
-- Name: tasks UQ_ae394f93fcb0a172c55ee28339d; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "UQ_ae394f93fcb0a172c55ee28339d" UNIQUE (task_number);


--
-- Name: organizations UQ_cb9542e971de157981d1cf74024; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "UQ_cb9542e971de157981d1cf74024" UNIQUE (registration_number);


--
-- Name: devices UQ_d0a4b8b9b38b6600a92e2edb355; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.devices
    ADD CONSTRAINT "UQ_d0a4b8b9b38b6600a92e2edb355" UNIQUE (imei);


--
-- Name: invoices UQ_d8f8d3788694e1b3f96c42c36fb; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT "UQ_d8f8d3788694e1b3f96c42c36fb" UNIQUE (invoice_number);


--
-- Name: shortcodes UQ_df0f13c82d478090107105e835d; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "UQ_df0f13c82d478090107105e835d" UNIQUE (shortcode);


--
-- Name: IDX_17022daf3f885f7d35423e9971; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_17022daf3f885f7d35423e9971" ON public.role_permissions USING btree (permission_id);


--
-- Name: IDX_178199805b901ccd220ab7740e; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_178199805b901ccd220ab7740e" ON public.role_permissions USING btree (role_id);


--
-- Name: IDX_5aaa50739ad91d5a1753ada5da; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_5aaa50739ad91d5a1753ada5da" ON public.activity_notes USING btree (status);


--
-- Name: IDX_6126acaf5de9975f0037c57a24; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_6126acaf5de9975f0037c57a24" ON public.activity_notes USING btree (created_by);


--
-- Name: IDX_6db2601ea7d61fe753854f10b7; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_6db2601ea7d61fe753854f10b7" ON public.activity_notes USING btree (entity_type, entity_id);


--
-- Name: IDX_87b8888186ca9769c960e92687; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_87b8888186ca9769c960e92687" ON public.user_roles USING btree (user_id);


--
-- Name: IDX_b23c65e50a758245a33ee35fda; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_b23c65e50a758245a33ee35fda" ON public.user_roles USING btree (role_id);


--
-- Name: IDX_b47f8d214f6964d711fec74460; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX "IDX_b47f8d214f6964d711fec74460" ON public.activity_notes USING btree (note_type);


--
-- Name: data_breach_reports FK_006e06e5d4ffd0e34eff9871c8a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "FK_006e06e5d4ffd0e34eff9871c8a" FOREIGN KEY (reporter_id) REFERENCES public.users(user_id);


--
-- Name: organizations FK_019cb56f4ae9b3b20185a90cadd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "FK_019cb56f4ae9b3b20185a90cadd" FOREIGN KEY (postal_address_id) REFERENCES public.addresses(address_id);


--
-- Name: scope_of_service FK_02d35ca39d840bc9d7ab2bd16d3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scope_of_service
    ADD CONSTRAINT "FK_02d35ca39d840bc9d7ab2bd16d3" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: applications FK_049ad019832264e195e99e5bd4f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "FK_049ad019832264e195e99e5bd4f" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: employee_roles FK_04aafdf0252f05451916c4810ec; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_roles
    ADD CONSTRAINT "FK_04aafdf0252f05451916c4810ec" FOREIGN KEY (employee_id) REFERENCES public.employees(user_id);


--
-- Name: evaluations FK_04b7768cb02bd4bfd08d3d37e3d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT "FK_04b7768cb02bd4bfd08d3d37e3d" FOREIGN KEY (evaluator_id) REFERENCES public.users(user_id);


--
-- Name: consumer_affairs_complaint_status_history FK_06676eb0e36b312a5e456f4e3c5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_status_history
    ADD CONSTRAINT "FK_06676eb0e36b312a5e456f4e3c5" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: contacts FK_06dcbcd88c5647753f0f0a4f1cc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contacts
    ADD CONSTRAINT "FK_06dcbcd88c5647753f0f0a4f1cc" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users FK_0921d1972cf861d568f5271cd85; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "FK_0921d1972cf861d568f5271cd85" FOREIGN KEY (department_id) REFERENCES public.departments(department_id);


--
-- Name: consumer_affairs_complaints FK_09a759ddac6c9c02fef225e15a4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "FK_09a759ddac6c9c02fef225e15a4" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: employees FK_0ab5290751972652ae2786f4bc3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT "FK_0ab5290751972652ae2786f4bc3" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: employee_roles FK_0cad03c288690ed8301d27fdcb9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_roles
    ADD CONSTRAINT "FK_0cad03c288690ed8301d27fdcb9" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: consumer_affairs_complaint_attachments FK_0e091c562c231a065aa275872e3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_attachments
    ADD CONSTRAINT "FK_0e091c562c231a065aa275872e3" FOREIGN KEY (uploaded_by) REFERENCES public.users(user_id);


--
-- Name: organizations FK_0e228fd7385e0ca7bb8b2e4383d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "FK_0e228fd7385e0ca7bb8b2e4383d" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: employee_roles FK_13f42debabcdc155b21632097cf; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_roles
    ADD CONSTRAINT "FK_13f42debabcdc155b21632097cf" FOREIGN KEY (role_id) REFERENCES public.roles(role_id);


--
-- Name: documents FK_14371caaff44d0801b59b284166; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_14371caaff44d0801b59b284166" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_certification_bodies FK_15c360c35e373d1ead803898581; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "FK_15c360c35e373d1ead803898581" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: role_permissions FK_17022daf3f885f7d35423e9971e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT "FK_17022daf3f885f7d35423e9971e" FOREIGN KEY (permission_id) REFERENCES public.permissions(permission_id);


--
-- Name: role_permissions FK_178199805b901ccd220ab7740ec; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT "FK_178199805b901ccd220ab7740ec" FOREIGN KEY (role_id) REFERENCES public.roles(role_id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: devices FK_182fd30297143672133e7a6c3ff; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.devices
    ADD CONSTRAINT "FK_182fd30297143672133e7a6c3ff" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: applications FK_194d0fca275b8661a56e486cb64; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "FK_194d0fca275b8661a56e486cb64" FOREIGN KEY (applicant_id) REFERENCES public.applicants(applicant_id);


--
-- Name: notifications FK_19629e8eb1e6023c4c73e661c82; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_19629e8eb1e6023c4c73e661c82" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: license_category_documents FK_1a47acd3e439911aea6a60407c2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_category_documents
    ADD CONSTRAINT "FK_1a47acd3e439911aea6a60407c2" FOREIGN KEY (license_category_id) REFERENCES public.license_categories(license_category_id) ON DELETE CASCADE;


--
-- Name: ceir_certification_bodies FK_1aae2b0b798ff125d71a7758240; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "FK_1aae2b0b798ff125d71a7758240" FOREIGN KEY (address_id) REFERENCES public.addresses(address_id);


--
-- Name: license_types FK_1b91a719834bc653b2edf94a6e5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_types
    ADD CONSTRAINT "FK_1b91a719834bc653b2edf94a6e5" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: data_breach_reports FK_1cbb5b088d08ecc5f1d7d1737ad; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "FK_1cbb5b088d08ecc5f1d7d1737ad" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: licenses FK_1d07d3d4a8e843624ee4ed033d8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "FK_1d07d3d4a8e843624ee4ed033d8" FOREIGN KEY (issued_by) REFERENCES public.users(user_id);


--
-- Name: addresses FK_211a084fe7d2ed402a4fd2f1bdc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT "FK_211a084fe7d2ed402a4fd2f1bdc" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users FK_21a659804ed7bf61eb91688dea7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "FK_21a659804ed7bf61eb91688dea7" FOREIGN KEY (organization_id) REFERENCES public.organizations(organization_id);


--
-- Name: license_categories FK_2553ce42977ac6dd86fb6d743ce; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_categories
    ADD CONSTRAINT "FK_2553ce42977ac6dd86fb6d743ce" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: documents FK_26bb43df7679eb460871524260c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_26bb43df7679eb460871524260c" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: client_systems FK_2756829aa844944f5c6a6c88257; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_systems
    ADD CONSTRAINT "FK_2756829aa844944f5c6a6c88257" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: user_identifications FK_29e15ca90d4be9d07f0d5b0f23b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "FK_29e15ca90d4be9d07f0d5b0f23b" FOREIGN KEY (identification_type_id) REFERENCES public.identification_types(identification_type_id);


--
-- Name: departments FK_2a517743a9cd9c4afdef36ec033; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "FK_2a517743a9cd9c4afdef36ec033" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: licenses FK_2ac35790d5a908d931c42976b6b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "FK_2ac35790d5a908d931c42976b6b" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: consumer_affairs_complaint_attachments FK_2ad1eba1b12fe5e8d3aaef2dcf9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_attachments
    ADD CONSTRAINT "FK_2ad1eba1b12fe5e8d3aaef2dcf9" FOREIGN KEY (complaint_id) REFERENCES public.consumer_affairs_complaints(complaint_id);


--
-- Name: payments FK_2b505576ec68c4d47782a51a832; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "FK_2b505576ec68c4d47782a51a832" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: applications FK_2d58d66aa4263082e748c990ae1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "FK_2d58d66aa4263082e748c990ae1" FOREIGN KEY (assigned_to) REFERENCES public.users(user_id);


--
-- Name: employees FK_2d83c53c3e553a48dadb9722e38; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT "FK_2d83c53c3e553a48dadb9722e38" FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: ceir_test_reports FK_2f5b96af044d57dab8ef5d92ea6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_2f5b96af044d57dab8ef5d92ea6" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_test_reports FK_33c29a3424bf355651a7288f38e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_33c29a3424bf355651a7288f38e" FOREIGN KEY (device_id) REFERENCES public.devices(device_id);


--
-- Name: consumer_affairs_complaint_status_history FK_33cfde7570f0e6b926ca9467310; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaint_status_history
    ADD CONSTRAINT "FK_33cfde7570f0e6b926ca9467310" FOREIGN KEY (complaint_id) REFERENCES public.consumer_affairs_complaints(complaint_id);


--
-- Name: consumer_affairs_complaints FK_36e4838a8cae2a1766108340fe8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "FK_36e4838a8cae2a1766108340fe8" FOREIGN KEY (assigned_to) REFERENCES public.users(user_id);


--
-- Name: invoices FK_39a202af5d1dd1744458820ecb5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT "FK_39a202af5d1dd1744458820ecb5" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_equipment_specifications FK_39bc9307aad580ab8c4d2fdc1ca; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_specifications
    ADD CONSTRAINT "FK_39bc9307aad580ab8c4d2fdc1ca" FOREIGN KEY (device_id) REFERENCES public.devices(device_id);


--
-- Name: shortcodes FK_3a85db68bc44fdbd71ac8733ab3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "FK_3a85db68bc44fdbd71ac8733ab3" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: payments FK_3b379ebb0e5d8ac17f998b932e7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "FK_3b379ebb0e5d8ac17f998b932e7" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: ceir_technical_standards FK_3df67012d56933348b664f7615d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_technical_standards
    ADD CONSTRAINT "FK_3df67012d56933348b664f7615d" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: professional_services FK_3fef306a19c0974cc011a696321; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.professional_services
    ADD CONSTRAINT "FK_3fef306a19c0974cc011a696321" FOREIGN KEY (application_id) REFERENCES public.applications(application_id) ON DELETE CASCADE;


--
-- Name: legal_history FK_410848e0b4fb95826d093f79d63; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legal_history
    ADD CONSTRAINT "FK_410848e0b4fb95826d093f79d63" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: evaluations FK_42613ea05dd223da68e45d77ed1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT "FK_42613ea05dd223da68e45d77ed1" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: payments FK_427785468fb7d2733f59e7d7d39; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "FK_427785468fb7d2733f59e7d7d39" FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: employees FK_43d76ca7eecf9373241e2e890fb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT "FK_43d76ca7eecf9373241e2e890fb" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: scope_of_service FK_45a0dcf57a358bc64c545c314d0; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scope_of_service
    ADD CONSTRAINT "FK_45a0dcf57a358bc64c545c314d0" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: data_breach_report_attachments FK_47c299eeb0907ec508ee510d875; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_attachments
    ADD CONSTRAINT "FK_47c299eeb0907ec508ee510d875" FOREIGN KEY (uploaded_by) REFERENCES public.users(user_id);


--
-- Name: devices FK_482b24fdc935ff496a87dc8d373; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.devices
    ADD CONSTRAINT "FK_482b24fdc935ff496a87dc8d373" FOREIGN KEY (application_id) REFERENCES public.applications(application_id) ON DELETE SET NULL;


--
-- Name: ceir_equipment_type_categories FK_482fb4a42bc7f28575c9ebedd04; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_type_categories
    ADD CONSTRAINT "FK_482fb4a42bc7f28575c9ebedd04" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: roles FK_4a39f3095781cdd9d6061afaae5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT "FK_4a39f3095781cdd9d6061afaae5" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: license_categories FK_4aa9b4e0b4efa45b180413018b2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_categories
    ADD CONSTRAINT "FK_4aa9b4e0b4efa45b180413018b2" FOREIGN KEY (parent_id) REFERENCES public.license_categories(license_category_id);


--
-- Name: data_breach_reports FK_4cb86dbf4ff85bc1983230c768a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "FK_4cb86dbf4ff85bc1983230c768a" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: applicants FK_4cb8ab619da90e77a4a5a0bb224; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicants
    ADD CONSTRAINT "FK_4cb8ab619da90e77a4a5a0bb224" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_test_reports FK_4e4f78ea5a85beddc34dc642b4f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_4e4f78ea5a85beddc34dc642b4f" FOREIGN KEY (application_id) REFERENCES public.applications(application_id) ON DELETE SET NULL;


--
-- Name: notifications FK_5332a4daa46fd3f4e6625dd275d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_5332a4daa46fd3f4e6625dd275d" FOREIGN KEY (recipient_id) REFERENCES public.users(user_id);


--
-- Name: user_identifications FK_54e7a068b76b3e8ec02d5bc7be5; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "FK_54e7a068b76b3e8ec02d5bc7be5" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: stakeholders FK_5518ae16ebb22019f47827a3127; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stakeholders
    ADD CONSTRAINT "FK_5518ae16ebb22019f47827a3127" FOREIGN KEY (updated_by) REFERENCES public.users(user_id) ON DELETE SET NULL;


--
-- Name: stakeholders FK_5525cda345b76b7633dba45bc3d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stakeholders
    ADD CONSTRAINT "FK_5525cda345b76b7633dba45bc3d" FOREIGN KEY (created_by) REFERENCES public.users(user_id) ON DELETE SET NULL;


--
-- Name: invoices FK_5534ba11e10f1a9953cbdaabf16; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT "FK_5534ba11e10f1a9953cbdaabf16" FOREIGN KEY (client_id) REFERENCES public.applicants(applicant_id);


--
-- Name: tasks FK_5770b28d72ca90c43b1381bf787; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "FK_5770b28d72ca90c43b1381bf787" FOREIGN KEY (assigned_to) REFERENCES public.users(user_id);


--
-- Name: identification_types FK_596e4dc4860c397ea352a797e8d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identification_types
    ADD CONSTRAINT "FK_596e4dc4860c397ea352a797e8d" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: identification_types FK_5bd2e8c93297e6a49a24a09b128; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.identification_types
    ADD CONSTRAINT "FK_5bd2e8c93297e6a49a24a09b128" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: contact_persons FK_5d25c125b5c911c9937abf55ed1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contact_persons
    ADD CONSTRAINT "FK_5d25c125b5c911c9937abf55ed1" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: data_breach_reports FK_5d8e4acff549786cf48bfaf377a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_reports
    ADD CONSTRAINT "FK_5d8e4acff549786cf48bfaf377a" FOREIGN KEY (assigned_to) REFERENCES public.users(user_id);


--
-- Name: tasks FK_5d927ef9f86fac1f1671d093a04; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "FK_5d927ef9f86fac1f1671d093a04" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: activity_notes FK_6126acaf5de9975f0037c57a24a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_notes
    ADD CONSTRAINT "FK_6126acaf5de9975f0037c57a24a" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: proof_of_payments FK_62c848495ffca4e8839e535b981; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.proof_of_payments
    ADD CONSTRAINT "FK_62c848495ffca4e8839e535b981" FOREIGN KEY (reviewed_by) REFERENCES public.users(user_id);


--
-- Name: license_categories FK_6731bc70cc7d8adf81df6aa34eb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_categories
    ADD CONSTRAINT "FK_6731bc70cc7d8adf81df6aa34eb" FOREIGN KEY (license_type_id) REFERENCES public.license_types(license_type_id);


--
-- Name: data_breach_report_status_history FK_6abc8292b3038f69ca486515ab8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_status_history
    ADD CONSTRAINT "FK_6abc8292b3038f69ca486515ab8" FOREIGN KEY (report_id) REFERENCES public.data_breach_reports(report_id);


--
-- Name: shareholder_details FK_6b6029b9904bb10430e5eb103cd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shareholder_details
    ADD CONSTRAINT "FK_6b6029b9904bb10430e5eb103cd" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: scope_of_service FK_6ef77711a084b67bf4bb292eef3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scope_of_service
    ADD CONSTRAINT "FK_6ef77711a084b67bf4bb292eef3" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: licenses FK_709bcf94cc7422f5d3db7082296; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "FK_709bcf94cc7422f5d3db7082296" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: evaluation_criteria FK_71d78d3b3f4b02c659e3ad0c24f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluation_criteria
    ADD CONSTRAINT "FK_71d78d3b3f4b02c659e3ad0c24f" FOREIGN KEY (evaluation_id) REFERENCES public.evaluations(evaluation_id);


--
-- Name: roles FK_747b580d73db0ad78963d78b076; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT "FK_747b580d73db0ad78963d78b076" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: employee_roles FK_74cbe9b97bd9fa29064d69c21b2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employee_roles
    ADD CONSTRAINT "FK_74cbe9b97bd9fa29064d69c21b2" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: license_category_documents FK_81f91c32dd473315e021f6d768f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_category_documents
    ADD CONSTRAINT "FK_81f91c32dd473315e021f6d768f" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: user_roles FK_87b8888186ca9769c960e926870; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: organizations FK_88a24953b7fb00e52d96fc1e2ba; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "FK_88a24953b7fb00e52d96fc1e2ba" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_equipment_specifications FK_8ad21ba20e30895ac1437e4978e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_specifications
    ADD CONSTRAINT "FK_8ad21ba20e30895ac1437e4978e" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: ceir_equipment_specifications FK_8dabe56764b04d7286b8a60a42e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_specifications
    ADD CONSTRAINT "FK_8dabe56764b04d7286b8a60a42e" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: invoices FK_8dc3c1211899ef0d948b1652908; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT "FK_8dc3c1211899ef0d948b1652908" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: applicant_disclosure FK_8e6deaf59ed4dce05d85e514c63; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicant_disclosure
    ADD CONSTRAINT "FK_8e6deaf59ed4dce05d85e514c63" FOREIGN KEY (applicant_id) REFERENCES public.applicants(applicant_id);


--
-- Name: shareholder_details FK_9096b4921615d8667cb58972157; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shareholder_details
    ADD CONSTRAINT "FK_9096b4921615d8667cb58972157" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: client_systems FK_90f5a95ef5be80ef2e4abb36b44; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.client_systems
    ADD CONSTRAINT "FK_90f5a95ef5be80ef2e4abb36b44" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: organizations FK_94e4d0bc02ac43f12e74501bcf7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "FK_94e4d0bc02ac43f12e74501bcf7" FOREIGN KEY (physical_address_id) REFERENCES public.addresses(address_id);


--
-- Name: devices FK_9d845b2bf8b0dcd91ca1017cf6d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.devices
    ADD CONSTRAINT "FK_9d845b2bf8b0dcd91ca1017cf6d" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: tasks FK_9fc727aef9e222ebd09dc8dac08; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "FK_9fc727aef9e222ebd09dc8dac08" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: addresses FK_a09c50b87e08571067c39ccba46; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.addresses
    ADD CONSTRAINT "FK_a09c50b87e08571067c39ccba46" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: licenses FK_a24d177962b0c9ff1fa4ba0faa9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.licenses
    ADD CONSTRAINT "FK_a24d177962b0c9ff1fa4ba0faa9" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: contact_persons FK_a262fae95b07642b54c26b5f9ae; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contact_persons
    ADD CONSTRAINT "FK_a262fae95b07642b54c26b5f9ae" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: shortcodes FK_a4146b8f723b9e6b5f706fcb75e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "FK_a4146b8f723b9e6b5f706fcb75e" FOREIGN KEY (application_id) REFERENCES public.applications(application_id) ON DELETE SET NULL;


--
-- Name: application_status_history FK_a461ff43395424ebd6e218deb32; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_status_history
    ADD CONSTRAINT "FK_a461ff43395424ebd6e218deb32" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: license_categories FK_a7bced9fac1b4f7ed4037b806aa; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_categories
    ADD CONSTRAINT "FK_a7bced9fac1b4f7ed4037b806aa" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: user_identifications FK_a892d6699dd00a59982c2fc6511; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "FK_a892d6699dd00a59982c2fc6511" FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: tasks FK_a8a7b3b66c0b0fee5da777fea96; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT "FK_a8a7b3b66c0b0fee5da777fea96" FOREIGN KEY (assigned_by) REFERENCES public.users(user_id);


--
-- Name: ceir_certification_bodies FK_aa9d3d9acc23dc74d6e3ef9c755; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "FK_aa9d3d9acc23dc74d6e3ef9c755" FOREIGN KEY (contact_id) REFERENCES public.contacts(contact_id);


--
-- Name: data_breach_report_status_history FK_af8796d3fc288cfce659a7f4853; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_status_history
    ADD CONSTRAINT "FK_af8796d3fc288cfce659a7f4853" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: applications FK_af9ee72dfb62f006fb09a10521f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "FK_af9ee72dfb62f006fb09a10521f" FOREIGN KEY (license_category_id) REFERENCES public.license_categories(license_category_id);


--
-- Name: evaluations FK_affa9d290242ff72ee62ba7e807; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT "FK_affa9d290242ff72ee62ba7e807" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: user_roles FK_b23c65e50a758245a33ee35fda1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY (role_id) REFERENCES public.roles(role_id);


--
-- Name: evaluations FK_b57cb82ca463d8d04c31657da87; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluations
    ADD CONSTRAINT "FK_b57cb82ca463d8d04c31657da87" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: license_category_documents FK_b736b1c2ee7efd38425a397be39; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_category_documents
    ADD CONSTRAINT "FK_b736b1c2ee7efd38425a397be39" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: users FK_b75c92ef36f432fe68ec300a7d4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "FK_b75c92ef36f432fe68ec300a7d4" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: ceir_certification_bodies FK_b8b929559451603541880082520; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_certification_bodies
    ADD CONSTRAINT "FK_b8b929559451603541880082520" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: data_breach_report_attachments FK_b8fea36148d7dfb16d9f71aa30f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_breach_report_attachments
    ADD CONSTRAINT "FK_b8fea36148d7dfb16d9f71aa30f" FOREIGN KEY (report_id) REFERENCES public.data_breach_reports(report_id);


--
-- Name: ceir_test_reports FK_bbea5844e5e53029041616611d7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_bbea5844e5e53029041616611d7" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: shareholder_details FK_be3c4637ddbe72357e5ce45a5e1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shareholder_details
    ADD CONSTRAINT "FK_be3c4637ddbe72357e5ce45a5e1" FOREIGN KEY (stakeholder_id) REFERENCES public.stakeholders(stakeholder_id);


--
-- Name: evaluation_criteria FK_bf9c26fa999cfc552d23aed0fb7; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluation_criteria
    ADD CONSTRAINT "FK_bf9c26fa999cfc552d23aed0fb7" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: applicant_disclosure FK_c0aa9b8c9b2ead0a7271e2a4328; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicant_disclosure
    ADD CONSTRAINT "FK_c0aa9b8c9b2ead0a7271e2a4328" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: contacts FK_c8067d2dc7bc8408ec3cc4a9e25; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contacts
    ADD CONSTRAINT "FK_c8067d2dc7bc8408ec3cc4a9e25" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: user_identifications FK_c8f6aeb0b91869d0f908fe58179; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_identifications
    ADD CONSTRAINT "FK_c8f6aeb0b91869d0f908fe58179" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: activity_notes FK_cbc472cf8e556376e580b92b208; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.activity_notes
    ADD CONSTRAINT "FK_cbc472cf8e556376e580b92b208" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: consumer_affairs_complaints FK_cfa74a8a673091daaec4b8c3e92; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "FK_cfa74a8a673091daaec4b8c3e92" FOREIGN KEY (complainant_id) REFERENCES public.users(user_id);


--
-- Name: stakeholders FK_d0da18334256830ed1dd565a4d3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.stakeholders
    ADD CONSTRAINT "FK_d0da18334256830ed1dd565a4d3" FOREIGN KEY (application_id) REFERENCES public.applications(application_id) ON DELETE CASCADE;


--
-- Name: payments FK_d2448ea73e035eaab83372ee8a8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "FK_d2448ea73e035eaab83372ee8a8" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: proof_of_payments FK_d66b10f06e77e9a97435f155d0c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.proof_of_payments
    ADD CONSTRAINT "FK_d66b10f06e77e9a97435f155d0c" FOREIGN KEY (submitted_by) REFERENCES public.users(user_id);


--
-- Name: ceir_test_reports FK_d69519379d99c3564d462f54199; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_d69519379d99c3564d462f54199" FOREIGN KEY (technical_standard_id) REFERENCES public.ceir_technical_standards(standard_id);


--
-- Name: shortcodes FK_d8f2e71bea6933c2f3650abd1a9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "FK_d8f2e71bea6933c2f3650abd1a9" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: license_types FK_d910de9dc26f536f8ddd59c7275; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.license_types
    ADD CONSTRAINT "FK_d910de9dc26f536f8ddd59c7275" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: ceir_technical_standards FK_dad1b101bdf60b0bd399a3ed582; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_technical_standards
    ADD CONSTRAINT "FK_dad1b101bdf60b0bd399a3ed582" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: applicants FK_de01c9a3c0f4a999ea404d9581e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicants
    ADD CONSTRAINT "FK_de01c9a3c0f4a999ea404d9581e" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: notifications FK_e0517903116b233d60423efa296; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_e0517903116b233d60423efa296" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: applicant_disclosure FK_e07590b783beb0d2969ea3c82c4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applicant_disclosure
    ADD CONSTRAINT "FK_e07590b783beb0d2969ea3c82c4" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_equipment_specifications FK_e2f5e8f3330347a575605da71dd; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_specifications
    ADD CONSTRAINT "FK_e2f5e8f3330347a575605da71dd" FOREIGN KEY (equipment_category_id) REFERENCES public.ceir_equipment_type_categories(category_id);


--
-- Name: departments FK_e4fb15c173042ccd356099f69f6; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "FK_e4fb15c173042ccd356099f69f6" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: ceir_equipment_type_categories FK_ecc300a21279083488aef7d4d2c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_equipment_type_categories
    ADD CONSTRAINT "FK_ecc300a21279083488aef7d4d2c" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: audit_trails FK_ed8d9979a8295afe3a06fb5d82a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.audit_trails
    ADD CONSTRAINT "FK_ed8d9979a8295afe3a06fb5d82a" FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE SET NULL;


--
-- Name: departments FK_ef8a4fb89ff96bbe98f1798798c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT "FK_ef8a4fb89ff96bbe98f1798798c" FOREIGN KEY (manager_id) REFERENCES public.users(user_id);


--
-- Name: legal_history FK_ef8ad6c3671c4c69350179a209e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legal_history
    ADD CONSTRAINT "FK_ef8ad6c3671c4c69350179a209e" FOREIGN KEY (application_id) REFERENCES public.applications(application_id);


--
-- Name: consumer_affairs_complaints FK_f063314082d23cbf6128ad23bcb; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.consumer_affairs_complaints
    ADD CONSTRAINT "FK_f063314082d23cbf6128ad23bcb" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: organizations FK_f28555f7d5cdc3eb0b83f80ff39; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT "FK_f28555f7d5cdc3eb0b83f80ff39" FOREIGN KEY (contact_id) REFERENCES public.contacts(contact_id);


--
-- Name: users FK_f32b1cb14a9920477bcfd63df2c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "FK_f32b1cb14a9920477bcfd63df2c" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: contact_persons FK_f629b05f1a06aa1ee8da0763c7a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contact_persons
    ADD CONSTRAINT "FK_f629b05f1a06aa1ee8da0763c7a" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: application_status_history FK_f9a66d9d7e20013564479f108bc; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.application_status_history
    ADD CONSTRAINT "FK_f9a66d9d7e20013564479f108bc" FOREIGN KEY (changed_by) REFERENCES public.users(user_id);


--
-- Name: applications FK_f9aa94249c4c9aa09d38ed6bc27; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT "FK_f9aa94249c4c9aa09d38ed6bc27" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: ceir_test_reports FK_f9d72b0d107d452dc3976641aa1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ceir_test_reports
    ADD CONSTRAINT "FK_f9d72b0d107d452dc3976641aa1" FOREIGN KEY (certification_body_id) REFERENCES public.ceir_certification_bodies(certification_body_id);


--
-- Name: legal_history FK_fa0c236942d8a3528fff129ddf9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legal_history
    ADD CONSTRAINT "FK_fa0c236942d8a3528fff129ddf9" FOREIGN KEY (created_by) REFERENCES public.users(user_id);


--
-- Name: evaluation_criteria FK_fb7f9075faeff922e1b68067c9e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.evaluation_criteria
    ADD CONSTRAINT "FK_fb7f9075faeff922e1b68067c9e" FOREIGN KEY (updated_by) REFERENCES public.users(user_id);


--
-- Name: shortcodes FK_ff91f61921db3dc725e4dd95964; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shortcodes
    ADD CONSTRAINT "FK_ff91f61921db3dc725e4dd95964" FOREIGN KEY (assigned_to) REFERENCES public.organizations(organization_id) ON DELETE SET NULL;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: -
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

