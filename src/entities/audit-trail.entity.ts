import {
  En<PERSON><PERSON>,
  Column,
  Create<PERSON><PERSON><PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { SetMetadata } from '@nestjs/common';

export enum AuditAction {
  LOGIN = 'login',
  LOGOUT = 'logout',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  EXPORT = 'export',
  IMPORT = 'import',
}

export enum AuditModule {
  AUTHENTICATION = 'authentication',
  USER_MANAGEMENT = 'user_management',
  ROLE_MANAGEMENT = 'role_management',
  PERMISSION_MANAGEMENT = 'permission_management',
  LICENSE_MANAGEMENT = 'license_management',
  DOCUMENT_MANAGEMENT = 'document_management',
  SPECTRUM_MANAGEMENT = 'spectrum_management',
  TRANSACTION_MANAGEMENT = 'transaction_management',
  SYSTEM_SETTINGS = 'system_settings',
  SYSTEM_MANAGEMENT = 'system_management',
  POSTAL_SERVICES = 'postal_services',
  TYPE_APPROVAL_SERVICES = 'type_approval_services',
  SHORT_CODE_SERVICES = 'short_code_services',
  TELECOMMUNICATION_SERVICES = 'telecommunication_services',
  BROADCASTING_SERVICES = 'broadcasting_services',
  ADDRESS_MANAGEMENT = 'address_management',
  DASHBOARD = 'dashboard',
  TASK_MANAGEMENT = 'task_management',
  CONSUMER_AFFAIRS = 'consumer_affairs',
}

export enum AuditStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  WARNING = 'warning',
}

@Entity('audit_trails')
export class AuditTrail {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  audit_id: string;

  @Column({ type: 'varchar', default: AuditAction.CREATE })
  action: string;

  @Column({ type: 'varchar',  nullable: true })
  module: string;

  @Column({ type: 'varchar',  nullable: true })
  status: string;

  @Column({ type: 'varchar', length: 255 })
  resource_type: string; // e.g., 'User', 'Role', 'Permission'

  @Column({ type: 'varchar', length: 255, nullable: true })
  resource_id: string; // ID of the affected resource

  @Column({ type: 'text', nullable: true })
  description: string; // Human-readable description

  @Column({ type: 'json', nullable: true })
  old_values: Record<string, any>; // Previous values (for updates)

  @Column({ type: 'json', nullable: true })
  new_values: Record<string, any>; // New values (for creates/updates)

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>; // Additional context data

  @Column({ type: 'varchar', length: 45, nullable: true })
  ip_address: string;

  @Column({ type: 'text', nullable: true })
  user_agent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  session_id: string;

  @Column({ type: 'text', nullable: true })
  error_message: string; // For failed actions

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  user_id: string;

  @CreateDateColumn()
  created_at: Date;

  @BeforeInsert()
  generateId() {
    if (!this.audit_id) {
      this.audit_id = uuidv4();
    }
  }
}

// Audit decorator
export const Audit = (options: {
  action: AuditAction;
  module: AuditModule;
  resourceType: string;
  description: string;
}) => SetMetadata('audit', options);
