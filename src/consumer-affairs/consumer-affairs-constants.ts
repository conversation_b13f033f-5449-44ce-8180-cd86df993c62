export enum ComplaintPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum ComplaintStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum ComplaintCategory {
  BILLING_CHARGES = 'Billing & Charges',
  SERVICE_QUALITY = 'Service Quality',
  NETWORK_ISSUES = 'Network Issues',
  CUSTOMER_SERVICE = 'Customer Service',
  CONTRACT_DISPUTES = 'Contract Disputes',
  ACCESSIBILITY = 'Accessibility',
  FRAUD_SCAMS = 'Fraud & Scams',
  OTHER = 'Other',
}

export interface ConsumerAffairsComplaintFilter {
  complaint_number?: string;
  complaint_id?: string;
  complainant_id?: string;
  title?: string;
  description?: string;
  category?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
  resolution?: string;
  resolved_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface ConsumerAffairsComplaintResponseDto {
  complaint_id: string;
  complaint_number: string;
  complainant_id: string;
  complainee_reg_number?: string;
  title: string;
  description: string;
  category: string;
  status: string;
  priority: string;
  assigned_to?: string;
  resolution?: string;
  resolved_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  
  // Related data
  complainant?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  // Complainee to be added here
  /*
  complainee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  */
  
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  
  attachments?: {
    attachment_id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    uploaded_at: Date;
  }[];
  
  status_history?: {
    history_id: string;
    status: string;
    comment?: string;
    created_at: Date;
    creator: {
      user_id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}
