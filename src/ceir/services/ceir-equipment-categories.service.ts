import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirEquipmentTypeCategories } from '../entities/ceir-equipment-type-categories.entity';
import { CreateCeirEquipmentCategoryDto, UpdateCeirEquipmentCategoryDto } from '../dto/ceir-equipment-categories';

@Injectable()
export class CeirEquipmentCategoriesService {
  constructor(
    @InjectRepository(CeirEquipmentTypeCategories)
    private readonly categoryRepository: Repository<CeirEquipmentTypeCategories>,
  ) {}

  async create(createDto: CreateCeirEquipmentCategoryDto, userId?: string): Promise<CeirEquipmentTypeCategories> {
    // Check if category with same type or CEIR standard code already exists
    const existingCategory = await this.categoryRepository.findOne({
      where: [
        { category_type: createDto.category_type },
        { ceir_standard_code: createDto.ceir_standard_code }
      ]
    });

    if (existingCategory) {
      if (existingCategory.category_type === createDto.category_type) {
        throw new ConflictException(`Equipment category with type '${createDto.category_type}' already exists`);
      }
      if (existingCategory.ceir_standard_code === createDto.ceir_standard_code) {
        throw new ConflictException(`Equipment category with CEIR standard code '${createDto.ceir_standard_code}' already exists`);
      }
    }

    const category = this.categoryRepository.create({
      ...createDto,
      created_by: createDto.created_by || userId, // Use provided created_by or fallback to userId from JWT
    });
    return await this.categoryRepository.save(category);
  }

  async findAll(): Promise<CeirEquipmentTypeCategories[]> {
    return await this.categoryRepository.find({
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' }
    });
  }

  async findAllActive(): Promise<CeirEquipmentTypeCategories[]> {
    return await this.categoryRepository.find({
      where: { is_active: true },
      relations: ['creator', 'updater'],
      order: { category_name: 'ASC' }
    });
  }

  async findOne(id: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.categoryRepository.findOne({
      where: { category_id: id },
      relations: ['creator', 'updater', 'approved_devices']
    });

    if (!category) {
      throw new NotFoundException(`Equipment category with ID '${id}' not found`);
    }

    return category;
  }

  async findByType(categoryType: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.categoryRepository.findOne({
      where: { category_type: categoryType },
      relations: ['creator', 'updater']
    });

    if (!category) {
      throw new NotFoundException(`Equipment category with type '${categoryType}' not found`);
    }

    return category;
  }

  async findByCeirCode(ceirCode: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.categoryRepository.findOne({
      where: { ceir_standard_code: ceirCode },
      relations: ['creator', 'updater']
    });

    if (!category) {
      throw new NotFoundException(`Equipment category with CEIR code '${ceirCode}' not found`);
    }

    return category;
  }

  async update(id: string, updateDto: UpdateCeirEquipmentCategoryDto, userId?: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.findOne(id);

    // Check for conflicts if updating category_type or ceir_standard_code
    if (updateDto.category_type || updateDto.ceir_standard_code) {
      const conflictConditions: Array<{ category_type?: string } | { ceir_standard_code?: string }> = [];

      if (updateDto.category_type && updateDto.category_type !== category.category_type) {
        conflictConditions.push({ category_type: updateDto.category_type });
      }

      if (updateDto.ceir_standard_code && updateDto.ceir_standard_code !== category.ceir_standard_code) {
        conflictConditions.push({ ceir_standard_code: updateDto.ceir_standard_code });
      }

      if (conflictConditions.length > 0) {
        const existingCategory = await this.categoryRepository.findOne({
          where: conflictConditions
        });

        if (existingCategory && existingCategory.category_id !== id) {
          if (existingCategory.category_type === updateDto.category_type) {
            throw new ConflictException(`Equipment category with type '${updateDto.category_type}' already exists`);
          }
          if (existingCategory.ceir_standard_code === updateDto.ceir_standard_code) {
            throw new ConflictException(`Equipment category with CEIR standard code '${updateDto.ceir_standard_code}' already exists`);
          }
        }
      }
    }

    const updateData = {
      ...updateDto,
      updated_by: updateDto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
    };
    Object.assign(category, updateData);
    return await this.categoryRepository.save(category);
  }

  async remove(id: string): Promise<void> {
    const category = await this.findOne(id);
    await this.categoryRepository.softDelete(id);
  }

  async restore(id: string): Promise<CeirEquipmentTypeCategories> {
    await this.categoryRepository.restore(id);
    return await this.findOne(id);
  }

  async activate(id: string, userId: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.findOne(id);
    category.is_active = true;
    category.updated_by = userId;
    return await this.categoryRepository.save(category);
  }

  async deactivate(id: string, userId: string): Promise<CeirEquipmentTypeCategories> {
    const category = await this.findOne(id);
    category.is_active = false;
    category.updated_by = userId;
    return await this.categoryRepository.save(category);
  }

  async findByFrequencyBand(frequencyBand: string): Promise<CeirEquipmentTypeCategories[]> {
    return await this.categoryRepository
      .createQueryBuilder('category')
      .where(':frequencyBand = ANY(category.supported_frequency_bands)', { frequencyBand })
      .andWhere('category.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('category.creator', 'creator')
      .leftJoinAndSelect('category.updater', 'updater')
      .orderBy('category.category_name', 'ASC')
      .getMany();
  }

  async findByStandard(standard: string): Promise<CeirEquipmentTypeCategories[]> {
    return await this.categoryRepository
      .createQueryBuilder('category')
      .where(':standard = ANY(category.required_standards)', { standard })
      .andWhere('category.is_active = :isActive', { isActive: true })
      .leftJoinAndSelect('category.creator', 'creator')
      .leftJoinAndSelect('category.updater', 'updater')
      .orderBy('category.category_name', 'ASC')
      .getMany();
  }

  async findByTestRequirement(requiresSar?: boolean, requiresEmc?: boolean, requiresRf?: boolean): Promise<CeirEquipmentTypeCategories[]> {
    const query = this.categoryRepository
      .createQueryBuilder('category')
      .where('category.is_active = :isActive', { isActive: true });

    if (requiresSar !== undefined) {
      query.andWhere('category.requires_sar_testing = :requiresSar', { requiresSar });
    }

    if (requiresEmc !== undefined) {
      query.andWhere('category.requires_emc_testing = :requiresEmc', { requiresEmc });
    }

    if (requiresRf !== undefined) {
      query.andWhere('category.requires_rf_testing = :requiresRf', { requiresRf });
    }

    return await query
      .leftJoinAndSelect('category.creator', 'creator')
      .leftJoinAndSelect('category.updater', 'updater')
      .orderBy('category.category_name', 'ASC')
      .getMany();
  }

  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byType: Record<string, number>;
    requiresSar: number;
    requiresEmc: number;
    requiresRf: number;
  }> {
    const [total, active, inactive, requiresSar, requiresEmc, requiresRf] = await Promise.all([
      this.categoryRepository.count(),
      this.categoryRepository.count({ where: { is_active: true } }),
      this.categoryRepository.count({ where: { is_active: false } }),
      this.categoryRepository.count({ where: { requires_sar_testing: true } }),
      this.categoryRepository.count({ where: { requires_emc_testing: true } }),
      this.categoryRepository.count({ where: { requires_rf_testing: true } }),
    ]);

    const categories = await this.categoryRepository.find();
    const byType = categories.reduce((acc, category) => {
      acc[category.category_type] = (acc[category.category_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      inactive,
      byType,
      requiresSar,
      requiresEmc,
      requiresRf,
    };
  }
}
