import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from '../entities/department.entity';
import { UpdateDepartmentDto } from 'src/dto/department/update-department.dto';
import { CreateDepartmentDto } from 'src/dto/department/create-department.dto';
import { paginate, PaginateQuery, PaginateConfig } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class DepartmentService extends Department {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {
    super();
  }

  protected getEntityName(): string {
    return 'Department';
  }

  protected getIdField(): string {
    return 'department_id';
  }

  async create(createDto: CreateDepartmentDto): Promise<Department> {
    const department = this.departmentRepository.create(createDto);
    return this.departmentRepository.save(department);
  }

  async findAll(): Promise<Department[]> {
    return super.findAll({
      relations: ['creator', 'updater', 'department_manager'],
    });
  }

  async findAllPaginated(query: PaginateQuery): Promise<PaginatedResult<Department>> {
    const config: PaginateConfig<Department> = {
      relations: ['creator', 'updater', 'department_manager'],
      sortableColumns: ['code', 'name', 'email', 'created_at'],
      searchableColumns: ['code', 'name', 'email'],
      defaultSortBy: [['code', 'ASC']],
      defaultLimit: 50,
      maxLimit: 100,
    };

    const result = await paginate(query, this.departmentRepository, config);
    return PaginationTransformer.transform<Department>(result);
  }

  async findOne(id: string): Promise<Department> {
    return super.findOne(id, {
      relations: ['creator', 'updater', 'department_manager'],
    });
  }

  async update(id: string, updateDto: UpdateDepartmentDto): Promise<Department> {
    const department = await this.findOne(id);
    Object.assign(department, updateDto);
    return this.departmentRepository.save(department);
  }

  async remove(id: string): Promise<void> {
    return super.softDelete(id);
  }

  async restore(id: string): Promise<Department> {
    return super.restore(id);
  }

  async findDeleted(): Promise<Department[]> {
    return super.findDeleted({
      relations: ['creator', 'updater', 'department_manager'],
    });
  }
}
