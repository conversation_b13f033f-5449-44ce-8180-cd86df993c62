import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LicenseCategoryDocumentsService } from './license-category-documents.service';
import { LicenseCategoryDocumentsController } from './license-category-documents.controller';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LicenseCategoryDocument, User])],
  controllers: [LicenseCategoryDocumentsController],
  providers: [LicenseCategoryDocumentsService],
  exports: [LicenseCategoryDocumentsService],
})
export class LicenseCategoryDocumentsModule {}
