import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
  <PERSON>Length,
  IsArray,
  IsIn,
  IsDateString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { 
  TEST_TYPES, 
  TEST_RESULTS, 
  REPORT_STATUSES 
} from '../../entities/ceir-test-reports.entity';

export class CreateCeirTestReportDto {
  @ApiProperty({
    description: 'Unique report number',
    example: 'TR-2024-001-RF',
    maxLength: 100
  })
  @IsString({ message: 'Report number must be a string' })
  @MaxLength(100, { message: 'Report number must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Report number is required' })
  report_number: string;

  @ApiProperty({
    description: 'Application ID this report belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Application ID must be a valid UUID' })
  application_id?: string;

  @ApiProperty({
    description: 'Device ID this report is for',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID('4', { message: 'Device ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Device ID is required' })
  device_id: string;

  @ApiProperty({
    description: 'Certification body that conducted the test',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID('4', { message: 'Certification body ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Certification body ID is required' })
  certification_body_id: string;

  @ApiProperty({
    description: 'Technical standard tested against',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID('4', { message: 'Technical standard ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Technical standard ID is required' })
  technical_standard_id: string;

  @ApiProperty({
    description: 'Type of test performed',
    enum: TEST_TYPES,
    example: 'rf_performance'
  })
  @IsString({ message: 'Test type must be a string' })
  @IsIn(TEST_TYPES, { message: 'Test type must be one of the valid test types' })
  @IsNotEmpty({ message: 'Test type is required' })
  test_type: string;

  @ApiProperty({
    description: 'Title of the test report',
    example: 'RF Performance Test Report for Samsung Galaxy S21',
    maxLength: 255
  })
  @IsString({ message: 'Report title must be a string' })
  @MaxLength(255, { message: 'Report title must not exceed 255 characters' })
  @IsNotEmpty({ message: 'Report title is required' })
  report_title: string;

  @ApiProperty({
    description: 'Date when testing was conducted',
    example: '2024-01-15'
  })
  @IsDateString({}, { message: 'Test date must be a valid ISO 8601 date string' })
  @IsNotEmpty({ message: 'Test date is required' })
  test_date: string;

  @ApiProperty({
    description: 'Date when the report was issued',
    example: '2024-01-20'
  })
  @IsDateString({}, { message: 'Report date must be a valid ISO 8601 date string' })
  @IsNotEmpty({ message: 'Report date is required' })
  report_date: string;

  @ApiProperty({
    description: 'Date when the report expires',
    example: '2027-01-20',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Expiry date must be a valid ISO 8601 date string' })
  expiry_date?: string;

  @ApiProperty({
    description: 'Overall test result',
    enum: TEST_RESULTS,
    example: 'pass'
  })
  @IsString({ message: 'Test result must be a string' })
  @IsIn(TEST_RESULTS, { message: 'Test result must be one of the valid test results' })
  @IsNotEmpty({ message: 'Test result is required' })
  test_result: string;

  @ApiProperty({
    description: 'Current status of the report',
    enum: REPORT_STATUSES,
    example: 'approved',
    default: 'draft'
  })
  @IsString({ message: 'Report status must be a string' })
  @IsIn(REPORT_STATUSES, { message: 'Report status must be one of the valid report statuses' })
  report_status: string = 'draft';

  @ApiProperty({
    description: 'Test methods used',
    type: 'array',
    example: ['Conducted spurious emissions', 'Radiated spurious emissions'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Test methods must be an array' })
  @IsString({ each: true, message: 'Each test method must be a string' })
  test_methods?: string[];

  @ApiProperty({
    description: 'Frequency bands tested',
    type: 'array',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Tested frequency bands must be an array' })
  @IsString({ each: true, message: 'Each frequency band must be a string' })
  tested_frequency_bands?: string[];

  @ApiProperty({
    description: 'Maximum measured power in dBm',
    example: 32.5,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Maximum measured power must be a number' })
  @Min(-50, { message: 'Maximum measured power must be at least -50 dBm' })
  @Max(50, { message: 'Maximum measured power must not exceed 50 dBm' })
  max_measured_power_dbm?: number;

  @ApiProperty({
    description: 'SAR value if applicable (W/kg)',
    example: 1.2,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'SAR value must be a number' })
  @Min(0, { message: 'SAR value must be non-negative' })
  @Max(10, { message: 'SAR value must not exceed 10 W/kg' })
  sar_value_wkg?: number;

  @ApiProperty({
    description: 'Test conditions and environment',
    example: 'Temperature: 23°C, Humidity: 45%, Atmospheric pressure: 86-106 kPa',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Test conditions must be a string' })
  test_conditions?: string;

  @ApiProperty({
    description: 'Equipment used for testing',
    example: 'Rohde & Schwarz FSW Signal Analyzer, Keysight E5071C Network Analyzer',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Test equipment must be a string' })
  test_equipment?: string;

  @ApiProperty({
    description: 'Detailed test results and measurements',
    example: 'All measurements within acceptable limits as per standard requirements',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Test results details must be a string' })
  test_results_details?: string;

  @ApiProperty({
    description: 'Deviations or non-conformities found',
    example: 'Minor deviation in spurious emission at 1.8 GHz band, within acceptable tolerance',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Deviations must be a string' })
  deviations?: string;

  @ApiProperty({
    description: 'Recommendations or corrective actions',
    example: 'No corrective actions required. Equipment meets all requirements.',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Recommendations must be a string' })
  recommendations?: string;

  @ApiProperty({
    description: 'Name of the test engineer',
    example: 'Dr. John Smith',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Test engineer must be a string' })
  @MaxLength(255, { message: 'Test engineer must not exceed 255 characters' })
  test_engineer?: string;

  @ApiProperty({
    description: 'Name of the report reviewer',
    example: 'Prof. Jane Doe',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Reviewed by must be a string' })
  @MaxLength(255, { message: 'Reviewed by must not exceed 255 characters' })
  reviewed_by?: string;

  @ApiProperty({
    description: 'Name of the report approver',
    example: 'Dr. Michael Johnson',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Approved by must be a string' })
  @MaxLength(255, { message: 'Approved by must not exceed 255 characters' })
  approved_by?: string;

  @ApiProperty({
    description: 'File path to the report document',
    example: '/documents/test-reports/TR-2024-001-RF.pdf',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Report file path must be a string' })
  @MaxLength(500, { message: 'Report file path must not exceed 500 characters' })
  report_file_path?: string;

  @ApiProperty({
    description: 'Whether this report is currently valid',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is valid must be a boolean' })
  is_valid: boolean = true;

  @ApiProperty({
    description: 'Additional notes or comments',
    example: 'Report generated using automated test system',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;

  @ApiProperty({
    description: 'User ID who is creating this test report (optional, can be extracted from JWT)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided' })
  created_by?: string;
}
