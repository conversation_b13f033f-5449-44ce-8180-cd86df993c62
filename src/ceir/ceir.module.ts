import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { CeirManagementController } from './controllers/ceir-management.controller';
import { CeirTestingController } from './controllers/ceir-testing.controller';

// Services
import { CeirCertificationBodiesService } from './services/ceir-certification-bodies.service';
import { CeirEquipmentCategoriesService } from './services/ceir-equipment-categories.service';
import { CeirEquipmentSpecificationsService } from './services/ceir-equipment-specifications.service';
import { CeirTechnicalStandardsService } from './services/ceir-technical-standards.service';
import { CeirTestReportsService } from './services/ceir-test-reports.service';

// Entities
import { CeirCertificationBodies } from './entities/ceir-certification-bodies.entity';
import { CeirEquipmentTypeCategories } from './entities/ceir-equipment-type-categories.entity';
import { CeirEquipmentSpecifications } from './entities/ceir-equipment-specifications.entity';
import { CeirTechnicalStandards } from './entities/ceir-technical-standards.entity';
import { CeirTestReports } from './entities/ceir-test-reports.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CeirCertificationBodies,
      CeirEquipmentTypeCategories,
      CeirEquipmentSpecifications,
      CeirTechnicalStandards,
      CeirTestReports,
    ]),
  ],
  controllers: [
    CeirManagementController,
    CeirTestingController,
  ],
  providers: [
    CeirCertificationBodiesService,
    CeirEquipmentCategoriesService,
    CeirEquipmentSpecificationsService,
    CeirTechnicalStandardsService,
    CeirTestReportsService,
  ],
  exports: [
    CeirCertificationBodiesService,
    CeirEquipmentCategoriesService,
    CeirEquipmentSpecificationsService,
    CeirTechnicalStandardsService,
    CeirTestReportsService,
  ],
})
export class CeirModule {}
