import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Organization } from './organization.entity';

@Entity('applicants')
export class Applicants {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  applicant_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  business_registration_number?: string;

  @Column({ type: 'varchar' })
  tpin: string;

  @Column({ type: 'varchar', nullable: true })
  website: string;

  @Column({ type: 'varchar' })
  email: string;

  @Column({ type: 'varchar', length: 20 })
  phone: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  fax?: string;

  @Column({ type: 'text', nullable: true })
  level_of_insurance_cover?: string;

  @Column({ type: 'date' })
  date_incorporation: Date;

  @Column({ type: 'varchar' })
  place_incorporation: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // 🔗 User Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    this.applicant_id = this.applicant_id || uuidv4();
  }
}
