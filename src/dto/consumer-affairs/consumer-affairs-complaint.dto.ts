import { PartialType } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ength, IsNotEmpty } from 'class-validator';
import { ComplaintCategory, ComplaintStatus, ComplaintPriority } from 'src/consumer-affairs/consumer-affairs-constants';

export class CreateConsumerAffairsComplaintDto {
  @IsString()
  @MinLength(5, { message: 'Title must be at least 5 characters long' })
  @MaxLength(255, { message: 'Title must not exceed 255 characters' })
  title: string;

  @IsString()
  @MinLength(20, { message: 'Description must be at least 20 characters long' })
  description: string;

  @IsEnum(ComplaintCategory, { message: 'Invalid complaint category' })
  category: ComplaintCategory;

  @IsOptional()
  @IsEnum(ComplaintPriority, { message: 'Invalid priority level' })
  priority?: ComplaintPriority;
}

export class UpdateConsumerAffairsComplaintDto extends PartialType(CreateConsumerAffairsComplaintDto) {
  @IsOptional()
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status?: ComplaintStatus;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsString()
  resolution?: string;

  @IsOptional()
  @IsString()
  internal_notes?: string;

  @IsOptional()
  @IsDateString()
  resolved_at?: Date;
}

export class CreateConsumerAffairsComplaintAttachmentDto {
  @IsUUID(4, { message: 'Invalid complaint ID' })
  complaint_id: string;

  @IsString()
  @MinLength(1, { message: 'File name is required' })
  file_name: string;

  @IsString()
  @MinLength(1, { message: 'File path is required' })
  file_path: string;

  @IsString()
  @MinLength(1, { message: 'File type is required' })
  file_type: string;

  file_size: number;
}

export class UpdateConsumerAffairsComplaintStatusDto {
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status: ComplaintStatus;

  @IsOptional()
  @IsString()
  comment?: string;
}

export class UpdateConsumerAffairsComplaineeDto {
  @IsString({ message: 'Invalid business registration number!'})
  @IsNotEmpty({ message: "Complainee's business registration number is required to link complaint to complainee"})
  complainee_reg_number: string;
}

export class ConsumerAffairsComplaintFilterDto {
  @IsOptional()
  @IsEnum(ComplaintCategory, { message: 'Invalid complaint category' })
  category?: ComplaintCategory;

  @IsOptional()
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status?: ComplaintStatus;

  @IsOptional()
  @IsEnum(ComplaintPriority, { message: 'Invalid priority level' })
  priority?: ComplaintPriority;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid complainant ID' })
  complainant_id?: string;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for from_date' })
  from_date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for to_date' })
  to_date?: string;

  @IsOptional()
  @IsString()
  search?: string; // For searching in title and description

  @IsOptional()
  page?: number;

  @IsOptional()
  limit?: number;

  @IsOptional()
  sort_by?: string;

  @IsOptional()
  sort_order?: 'ASC' | 'DESC';
}
