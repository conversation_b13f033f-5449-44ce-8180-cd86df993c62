import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>n,
  <PERSON>reateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';
import { Organization } from './organization.entity';

@Entity('shortcodes')
export class Shortcode {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  shortcode_id: string;

  @Column({ type: 'varchar', length: 5, unique: true })
  shortcode: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  application_id?: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  assigned_to?: string;

  @Column({ type: 'int', nullable: false, default: 3 })
  shortcode_length: number;

  @Column({ type: 'varchar', length: 10, unique: false, nullable: false, default: 'community' })
  audience: string;

  @Column({ type: 'varchar', length: 10, unique: false, nullable: false, default: 'inactive' })
  status: string;

  @Column({ type: 'varchar', length: 20, unique: false, nullable: false, default: 'reserved' })
  category: string;
  
  @Column({ type: 'varchar', length: 255, nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  notes?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => Applications, {nullable: true, onDelete: 'SET NULL'})
  @JoinColumn({ name: 'application_id' })
  application?: Applications;

  @ManyToOne(() => Organization, {nullable: true, onDelete: 'SET NULL'})
  @JoinColumn({ name: 'assigned_to' })
  assignee?: Organization;

  @BeforeInsert()
  generateId() {
    if (!this.shortcode_id) {
      this.shortcode_id = uuidv4();
    }
  }
}
