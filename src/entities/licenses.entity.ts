import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';

export enum LicenseStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  REVOKED = 'revoked',
  UNDER_REVIEW = 'under_review',
}

@Entity('licenses')
export class Licenses {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  license_id: string;

  @Column({ type: 'varchar', nullable: true })
  description: string; // Pattern: ^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$

  @Column({ type: 'varchar', unique: true })
  license_number: string; // Pattern: ^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$

  @Column({ type: 'uuid' })
  application_id: string;

  @Column({
    type: 'enum',
    enum: LicenseStatus,
    default: LicenseStatus.ACTIVE,
  })
  status: LicenseStatus;

  @Column({ type: 'date' })
  issue_date: Date;

  @Column({ type: 'date' })
  expiry_date: Date;

  @Column({ type: 'uuid' })
  issued_by: string;

  @Column({ type: 'varchar', nullable: true })
  code: string;

  @Column({ type: 'text', nullable: true })
  conditions?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications)
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'issued_by' })
  issuer: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.license_id) {
      this.license_id = uuidv4();
    }
  }
}
