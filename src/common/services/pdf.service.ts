import { Injectable, Logger } from '@nestjs/common';
import * as handlebars from 'handlebars';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as qrcode from 'qrcode';

export interface PDFGenerationOptions {
  template: string;
  data: any;
  filename: string;
  format?: 'A4' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
}

export interface QRCodeOptions {
  text: string;
  width?: number;
  height?: number;
  margin?: number;
}

@Injectable()
export class PDFService {
  private readonly logger = new Logger(PDFService.name);

  private readonly allowedTemplates = [
    'certificate',
    'license-summary',
    'receipt',
    'standards-licence',
    'postal-licence',
    'short-code',
  ];

  private readonly templateCache = new Map<string, HandlebarsTemplateDelegate>();

  constructor() {
    this.registerHelpers();
  }

  clearTemplateCache(): void {
    this.templateCache.clear();
    this.logger.log('Template cache cleared');
  }

  async generateQRCode(options: QRCodeOptions): Promise<string> {
    try {
      const qrOptions = {
        width: options.width || 200,
        height: options.height || 200,
        margin: options.margin || 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      };

      return await qrcode.toDataURL(options.text, qrOptions);
    } catch (error) {
      this.logger.error('Failed to generate QR code:', error);
      throw new Error('QR code generation failed');
    }
  }

  private loadTemplate(templateName: string, forceReload = false): HandlebarsTemplateDelegate {
    if (!this.allowedTemplates.includes(templateName)) {
      throw new Error(`Template "${templateName}" is not allowed`);
    }

    if (!forceReload && this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName)!;
    }

    try {
      const templatePath = join(process.cwd(), 'src', 'templates', `${templateName}.hbs`);
      const templateContent = readFileSync(templatePath, 'utf8');

      if (templateContent.includes('||')) {
        this.logger.warn(`Template ${templateName} contains '||' syntax. Use 'fallback' helper instead.`);
      }

      const compiledTemplate = handlebars.compile(templateContent);
      this.templateCache.set(templateName, compiledTemplate);
      this.logger.log(`Template ${templateName} loaded and cached`);

      return compiledTemplate;
    } catch (error) {
      this.logger.error(`Failed to load template ${templateName}:`, error);
      throw new Error(`Template loading failed: ${error.message}`);
    }
  }

  async generatePDF(options: PDFGenerationOptions): Promise<Buffer> {
    const template = this.loadTemplate(options.template);

    if (options.data.verificationUrl) {
      options.data.qrCodeDataUrl = await this.generateQRCode({
        text: options.data.verificationUrl,
      });
    }

    let html: string;
    try {
      html = template(options.data);
    } catch (templateError) {
      if (templateError.message.includes('Parse error')) {
        this.logger.warn(`Template parse error, reloading ${options.template}`);
        const reloaded = this.loadTemplate(options.template, true);
        html = reloaded(options.data);
      } else {
        throw templateError;
      }
    }

    // 🔴 At this point, generating an actual PDF will not work
    // You may replace this with a third-party service or a different renderer
    this.logger.warn('PDF generation is disabled — no rendering engine available.');
    throw new Error('PDF generation is not supported in this environment');
  }

  registerHelpers(): void {
    handlebars.registerHelper('formatDate', (date: Date | string, format?: string) => {
      const d = new Date(date);
      return format === 'long'
        ? d.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })
        : d.toLocaleDateString();
    });

    handlebars.registerHelper('formatCurrency', (amount: number | string) => {
      const value = typeof amount === 'string' ? parseFloat(amount) : amount;
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(value);
    });

    handlebars.registerHelper('uppercase', (str: string) => (str ? str.toUpperCase() : ''));

    handlebars.registerHelper('ifEquals', function (a: any, b: any, options: any) {
      return a == b ? options.fn(this) : options.inverse(this);
    });

    handlebars.registerHelper('fallback', function (...args: any[]) {
      const values = args.slice(0, -1);
      for (const value of values) {
        if (value && value !== '' && value !== null && value !== undefined) {
          return value;
        }
      }
      return '';
    });

    this.logger.log('📦 Handlebars helpers registered');
  }

  async onModuleDestroy(): Promise<void> {
    this.logger.log('🧹 PDFService cleanup complete (no browser to close)');
  }
}
