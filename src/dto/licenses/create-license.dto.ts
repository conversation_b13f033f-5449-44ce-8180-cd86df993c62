import { IsString, IsEnum, IsOptional, IsUUID, IsDateString, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LicenseStatus } from '../../entities/licenses.entity';

export class CreateLicenseDto {
  @ApiProperty({
    description: 'Licence number in format LIC-YYYY-MM-NNN',
    example: 'LIC-2024-01-001',
    pattern: '^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$'
  })
  @IsString()
  @Matches(/^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$/, { message: 'Invalid licence number format' })
  license_number: string;

  @ApiProperty({
    description: 'UUID of the associated application',
    format: 'uuid'
  })
  @IsUUID()
  application_id: string;


  @ApiPropertyOptional({
    description: 'Licence status',
    enum: LicenseStatus,
    default: LicenseStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(LicenseStatus)
  status?: LicenseStatus;

  @ApiProperty({
    description: 'Licence issue date',
    format: 'date',
    example: '2024-01-15'
  })
  @IsDateString()
  issue_date: string;

  @ApiProperty({
    description: 'Licence expiry date',
    format: 'date',
    example: '2025-01-15'
  })
  @IsDateString()
  expiry_date: string;

  @ApiProperty({
    description: 'UUID of the user who issued the licence',
    format: 'uuid'
  })
  @IsUUID()
  issued_by: string;

  @ApiPropertyOptional({
    description: 'Additional licence conditions and terms'
  })
  @IsOptional()
  @IsString()
  conditions?: string;

  @ApiPropertyOptional({
    description: 'Description licence conditions and terms'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Description licence conditions and terms'
  })
  @IsOptional()
  @IsString()
  code?: string;
}
