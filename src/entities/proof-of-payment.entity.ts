import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Payment } from './payment.entity';

export enum ProofOfPaymentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export enum PaymentMethod {
  BANK_TRANSFER = 'Bank Transfer',
  MOBILE_MONEY = 'Mobile Money',
  CREDIT_CARD = 'Credit Card',
  CASH = 'Cash',
}

@Entity('proof_of_payments')
export class ProofOfPayment {
  @PrimaryGeneratedColumn('uuid')
  proof_id: string;

  @Column()
  transaction_reference: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column()
  currency: string;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  payment_method: PaymentMethod;

  @Column({ type: 'date' })
  payment_date: Date;

  @Column()
  document_path: string;

  @Column()
  original_filename: string;

  @Column()
  file_size: number;

  @Column()
  mime_type: string;

  @Column({
    type: 'enum',
    enum: ProofOfPaymentStatus,
    default: ProofOfPaymentStatus.PENDING,
  })
  status: ProofOfPaymentStatus;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'text', nullable: true })
  review_notes?: string;

  @Column({ nullable: true })
  reviewed_by?: string;

  @Column({ type: 'timestamp', nullable: true })
  reviewed_at?: Date;

  // Relations
  @Column()
  payment_id: string;

  // TEMPORARILY COMMENTED OUT - MISSING RELATIONSHIP
  // @ManyToOne(() => Payment, (payment) => payment.proof_of_payments, { eager: true })
  // @JoinColumn({ name: 'payment_id' })
  // payment: Payment;

  @Column()
  submitted_by: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'submitted_by' })
  user: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'reviewed_by' })
  reviewer?: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
