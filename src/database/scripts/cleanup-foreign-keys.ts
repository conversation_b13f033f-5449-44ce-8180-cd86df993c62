import { DataSource } from 'typeorm';
import { AppDataSource } from '../../data-source';

/**
 * <PERSON><PERSON>t to clean up foreign key constraint issues in the database
 * This script should be run before applying migrations or seeding data
 */
async function cleanupForeignKeys() {
  console.log('🔧 Starting database foreign key cleanup...');
  
  try {
    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    console.log('📊 Analyzing foreign key constraint issues...');

    // 1. Check and fix addresses table
    console.log('\n1️⃣ Checking addresses table...');
    
    // Check for invalid updated_by references
    const invalidUpdatedBy = await AppDataSource.query(`
      SELECT COUNT(*) as count 
      FROM addresses a 
      LEFT JOIN users u ON a.updated_by = u.user_id 
      WHERE a.updated_by IS NOT NULL AND u.user_id IS NULL
    `);
    
    console.log(`   Found ${invalidUpdatedBy[0].count} addresses with invalid updated_by references`);
    
    if (invalidUpdatedBy[0].count > 0) {
      await AppDataSource.query(`
        UPDATE addresses a 
        LEFT JOIN users u ON a.updated_by = u.user_id 
        SET a.updated_by = NULL 
        WHERE a.updated_by IS NOT NULL AND u.user_id IS NULL
      `);
      console.log(`   ✅ Fixed ${invalidUpdatedBy[0].count} invalid updated_by references`);
    }

    // Check for invalid created_by references
    const invalidCreatedBy = await AppDataSource.query(`
      SELECT COUNT(*) as count 
      FROM addresses a 
      LEFT JOIN users u ON a.created_by = u.user_id 
      WHERE a.created_by IS NOT NULL AND u.user_id IS NULL
    `);
    
    console.log(`   Found ${invalidCreatedBy[0].count} addresses with invalid created_by references`);
    
    if (invalidCreatedBy[0].count > 0) {
      // Get a system user or any available user
      const systemUser = await AppDataSource.query(`
        SELECT user_id FROM users 
        ORDER BY created_at ASC 
        LIMIT 1
      `);
      
      if (systemUser.length > 0) {
        await AppDataSource.query(`
          UPDATE addresses a 
          LEFT JOIN users u ON a.created_by = u.user_id 
          SET a.created_by = ? 
          WHERE a.created_by IS NOT NULL AND u.user_id IS NULL
        `, [systemUser[0].user_id]);
        console.log(`   ✅ Fixed ${invalidCreatedBy[0].count} invalid created_by references`);
      } else {
        console.log(`   ⚠️ No users available to assign as created_by. Setting to NULL.`);
        await AppDataSource.query(`
          UPDATE addresses a 
          LEFT JOIN users u ON a.created_by = u.user_id 
          SET a.created_by = NULL 
          WHERE a.created_by IS NOT NULL AND u.user_id IS NULL
        `);
      }
    }

    // 2. Check other tables that might have similar issues
    const tablesToCheck = [
      'applications',
      'applicants', 
      'documents',
      'contacts',
      'license_categories',
      'license_types',
      'organizations',
      'departments'
    ];

    for (const table of tablesToCheck) {
      console.log(`\n2️⃣ Checking ${table} table...`);
      
      try {
        // Check if table exists and has the columns
        const tableInfo = await AppDataSource.query(`
          SELECT COLUMN_NAME 
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = '${table}' 
          AND COLUMN_NAME IN ('created_by', 'updated_by')
        `);
        
        if (tableInfo.length === 0) {
          console.log(`   ⏭️ Table ${table} doesn't have created_by/updated_by columns, skipping...`);
          continue;
        }

        for (const column of tableInfo) {
          const columnName = column.COLUMN_NAME;
          
          const invalidRefs = await AppDataSource.query(`
            SELECT COUNT(*) as count 
            FROM ${table} t 
            LEFT JOIN users u ON t.${columnName} = u.user_id 
            WHERE t.${columnName} IS NOT NULL AND u.user_id IS NULL
          `);
          
          if (invalidRefs[0].count > 0) {
            console.log(`   Found ${invalidRefs[0].count} invalid ${columnName} references in ${table}`);
            
            if (columnName === 'updated_by') {
              // Set updated_by to NULL
              await AppDataSource.query(`
                UPDATE ${table} t 
                LEFT JOIN users u ON t.${columnName} = u.user_id 
                SET t.${columnName} = NULL 
                WHERE t.${columnName} IS NOT NULL AND u.user_id IS NULL
              `);
              console.log(`   ✅ Fixed ${invalidRefs[0].count} invalid ${columnName} references in ${table}`);
            } else if (columnName === 'created_by') {
              // For created_by, try to assign to a system user or set to NULL
              const systemUser = await AppDataSource.query(`
                SELECT user_id FROM users 
                ORDER BY created_at ASC 
                LIMIT 1
              `);
              
              if (systemUser.length > 0) {
                await AppDataSource.query(`
                  UPDATE ${table} t 
                  LEFT JOIN users u ON t.${columnName} = u.user_id 
                  SET t.${columnName} = ? 
                  WHERE t.${columnName} IS NOT NULL AND u.user_id IS NULL
                `, [systemUser[0].user_id]);
                console.log(`   ✅ Fixed ${invalidRefs[0].count} invalid ${columnName} references in ${table}`);
              } else {
                await AppDataSource.query(`
                  UPDATE ${table} t 
                  LEFT JOIN users u ON t.${columnName} = u.user_id 
                  SET t.${columnName} = NULL 
                  WHERE t.${columnName} IS NOT NULL AND u.user_id IS NULL
                `);
                console.log(`   ✅ Set ${invalidRefs[0].count} invalid ${columnName} references to NULL in ${table}`);
              }
            }
          } else {
            console.log(`   ✅ No invalid ${columnName} references found in ${table}`);
          }
        }
      } catch (error) {
        console.log(`   ⚠️ Error checking table ${table}:`, error.message);
      }
    }

    console.log('\n✅ Database foreign key cleanup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • Fixed invalid foreign key references');
    console.log('   • Set orphaned updated_by fields to NULL');
    console.log('   • Assigned orphaned created_by fields to system user or NULL');
    console.log('\n🚀 You can now run migrations and seeders safely.');

  } catch (error) {
    console.error('❌ Error during foreign key cleanup:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the cleanup if this file is executed directly
if (require.main === module) {
  cleanupForeignKeys()
    .then(() => {
      console.log('✅ Cleanup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Cleanup failed:', error);
      process.exit(1);
    });
}

export { cleanupForeignKeys };