import { PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
  IsEnum,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateDeviceDto } from './create-device.dto';
import { DeviceApprovalStatus } from './device-enums';

export class UpdateDeviceDto extends PartialType(CreateDeviceDto) {
  @ApiPropertyOptional({
    description: 'Application ID if device is being added as part of an application',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Application ID must be a valid UUID if provided.' })
  application_id?: string;



  @ApiPropertyOptional({
    description: 'Equipment category ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Equipment category ID must be a valid UUID if provided.' })
  equipment_category_id?: string;

  @ApiPropertyOptional({
    description: 'IMEI number - exactly 15 digits (International Mobile Equipment Identity). Format: AA-BBBBBB-CCCCCC-D (TAC-Serial-Check)',
    example: '351756051523999',
    pattern: '^\\d{15}$',
    minLength: 15,
    maxLength: 15,
  })
  @IsOptional()
  @IsString({ message: 'IMEI must be a string.' })
  @Matches(/^\d{15}$/, { message: 'IMEI must be exactly 15 digits' })
  imei?: string;

  @ApiPropertyOptional({
    description: 'Type of device (e.g., Mobile Phone, Modem, Tablet)',
    example: 'Mobile Phone',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Device type must be a string.' })
  @Length(1, 255, { message: 'Device type must be between 1 and 255 characters' })
  device_type?: string;

  @ApiPropertyOptional({
    description: 'Model name of the device',
    example: 'iPhone 15 Pro',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Model name must be a string.' })
  @Length(1, 100, { message: 'Model name must be between 1 and 100 characters' })
  model_name?: string;

  @ApiPropertyOptional({
    description: 'Device serial number - 6-50 alphanumeric characters (manufacturer-specific)',
    example: 'SN123456789ABC',
    pattern: '^[A-Za-z0-9]+$',
    minLength: 6,
    maxLength: 50,
  })
  @IsOptional()
  @IsString({ message: 'Device serial number must be a string.' })
  @Length(6, 50, { message: 'Device serial number must be between 6 and 50 characters' })
  @Matches(/^[A-Za-z0-9]+$/, { message: 'Device serial number must contain only alphanumeric characters' })
  device_serial_number?: string;

  @ApiPropertyOptional({
    description: 'Device approval status (optional for updates)',
    enum: DeviceApprovalStatus,
    example: DeviceApprovalStatus.APPROVED,
  })
  @IsOptional()
  @IsEnum(DeviceApprovalStatus, { message: 'Approval status must be a valid status.' })
  approval_status?: DeviceApprovalStatus;

  @ApiPropertyOptional({
    description: 'Device approval number (assigned when approved)',
    example: 'DEV-2024-001',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Device approval number must be a string.' })
  @Length(1, 100, { message: 'Device approval number must be between 1 and 100 characters' })
  device_approval_number?: string;

  @ApiPropertyOptional({
    description: 'Device approval date',
    example: '2024-01-15',
    type: 'string',
    format: 'date',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Device approval date must be a valid ISO 8601 date string.' })
  device_approval_date?: string;

  @ApiPropertyOptional({
    description: 'Notes about the approval process or status',
    example: 'Approved for use in Malawi telecommunications network',
  })
  @IsOptional()
  @IsString({ message: 'Approval notes must be a string.' })
  approval_notes?: string;

  @ApiPropertyOptional({
    description: 'User ID who updated this device record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID.' })
  updated_by?: string;
}
