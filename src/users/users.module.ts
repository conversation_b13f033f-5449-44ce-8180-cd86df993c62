import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from '../entities/user.entity';
import { Role } from '../entities/role.entity';
import { UserIdentification } from '../entities/user-identification.entity';
import { AuditInterceptor } from '../common/interceptors/audit.interceptor';
import { AuditTrailModule } from '../audit-trail/audit-trail.module';

import { memoryStorage } from 'multer';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, UserIdentification]),
    AuditTrailModule,

    MulterModule.register({
      storage: memoryStorage(),
      fileFilter: (req, file, callback) => {
        console.log('Multer fileFilter:', { mimetype: file.mimetype, originalname: file.originalname });
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed'), false);
        }
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    {
      provide: APP_INTERCEPTOR,
      useClass: AuditInterceptor,
    },
  ],
  exports: [UsersService],
})
export class UsersModule {}
