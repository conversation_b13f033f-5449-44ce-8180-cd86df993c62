trigger:
  branches:
    include:
      - development

resources:
  repositories:
    # Define the source Git repository
    - repository: templates
      type: git
      name: global-softwarehouse/Quadraat-infrastructure-azure

variables:
  appName: 'macra-services' ##change this one
  environment: 'dev' ##change this one
  dockerRegistryServiceConnection: 'quadraatglobal'
  tag: '$(Build.BuildId)'
  acrLoginServer: 'quadraatglobal.azurecr.io'
  containerAppMigration: '$(appName)-$(environment)-migration'
  containerAppRun: '$(appName)-$(environment)-app'
  resourceGroup: '$(appName)-$(environment)' 
  startupCommandMigration: 'npm run migration:run' ## Set the correct startup command and add in to the deploy step
  startupCommandRun: 'node dist/main.js' ## Set the correct startup command and add in to the deploy step

stages:
- stage: BuildAndPush
  displayName: Build and Push Docker Image
  jobs:
  - job: Build
    displayName: Build and Push
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      displayName: Build and Push Docker Image
      inputs:
        command: buildAndPush
        repository: $(appName)
        dockerfile: '**/Dockerfile'
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: $(tag)

- stage: TrivyScan
  displayName: scan image for vulnerabilities
  jobs:
  - job: Trivy
    displayName: scan image for vulnerabilities
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: Docker@2
      inputs:
        containerRegistry: $(dockerRegistryServiceConnection)
        command: 'login'
        addPipelineData: false
        addBaseImageData: false
    - task: trivy@1
      inputs:
        version: 'latest'
        image: '$(acrLoginServer)/$(appName):$(tag)'
        loginDockerConfig: true
        ignoreUnfixed: true
        tableOutput: true

- stage: UpdateContainerApps
  displayName: Update Azure Container Apps
  dependsOn: BuildAndPush
  jobs:
  - job: UpdateAppMigration
    displayName: Update migration app
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: AzureCLI@2
      displayName: Update Container App Migration
      inputs:
        azureSubscription: 'Quadraat.Global'
        scriptType: bash
        addSpnToEnvironment: true
        scriptLocation: inlineScript
        visibleAzLogin: false
        inlineScript: |
          az containerapp update \
            --name '$(containerAppMigration)' \
            --resource-group '$(resourceGroup)' \
            --image '$(acrLoginServer)/$(appName):$(tag)' \
            --container-name '$(containerAppMigration)' \
            --args '$(startupCommandMigration)' ## Set the correct startup command and add in to the deploy step
  
  - job: UpdateAppRun
    displayName: Update running App
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: AzureCLI@2
      displayName: Update Container App run
      inputs:
        azureSubscription: 'Quadraat.Global'
        scriptType: bash
        addSpnToEnvironment: true
        scriptLocation: inlineScript
        visibleAzLogin: false
        inlineScript: |
          az containerapp update \
            --name '$(containerAppRun)' \
            --resource-group '$(resourceGroup)' \
            --image '$(acrLoginServer)/$(appName):$(tag)' \
            --container-name '$(containerAppRun)' \
            --args '$(startupCommandRun)' ## Set the correct startup command and add in to the deploy step
