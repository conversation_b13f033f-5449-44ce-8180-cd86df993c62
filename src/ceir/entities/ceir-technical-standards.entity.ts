import {
  <PERSON><PERSON>ty,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  Before<PERSON><PERSON>rt,
  OneToMany,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsUUID, IsString, IsOptional, IsBoolean, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';
import { CeirEquipmentTypeCategories } from './ceir-equipment-type-categories.entity';

// Standard type constants for validation
export const STANDARD_TYPES = [
  'technical',
  'safety',
  'emc',
  'rf_performance',
  'sar',
  'environmental',
  'security',
  'interoperability',
] as const;

// Standard status constants for validation
export const STANDARD_STATUSES = [
  'active',
  'deprecated',
  'draft',
  'withdrawn',
] as const;

// Issuing organization constants for validation
export const ISSUING_ORGANIZATIONS = [
  'etsi',
  'itu',
  '3gpp',
  'ieee',
  'fcc',
  'gsma',
  'ceir',
  'iec',
  'iso',
  'macra',
] as const;

@Entity('ceir_technical_standards')
export class CeirTechnicalStandards {
  @ApiProperty({
    description: 'Unique identifier for the technical standard',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID()
  standard_id: string;

  @ApiProperty({
    description: 'Standard reference number',
    example: '3GPP TS 51.010-1'
  })
  @Column({ type: 'varchar', length: 100, unique: true })
  @IsString()
  standard_reference: string;

  @ApiProperty({
    description: 'Standard title',
    example: 'Mobile Station (MS) conformance specification'
  })
  @Column({ type: 'varchar', length: 255 })
  @IsString()
  standard_title: string;

  @ApiProperty({
    description: 'Detailed description of the standard',
    example: 'Conformance specification for mobile stations operating in GSM networks'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Type of standard',
    example: 'technical'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  standard_type: string;

  @ApiProperty({
    description: 'Organization that issued the standard',
    example: '3gpp'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  issuing_organization: string;

  @ApiProperty({
    description: 'Version of the standard',
    example: 'V16.0.0'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  version: string;

  @ApiProperty({
    description: 'Date when the standard was published',
    example: '2020-07-01'
  })
  @Column({ type: 'date' })
  @IsDateString()
  publication_date: Date;

  @ApiProperty({
    description: 'Date when the standard becomes effective',
    example: '2021-01-01'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  effective_date?: Date;

  @ApiProperty({
    description: 'Date when the standard expires or is superseded',
    example: '2025-12-31'
  })
  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString()
  expiry_date?: Date;

  @ApiProperty({
    description: 'Current status of the standard',
    example: 'active'
  })
  @Column({ type: 'varchar', length: 50, default: 'active' })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Applicable frequency bands',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100']
  })
  @Column({ type: 'simple-array', nullable: true })
  applicable_frequency_bands?: string[];

  @ApiProperty({
    description: 'Equipment categories this standard applies to',
    example: ['mobile_phone', 'smartphone', 'modem']
  })
  @Column({ type: 'simple-array', nullable: true })
  applicable_equipment_categories?: string[];

  @ApiProperty({
    description: 'Test methods defined in this standard',
    example: ['Conducted spurious emissions', 'Radiated spurious emissions']
  })
  @Column({ type: 'simple-array', nullable: true })
  test_methods?: string[];

  @ApiProperty({
    description: 'Compliance requirements',
    example: ['Maximum power spectral density', 'Frequency stability']
  })
  @Column({ type: 'simple-array', nullable: true })
  compliance_requirements?: string[];

  @ApiProperty({
    description: 'Reference to superseding standard',
    example: '3GPP TS 51.010-2'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  superseded_by?: string;

  @ApiProperty({
    description: 'Reference to superseded standard',
    example: '3GPP TS 51.010-0'
  })
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  supersedes?: string;

  @ApiProperty({
    description: 'URL to the standard document',
    example: 'https://www.3gpp.org/ftp/Specs/archive/51_series/51.010-1/'
  })
  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @IsString()
  document_url?: string;

  @ApiProperty({
    description: 'Whether this standard is mandatory for type approval',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_mandatory: boolean;

  @ApiProperty({
    description: 'Whether this standard is currently active',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.standard_id) {
      this.standard_id = uuidv4();
    }
  }
}
