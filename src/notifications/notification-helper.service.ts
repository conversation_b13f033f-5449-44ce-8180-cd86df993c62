import { Injectable } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { EmailTemplateService } from './email-template.service';
import { NotificationType, RecipientType, NotificationStatus } from '../entities/notifications.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { assetsDir } from '../app.module';
import { formatAmount } from 'src/common/utils/formatters';

@Injectable()
export class NotificationHelperService {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly emailTemplateService: EmailTemplateService,
    private readonly mailerService: MailerService,
  ) {}

  /**
   * Send application status notification to customer
   * Uses the generic sendEmailNotification method
   */
  async notifyApplicationStatus(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicationNumber: string,
    status: string,
    createdBy: string,
    applicantName?: string,
    licenseType?: string,
    oldStatus?: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing application status notification for ${applicationNumber} - ${status}`);

    // Determine which template to use based on status
    let emailTemplate: { subject: string; html: string };

    if (status === 'submitted') {
      emailTemplate = this.emailTemplateService.generateApplicationSubmittedTemplate({
        applicantName: applicantName || 'Valued Customer',
        applicationNumber,
        licenseType: licenseType || 'License',
        submissionDate: new Date().toLocaleDateString(),
        portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
      });
    } else if (status === 'invoice_generated') {
      throw new Error('Invoice generation should use notifyInvoiceGenerated method');
    } else {
      emailTemplate = this.emailTemplateService.generateApplicationStatusChangeTemplate({
        applicantName: applicantName || 'Valued Customer',
        applicationNumber,
        licenseType: licenseType || 'License',
        oldStatus: oldStatus || 'previous',
        newStatus: status,
        changeDate: new Date().toLocaleDateString(),
        portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
      });
    }

    const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: applicantId,
        recipientEmail: applicantEmail,
        recipientName: applicantName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'application',
        entityId: applicationId,
        actionUrl,
        recipientType: RecipientType.CUSTOMER,
        createdBy,
        sendEmail: !!applicantEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: Application status notifications completed for ${applicationNumber}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send application status notifications:`, error);
      throw error;
    }
  }

  /**
   * Send invoice generation notification to customer
   * Uses the generic sendEmailNotification method
   */
  async notifyInvoiceGenerated(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicationNumber: string,
    invoiceNumber: string,
    amount: number,
    dueDate: string,
    description: string,
    createdBy: string,
    applicantName?: string,
    licenseType?: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing invoice generation notification for ${invoiceNumber}`);

    const emailTemplate = this.emailTemplateService.generateInvoiceGeneratedTemplate({
      applicantName: applicantName || 'Valued Customer',
      applicationNumber,
      licenseType: licenseType || 'License',
      invoiceNumber,
      amount,
      dueDate,
      description,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
    });

    const message = `Invoice ${invoiceNumber} has been generated for your application ${applicationNumber}. Amount: ${formatAmount(amount)}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: applicantId,
        recipientEmail: applicantEmail,
        recipientName: applicantName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'invoice',
        entityId: invoiceNumber,
        actionUrl,
        recipientType: RecipientType.CUSTOMER,
        createdBy,
        sendEmail: !!applicantEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: Invoice notifications completed for ${invoiceNumber}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send invoice notifications:`, error);
      throw error;
    }
  }

  /**
   * Send payment confirmation notification to client
   * Uses the generic sendEmailNotification method
   */
  async notifyPaymentConfirmation(
    invoiceId: string,
    clientId: string,
    clientEmail: string,
    clientName: string,
    invoiceNumber: string,
    invoiceAmount: number,
    paidAmount: number,
    paymentDate: string,
    processedBy: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing payment confirmation notification for ${invoiceNumber}`);

    const emailTemplate = this.emailTemplateService.generatePaymentConfirmationTemplate({
      clientName: clientName || 'Valued Customer',
      invoiceNumber,
      invoiceAmount,
      paidAmount,
      paymentDate,
      processedBy,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/invoices?invoice_id=${invoiceId}`,
    });

    const message = `Payment confirmed for invoice ${invoiceNumber}. Amount paid:  ${formatAmount(paidAmount)}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/invoices?invoice_id=${invoiceId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: clientId,
        recipientEmail: clientEmail,
        recipientName: clientName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'invoice',
        entityId: invoiceId,
        actionUrl,
        recipientType: RecipientType.CUSTOMER,
        createdBy: processedBy,
        sendEmail: !!clientEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: Payment confirmation notifications completed for ${invoiceNumber}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send payment confirmation notifications:`, error);
      throw error;
    }
  }

  /**
   * Send activity note notification to applicant or additional recipients
   * Uses the generic sendEmailNotification method to both send email and save to database
   */
  async notifyActivityNote(
    applicationId: string,
    applicantId: string | null,
    applicantEmail: string,
    applicantName: string,
    applicationNumber: string,
    licenseType: string,
    noteContent: string,
    noteType: string,
    category: string,
    step: string | undefined,
    createdBy: string,
    createdDate: string,
    userId: string,
    isAdditionalRecipient: boolean = false
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing activity note notification for application ${applicationNumber}`);

    // Generate email template
    const emailTemplate = this.emailTemplateService.generateActivityNoteTemplate({
      applicantName: applicantName || 'Valued Customer',
      applicationNumber,
      licenseType: licenseType || 'License',
      noteContent,
      noteType,
      category,
      step,
      createdBy,
      createdDate,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
    });

    const message = `New update on your application ${applicationNumber}: ${noteContent}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: applicantId || undefined, // Convert null to undefined for optional field
        recipientEmail: applicantEmail,
        recipientName: applicantName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'application',
        entityId: applicationId,
        actionUrl,
        recipientType: isAdditionalRecipient ? RecipientType.STAFF : RecipientType.CUSTOMER,
        createdBy: userId,
        sendEmail: !!applicantEmail, // Only send email if email address is provided
        createInApp: !isAdditionalRecipient && !!applicantId, // Only create in-app notification for main applicant
      });

      if (isAdditionalRecipient) {
        console.log(`✅ Additional email notification sent to: ${applicantEmail}`);
      } else {
        console.log(`✅ Primary applicant notification sent to: ${applicantEmail}`);
      }
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send activity note notifications:`, error);
      throw error;
    }
  }

  /**
   * Send task assignment notification to staff
   * Uses the generic sendEmailNotification method
   */
  async notifyTaskAssignment(
    taskId: string,
    assigneeId: string,
    assigneeEmail: string,
    taskTitle: string,
    taskDescription: string,
    createdBy: string,
    assigneeName?: string,
    applicationNumber?: string,
    applicantName?: string,
    priority?: string,
    dueDate?: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing task assignment notification for ${taskTitle}`);

    const emailTemplate = this.emailTemplateService.generateTaskAssignedTemplate({
      assigneeName: assigneeName || 'Team Member',
      taskTitle,
      taskDescription,
      applicationNumber: applicationNumber || 'N/A',
      applicantName: applicantName || 'N/A',
      priority: priority || 'medium',
      dueDate,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
    });

    const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: assigneeId,
        recipientEmail: assigneeEmail,
        recipientName: assigneeName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'task',
        entityId: taskId,
        actionUrl,
        recipientType: RecipientType.STAFF,
        createdBy,
        sendEmail: !!assigneeEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: Task assignment notifications completed for ${taskTitle}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send task assignment notifications:`, error);
      throw error;
    }
  }

  /**
   * Send license expiry notification to customer
   * Uses the generic sendEmailNotification method
   */
  async notifyLicenseExpiry(
    licenseId: string,
    customerId: string,
    customerEmail: string,
    licenseNumber: string,
    expiryDate: Date,
    daysUntilExpiry: number,
    createdBy: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing license expiry notification for ${licenseNumber}`);

    const subject = `License ${licenseNumber} Expiry Notice`;
    const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #e02b20;">License Expiry Notice</h2>
        <p>Dear Valued Customer,</p>
        <p>Your license <strong>${licenseNumber}</strong> will expire in <strong>${daysUntilExpiry} days</strong> on <strong>${expiryDate.toDateString()}</strong>.</p>
        <p>Please renew your license to avoid service interruption.</p>
        <p>Best regards,<br>MACRA Team</p>
      </div>
    `;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_id=${licenseId}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: customerId,
        recipientEmail: customerEmail,
        subject,
        message,
        htmlContent,
        entityType: 'license',
        entityId: licenseId,
        actionUrl,
        recipientType: RecipientType.CUSTOMER,
        createdBy,
        sendEmail: !!customerEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: License expiry notifications completed for ${licenseNumber}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send license expiry notifications:`, error);
      throw error;
    }
  }

  /**
   * Send task completion notification to applicant and assignee
   * Uses the generic sendEmailNotification method
   */
  async notifyTaskCompletion(
    taskId: string,
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    assigneeId: string,
    assigneeEmail: string,
    taskTitle: string,
    applicationNumber: string,
    outcome: string,
    createdBy: string,
    applicantName?: string,
    licenseType?: string,
    comments?: string,
    nextSteps?: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing task completion notification for ${taskTitle}`);

    const emailTemplate = this.emailTemplateService.generateTaskCompletedTemplate({
      applicantName: applicantName || 'Valued Customer',
      taskTitle,
      applicationNumber,
      licenseType: licenseType || 'License',
      completionDate: new Date().toLocaleDateString(),
      outcome,
      comments,
      nextSteps,
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
    });

    const message = `Task "${taskTitle}" for application ${applicationNumber} has been completed with outcome: ${outcome}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;

    // Notify applicant using generic method
    if (applicantEmail) {
      try {
        await this.sendEmailNotification({
          recipientId: applicantId,
          recipientEmail: applicantEmail,
          recipientName: applicantName,
          subject: emailTemplate.subject,
          message,
          htmlContent: emailTemplate.html,
          entityType: 'application',
          entityId: applicationId,
          actionUrl,
          recipientType: RecipientType.CUSTOMER,
          createdBy,
          sendEmail: true,
          createInApp: true,
        });
      } catch (error) {
        console.error(`❌ Failed to notify applicant for task completion:`, error);
      }
    }

    // Notify assignee (confirmation) - in-app only
    if (assigneeEmail && assigneeId !== createdBy) {
      const assigneeMessage = `You have successfully completed task "${taskTitle}" for application ${applicationNumber}`;

      try {
        await this.sendEmailNotification({
          recipientId: assigneeId,
          recipientEmail: assigneeEmail,
          subject: `Task Completed: ${taskTitle}`,
          message: assigneeMessage,
          htmlContent: `<p>${assigneeMessage}</p>`,
          entityType: 'task',
          entityId: taskId,
          actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
          recipientType: RecipientType.STAFF,
          createdBy,
          sendEmail: false, // Only in-app notification for assignee
          createInApp: true,
        });
      } catch (error) {
        console.error(`❌ Failed to notify assignee for task completion:`, error);
      }
    }

    console.log(`✅ NotificationHelper: Task completion notifications completed for ${taskTitle}`);
  }

  /**
   * Send license approval notification to customer
   * Uses the generic sendEmailNotification method
   */
  async notifyLicenseApproval(
    applicationId: string,
    applicantId: string,
    applicantEmail: string,
    applicationNumber: string,
    licenseNumber: string,
    licenseType: string,
    createdBy: string,
    applicantName?: string,
    expiryDate?: string
  ): Promise<void> {
    console.log(`📧 NotificationHelper: Processing license approval notification for ${licenseNumber}`);

    const emailTemplate = this.emailTemplateService.generateLicenseApprovedTemplate({
      applicantName: applicantName || 'Valued Customer',
      applicationNumber,
      licenseType,
      licenseNumber,
      approvalDate: new Date().toLocaleDateString(),
      expiryDate: expiryDate || 'TBD',
      portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`,
    });

    const message = `Congratulations! Your license application ${applicationNumber} has been approved. License Number: ${licenseNumber}`;
    const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`;

    // Use the generic sendEmailNotification method
    try {
      await this.sendEmailNotification({
        recipientId: applicantId,
        recipientEmail: applicantEmail,
        recipientName: applicantName,
        subject: emailTemplate.subject,
        message,
        htmlContent: emailTemplate.html,
        entityType: 'application',
        entityId: applicationId,
        actionUrl,
        recipientType: RecipientType.CUSTOMER,
        createdBy,
        sendEmail: !!applicantEmail,
        createInApp: true,
      });

      console.log(`✅ NotificationHelper: License approval notifications completed for ${licenseNumber}`);
    } catch (error) {
      console.error(`❌ NotificationHelper: Failed to send license approval notifications:`, error);
      throw error;
    }
  }

  /**
   * Generic method to send email notification and save to database
   * This method both sends the email and creates notification records
   */
  async sendEmailNotification(params: {
    recipientId?: string;
    recipientEmail: string;
    recipientName?: string;
    subject: string;
    message: string;
    htmlContent: string;
    entityType?: string;
    entityId?: string;
    actionUrl?: string;
    recipientType?: RecipientType;
    createdBy: string;
    sendEmail?: boolean; // Whether to actually send email (default: true)
    createInApp?: boolean; // Whether to create in-app notification (default: true)
  }): Promise<{ emailNotification?: any; inAppNotification?: any }> {
    const {
      recipientId,
      recipientEmail,
      subject,
      message,
      htmlContent,
      entityType,
      entityId,
      actionUrl,
      recipientType = RecipientType.CUSTOMER,
      createdBy,
      sendEmail = true,
      createInApp = true
    } = params;

    const results: { emailNotification?: any; inAppNotification?: any } = {};

    try {
      // Send email if requested
      if (sendEmail && recipientEmail) {
        console.log(`📧 NotificationHelper: Sending email to ${recipientEmail}`);
        console.log(`📧 Email subject: ${subject}`);

        // Send email immediately
        await this.mailerService.sendMail({
          to: recipientEmail,
          subject: subject,
          html: htmlContent,
          attachments: [
            {
              filename: 'macra-logo.png',
              path: join(assetsDir, 'macra-logo.png'),
              cid: 'logo@macra',
            },
          ],
        } as any);

        console.log(`✅ Email sent successfully to ${recipientEmail}`);

        // Create email notification record
        results.emailNotification = await this.notificationsService.create({
          type: NotificationType.EMAIL,
          recipient_type: recipientType,
          recipient_id: recipientId,
          recipient_email: recipientEmail,
          subject,
          message,
          html_content: htmlContent,
          entity_type: entityType,
          entity_id: entityId,
          status: NotificationStatus.SENT,
        }, createdBy);

        // Mark as sent with timestamp
        await this.notificationsService.markAsSent(results.emailNotification.notification_id);

        console.log(`✅ Email notification record created with ID: ${results.emailNotification.notification_id}`);
      }

      // // Create in-app notification if requested
      // if (createInApp) {
      //   results.inAppNotification = await this.notificationsService.create({
      //     type: NotificationType.IN_APP,
      //     recipient_type: recipientType,
      //     recipient_id: recipientId,
      //     recipient_email: recipientEmail, // Include email for user linking
      //     subject,
      //     message,
      //     entity_type: entityType,
      //     entity_id: entityId,
      //     action_url: actionUrl,
      //   }, createdBy);

      //   console.log(`✅ In-app notification record created with ID: ${results.inAppNotification.notification_id}`);
      // }

      return results;
    } catch (error) {
      console.error('❌ Failed to send email notification:', error);

      // If email sending failed, create a failed notification record
      if (sendEmail && recipientEmail) {
        try {
          results.emailNotification = await this.notificationsService.create({
            type: NotificationType.EMAIL,
            recipient_type: recipientType,
            recipient_id: recipientId,
            recipient_email: recipientEmail,
            subject,
            message,
            html_content: htmlContent,
            entity_type: entityType,
            entity_id: entityId,
            status: NotificationStatus.FAILED,
          }, createdBy);

          // Mark as failed with error message
          await this.notificationsService.markAsFailed(
            results.emailNotification.notification_id,
            error.message || 'Failed to send email'
          );
        } catch (dbError) {
          console.error('❌ Failed to create failed notification record:', dbError);
        }
      }

      throw error;
    }
  }
}
