import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  PrimaryColumn,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';
import { ProofOfPayment } from './proof-of-payment.entity';

// Payment status constants
export const PaymentStatus = {
  PENDING: 'pending',
  APPROVED: 'approved',
  OVERDUE: 'overdue',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
} as const;

// Payment type constants
export const PaymentType = {
  LICENSE_FEE: 'License Fee',
  PROCUREMENT_FEE: 'Procurement Fee',
  APPLICATION_FEE: 'Application Fee',
  RENEWAL_FEE: 'Renewal Fee',
  PENALTY_FEE: 'Penalty Fee',
  INSPECTION_FEE: 'Inspection Fee',
} as const;

// Currency constants
export const Currency = {
  MWK: 'MWK',
  USD: 'USD',
  EUR: 'EUR',
} as const;

@Entity('payments')
export class Payment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  payment_id: string;

  @Column({ unique: true, nullable: false })
  invoice_number: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column({
    type: 'varchar',
    default: Currency.USD,
  })
  currency: string;

  @Column({
    type: 'varchar',
    default: PaymentStatus.PENDING,
  })
  status: string;

  @Column({
    type: 'varchar',
  })
  payment_type: string;

  @Column('text')
  description: string;

  @Column({ type: 'date' })
  issue_date: Date;

  @Column({ type: 'date', nullable: true })
  due_date?: Date;

  @Column({ type: 'date', nullable: true })
  paid_date?: Date;

  @Column({ nullable: true })
  payment_method?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ nullable: true })
  transaction_reference?: string;

  // Proof of payment fields
  @Column({ type: 'text', nullable: true })
  proof_of_payment_url?: string;

  @Column({ type: 'text', nullable: true })
  proof_of_payment_notes?: string;

  @Column({ type: 'timestamp', nullable: true })
  proof_of_payment_uploaded_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  invoice_id?: string;

  // Polymorphic relationship fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  entity_type?: string;

  @Column({ type: 'uuid', nullable: true })
  entity_id?: string;

  // Relations
  @Column()
  user_id: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'uuid' })
  created_by: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => Applications, { nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;


  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relation to proof of payments - TEMPORARILY COMMENTED OUT
  // @OneToMany(() => ProofOfPayment, (proofOfPayment) => proofOfPayment.payment)
  // proof_of_payments: ProofOfPayment[];

  @BeforeInsert()
  generateId() {
    if (!this.payment_id) {
      this.payment_id = uuidv4();
    }

    // Ensure invoice_number is not empty
    if (!this.invoice_number || this.invoice_number.trim() === '') {
      throw new Error('Invoice number cannot be empty');
    }
  }
}
