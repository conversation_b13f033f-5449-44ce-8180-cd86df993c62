import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON>DPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiConsumes
} from '@nestjs/swagger';
// File uploads now handled by documents controller
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { PaymentsService, PaymentFilters } from './payments.service';
import { CreatePaymentDto } from '../dto/payments/create-payment.dto';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { UpdatePaymentDto } from '../dto/payments/update-payment.dto';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiResponse({ status: 201, description: 'Payment created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Invoice number already exists' })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Created new payment',
  })
  async create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentsService.createPayment(createPaymentDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all payments with optional filters' })
  @ApiResponse({ status: 200, description: 'Payments retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'paymentType', required: false, type: String })
  @ApiQuery({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Query('status') status?: string,
    @Query('paymentType') paymentType?: string,
    @Query('dateRange') dateRange?: 'last-30' | 'last-90' | 'last-year',
    @Query('search') search?: string,
    @Request() req?: any,
  ): Promise<PaginatedResult<any>> {

    // Check user roles to determine filtering behavior
    const userRoles = req.user?.roles || [];
    // Roles from JWT are strings, not objects
    const isCustomer = userRoles.includes('customer');
    const filters: PaymentFilters = {
      status,
      paymentType,
      dateRange,
      search,
      // Only filter by userId for customers, admin/staff see all payments
      ...(isCustomer && { userId: req.user?.user_id })
    };


    const result = await this.paymentsService.getPayments(filters, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStatistics(@Request() req: any) {
    // Check user roles to determine filtering behavior
    const userRoles = req.user?.roles || [];
    // Roles from JWT are strings, not objects
    const isCustomer = userRoles.includes('customer');
    const isAdminOrStaff = userRoles.some((role: string) =>
      ['administrator', 'admin', 'staff', 'finance', 'officer'].includes(role)
    );

    // Only filter by userId for customers, admin/staff see all statistics
    const userId = (isCustomer && !isAdminOrStaff) ? req.user?.user_id : undefined;
    console.log('📊 PaymentsController.getStatistics called with userId:', userId, 'isCustomer:', isCustomer, 'isAdminOrStaff:', isAdminOrStaff);
    return this.paymentsService.getPaymentStatistics(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get payment by ID' })
  @ApiResponse({ status: 200, description: 'Payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Viewed payment details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.paymentsService.getPaymentById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update payment' })
  @ApiResponse({ status: 200, description: 'Payment updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid payment data provided' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiResponse({ status: 409, description: 'Payment update conflict (e.g., duplicate invoice number)' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('administrator', 'finance')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Updated payment',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePaymentDto: UpdatePaymentDto,
    @Request() req: any,
  ) {
    try {
      // Handle case where authentication is disabled (req.user is undefined)
      const userId = req.user?.userId || null;
      const result = await this.paymentsService.updatePayment(id, updatePaymentDto, userId);
      return {
        success: true,
        message: 'Payment updated successfully',
        data: result
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException({
          success: false,
          message: 'Payment not found',
          error: 'PAYMENT_NOT_FOUND'
        });
      }

      if (error instanceof ConflictException) {
        throw new ConflictException({
          success: false,
          message: error.message || 'Payment update conflict',
          error: 'PAYMENT_UPDATE_CONFLICT'
        });
      }

      if (error instanceof BadRequestException) {
        throw new BadRequestException({
          success: false,
          message: error.message || 'Invalid payment data provided',
          error: 'INVALID_PAYMENT_DATA'
        });
      }

      throw new InternalServerErrorException({
        success: false,
        message: 'Failed to update payment. Please try again later.',
        error: 'PAYMENT_UPDATE_FAILED'
      });
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete payment' })
  @ApiResponse({ status: 200, description: 'Payment deleted successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Deleted payment',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.paymentsService.deletePayment(id);
    return { message: 'Payment deleted successfully' };
  }

  @Post('mark-overdue')
  @ApiOperation({ summary: 'Mark overdue payments (admin only)' })
  @ApiResponse({ status: 200, description: 'Overdue payments marked successfully' })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Marked overdue payments',
  })
  async markOverduePayments() {
    return this.paymentsService.markOverduePayments();
  }

  @Get('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get payments for a specific entity' })
  @ApiResponse({ status: 200, description: 'Entity payments retrieved successfully' })
  @ApiParam({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String })
  @ApiParam({ name: 'entityId', description: 'Entity ID', type: String })
  async getPaymentsByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Paginate() query: PaginateQuery,
  ): Promise<PaginatedResult<any>> {
    const result = await this.paymentsService.getPaymentsByEntity(entityType, entityId, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Post('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Create payment for a specific entity' })
  @ApiResponse({ status: 201, description: 'Payment created successfully for entity' })
  @ApiParam({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String })
  @ApiParam({ name: 'entityId', description: 'Entity ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Created payment for entity',
  })
  async createPaymentForEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Body() createPaymentDto: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>,
  ) {
    return this.paymentsService.createPaymentForEntity(entityType, entityId, createPaymentDto);
  }

  // Customer-specific endpoints
  @Get('customer/my-payments')
  @ApiOperation({ summary: 'Get customer payments (filtered by user applications)' })
  @ApiResponse({ status: 200, description: 'Customer payments retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by payment status' })
  @ApiQuery({ name: 'paymentType', required: false, description: 'Filter by payment type' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Viewed customer payments',
  })
  async getCustomerPayments(
    @Query('status') status?: string,
    @Query('paymentType') paymentType?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('dateRange') dateRange?: 'last-30' | 'last-90' | 'last-year',
    @Request() req?: any,
  ) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.paymentsService.getPaymentsByCustomer(userId, {
      status,
      paymentType,
      page: page || 1,
      limit: limit || 10,
      search,
      dateRange,
    });
  }

  @Get('customer/statistics')
  @ApiOperation({ summary: 'Get customer payment statistics' })
  @ApiResponse({ status: 200, description: 'Customer payment statistics retrieved successfully' })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Viewed customer payment statistics',
  })
  async getCustomerPaymentStatistics(@Request() req: any) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.paymentsService.getPaymentStatisticsByCustomer(userId);
  }

  @Get('customer/:id')
  @ApiOperation({ summary: 'Get specific customer payment' })
  @ApiResponse({ status: 200, description: 'Customer payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found or not accessible' })
  @ApiParam({ name: 'id', description: 'Payment ID' })
  @UseGuards(RolesGuard)
  // @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Viewed specific customer payment',
  })
  async getCustomerPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.paymentsService.getPaymentByCustomer(id, userId);
  }

  @Get('customer/application/:applicationId')
  @ApiOperation({ summary: 'Get payments for specific customer application' })
  @ApiResponse({ status: 200, description: 'Application payments retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Application not found or not accessible' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Viewed customer application payments',
  })
  async getCustomerApplicationPayments(
    @Param('applicationId', ParseUUIDPipe) applicationId: string,
    @Request() req: any,
  ) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.paymentsService.getApplicationPaymentsByCustomer(applicationId, userId);
  }


  @Get('customer/invoice/:invoiceId')
  @ApiOperation({ summary: 'Get payments for specific customer invoice' })
  @ApiResponse({ status: 200, description: 'Invoice payments retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found or not accessible' })
  @ApiParam({ name: 'invoiceId', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  @Roles('customer', 'finance', 'administrator')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Viewed customer invoice payments',
  })
  async getCustomerInvoicePayments(
    @Param('invoiceId', ParseUUIDPipe) invoiceId: string,
    @Request() req: any,
  ) {
    // Handle case where authentication is disabled (req.user is undefined)
    const userId = req.user?.user_id || null;
    return this.paymentsService.getInvoicePaymentsByCustomer(invoiceId);
  }

  @Post('customer/invoice/:invoiceId/proof-of-payment')
  @ApiOperation({
    summary: 'Link proof of payment document to customer invoice',
    description: 'Links an already uploaded document (via documents controller) to a payment for the specified invoice'
  })
  @ApiResponse({ status: 200, description: 'Proof of payment linked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid document ID or missing required data' })
  @ApiResponse({ status: 404, description: 'Invoice or document not found' })
  @ApiResponse({ status: 409, description: 'Payment already exists for this invoice' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @ApiParam({ name: 'invoiceId', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerPayment',
    description: 'Linked proof of payment document to invoice',
  })
  async linkProofOfPayment(
    @Param('invoiceId', ParseUUIDPipe) invoiceId: string,
    @Body() proofData: { documentId: string; amount?: number; paymentMethod?: string; transactionReference?: string; notes?: string; payment_id?: string },
    @Request() req: any,
  ) {
    try {
      // Handle case where authentication is disabled (req.user is undefined)
      const userId = req.user?.user_id || null;

      // Validate document ID
      if (!proofData.documentId) {
        throw new BadRequestException({
          success: false,
          message: 'Document ID is required. Please upload the document first via /documents endpoint.',
          error: 'NO_DOCUMENT_ID'
        });
      }
      console.log(`📤 Processing proof of payment link for invoice ${invoiceId} by user ${userId}`);

      const result = await this.paymentsService.uploadProofOfPaymentByCustomer(invoiceId, userId, proofData, proofData.documentId);

      return {
        success: true,
        message: 'Proof of payment linked successfully. Your payment is now under review.',
        data: result
      };

    } catch (error) {
      console.error(`❌ Error uploading proof of payment for invoice ${invoiceId}:`, error);

      if (error instanceof NotFoundException) {
        throw new NotFoundException({
          success: false,
          message: 'Invoice not found or you do not have access to it.',
          error: 'INVOICE_NOT_FOUND'
        });
      }

      if (error instanceof ConflictException) {
        throw new ConflictException({
          success: false,
          message: error.message || 'A payment already exists for this invoice.',
          error: 'PAYMENT_ALREADY_EXISTS'
        });
      }

      if (error instanceof BadRequestException) {
        throw error; // Re-throw validation errors as-is
      }

      // Handle multer file upload errors
      if (error.message?.includes('File too large')) {
        throw new BadRequestException({
          success: false,
          message: 'File size too large. Maximum allowed size is 5MB.',
          error: 'FILE_TOO_LARGE'
        });
      }

      if (error.message?.includes('Invalid file type')) {
        throw new BadRequestException({
          success: false,
          message: 'Invalid file type. Only JPEG, PNG, and PDF files are allowed.',
          error: 'INVALID_FILE_TYPE'
        });
      }

      throw new InternalServerErrorException({
        success: false,
        message: 'Failed to upload proof of payment. Please try again later.',
        error: 'UPLOAD_FAILED'
      });
    }
  }
}