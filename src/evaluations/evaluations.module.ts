import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EvaluationsService } from './evaluations.service';
import { EvaluationsController } from './evaluations.controller';
import { Evaluations } from '../entities/evaluations.entity';
import { EvaluationCriteria } from '../entities/evaluation-criteria.entity';
import { Applications } from '../entities/applications.entity';
import { TasksModule } from '../tasks/tasks.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Evaluations,
      EvaluationCriteria,
      Applications,
    ]),
    forwardRef(() => TasksModule),
  ],
  controllers: [EvaluationsController],
  providers: [EvaluationsService],
  exports: [EvaluationsService],
})
export class EvaluationsModule {}
