import { DataSource } from 'typeorm';
import { CeirTechnicalStandards, STANDARD_TYPES, STANDARD_STATUSES, ISSUING_ORGANIZATIONS } from '../../ceir/entities/ceir-technical-standards.entity';
import { User } from '../../entities/user.entity';
import { v4 as uuidv4 } from 'uuid';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class CeirTechnicalStandardsSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    const standardsRepository = dataSource.getRepository(CeirTechnicalStandards);
    const userRepository = dataSource.getRepository(User);

    // Check if standards already exist
    const existingCount = await standardsRepository.count();
    if (existingCount > 0) {
      console.log('CEIR Technical Standards already exist, skipping seeder...');
      return;
    }

    // Try to get a system user for created_by (optional)
    let systemUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    let systemUserId: string | undefined = undefined;

    if (!systemUser) {
      try {
        // Try to create a system user if it doesn't exist
        systemUser = userRepository.create({
          email: '<EMAIL>',
          first_name: 'System',
          last_name: 'Administrator',
          phone: '+265999000000',
          password: 'system123',
          status: 'active' as any,
        });
        systemUser = await userRepository.save(systemUser);
        systemUserId = systemUser.user_id;
        console.log('✅ Created system user for seeding');
      } catch (error) {
        console.log('⚠️ Could not create system user, proceeding with null created_by');
        systemUserId = undefined;
      }
    } else {
      systemUserId = systemUser.user_id;
      console.log('✅ Found existing system user for seeding');
    }

    const standards = [
      {
        standard_id: uuidv4(),
        standard_reference: '3GPP TS 51.010-1',
        standard_title: 'Mobile Station (MS) conformance specification; Part 1: Conformance specification',
        description: 'Conformance specification for mobile stations operating in GSM networks',
        standard_type: 'technical',
        issuing_organization: '3gpp',
        version: 'V16.0.0',
        publication_date: new Date('2020-07-01'),
        effective_date: new Date('2021-01-01'),
        status: 'active',
        applicable_frequency_bands: ['GSM 900', 'GSM 1800'],
        applicable_equipment_categories: ['mobile_phone', 'smartphone'],
        test_methods: ['Conducted spurious emissions', 'Radiated spurious emissions', 'Receiver blocking'],
        compliance_requirements: ['Maximum power spectral density', 'Frequency stability', 'Modulation accuracy'],
        document_url: 'https://www.3gpp.org/ftp/Specs/archive/51_series/51.010-1/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: '3GPP TS 36.521-1',
        standard_title: 'User Equipment (UE) conformance specification; Radio transmission and reception; Part 1: Conformance testing',
        description: 'Conformance specification for LTE user equipment radio transmission and reception',
        standard_type: 'technical',
        issuing_organization: '3gpp',
        version: 'V16.5.0',
        publication_date: new Date('2021-03-01'),
        effective_date: new Date('2021-06-01'),
        status: 'active',
        applicable_frequency_bands: ['LTE 800', 'LTE 1800', 'LTE 2600'],
        applicable_equipment_categories: ['smartphone', 'tablet', 'modem', 'router'],
        test_methods: ['Maximum output power', 'Output power dynamics', 'Transmit signal quality'],
        compliance_requirements: ['Power class requirements', 'ACLR requirements', 'EVM requirements'],
        document_url: 'https://www.3gpp.org/ftp/Specs/archive/36_series/36.521-1/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'ETSI EN 301 511',
        standard_title: 'Global System for Mobile communications (GSM); Harmonised Standard for mobile stations',
        description: 'Harmonised standard covering essential requirements for GSM mobile stations',
        standard_type: 'technical',
        issuing_organization: 'etsi',
        version: 'V12.5.1',
        publication_date: new Date('2017-01-01'),
        effective_date: new Date('2017-04-01'),
        status: 'active',
        applicable_frequency_bands: ['GSM 900', 'GSM 1800'],
        applicable_equipment_categories: ['mobile_phone', 'smartphone'],
        test_methods: ['RF output power', 'Modulation', 'RF spectrum due to modulation'],
        compliance_requirements: ['Power control', 'Frequency error', 'Phase error'],
        document_url: 'https://www.etsi.org/deliver/etsi_en/301500_301599/301511/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'ETSI EN 301 908-1',
        standard_title: 'IMT cellular networks; Harmonised Standard for access to radio spectrum; Part 1: Introduction and common requirements',
        description: 'Harmonised standard for IMT cellular networks covering common requirements',
        standard_type: 'technical',
        issuing_organization: 'etsi',
        version: 'V11.1.1',
        publication_date: new Date('2016-07-01'),
        effective_date: new Date('2016-10-01'),
        status: 'active',
        applicable_frequency_bands: ['UMTS 2100', 'LTE 800', 'LTE 1800', 'LTE 2600'],
        applicable_equipment_categories: ['smartphone', 'tablet', 'modem', 'router'],
        test_methods: ['Transmitter characteristics', 'Receiver characteristics', 'Performance requirements'],
        compliance_requirements: ['Spurious emissions', 'Blocking characteristics', 'Intermodulation characteristics'],
        document_url: 'https://www.etsi.org/deliver/etsi_en/301900_301999/30190801/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'IEC 62209-1',
        standard_title: 'Measurement procedure for the assessment of specific absorption rate of human exposure to radio frequency fields from hand-held and body-mounted wireless communication devices - Part 1: Devices used next to the ear',
        description: 'SAR measurement procedures for devices used next to the ear (head SAR)',
        standard_type: 'sar',
        issuing_organization: 'iec',
        version: 'Ed. 1.1',
        publication_date: new Date('2016-06-01'),
        effective_date: new Date('2016-09-01'),
        status: 'active',
        applicable_frequency_bands: ['GSM 900', 'GSM 1800', 'UMTS 2100', 'LTE 800', 'LTE 1800', 'LTE 2600'],
        applicable_equipment_categories: ['mobile_phone', 'smartphone'],
        test_methods: ['Head SAR measurement', 'Phantom preparation', 'Probe calibration'],
        compliance_requirements: ['SAR limit 2.0 W/kg', 'Measurement uncertainty', 'Test report requirements'],
        document_url: 'https://webstore.iec.ch/publication/25045',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'IEC 62209-2',
        standard_title: 'Measurement procedure for the assessment of specific absorption rate of human exposure to radio frequency fields from hand-held and body-mounted wireless communication devices - Part 2: Devices used in close proximity to the human body',
        description: 'SAR measurement procedures for devices used close to the human body (body SAR)',
        standard_type: 'sar',
        issuing_organization: 'iec',
        version: 'Ed. 1.0',
        publication_date: new Date('2010-03-01'),
        effective_date: new Date('2010-06-01'),
        status: 'active',
        applicable_frequency_bands: ['GSM 900', 'GSM 1800', 'UMTS 2100', 'LTE 800', 'LTE 1800', 'LTE 2600'],
        applicable_equipment_categories: ['smartphone', 'tablet', 'wearable'],
        test_methods: ['Body SAR measurement', 'Flat phantom measurement', 'Specific phantom measurement'],
        compliance_requirements: ['SAR limit 2.0 W/kg', 'Measurement distance', 'Averaging mass'],
        document_url: 'https://webstore.iec.ch/publication/6590',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'ETSI EN 300 328',
        standard_title: 'Wideband transmission systems; Data transmission equipment operating in the 2,4 GHz ISM band',
        description: 'Standard for wideband transmission systems operating in 2.4 GHz ISM band',
        standard_type: 'technical',
        issuing_organization: 'etsi',
        version: 'V2.2.2',
        publication_date: new Date('2019-07-01'),
        effective_date: new Date('2019-10-01'),
        status: 'active',
        applicable_frequency_bands: ['WiFi 2.4GHz', 'Bluetooth'],
        applicable_equipment_categories: ['smartphone', 'tablet', 'router', 'iot_device', 'wearable'],
        test_methods: ['Transmitter power', 'Occupied bandwidth', 'Spurious emissions'],
        compliance_requirements: ['EIRP limits', 'Power spectral density', 'Adaptive power control'],
        document_url: 'https://www.etsi.org/deliver/etsi_en/300300_300399/300328/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
      {
        standard_id: uuidv4(),
        standard_reference: 'ETSI EN 301 893',
        standard_title: '5 GHz RLAN; Harmonised Standard for access to radio spectrum',
        description: 'Harmonised standard for 5 GHz Radio Local Area Networks',
        standard_type: 'technical',
        issuing_organization: 'etsi',
        version: 'V2.1.1',
        publication_date: new Date('2017-05-01'),
        effective_date: new Date('2017-08-01'),
        status: 'active',
        applicable_frequency_bands: ['WiFi 5GHz'],
        applicable_equipment_categories: ['smartphone', 'tablet', 'router'],
        test_methods: ['Dynamic Frequency Selection', 'Transmit Power Control', 'Radar detection'],
        compliance_requirements: ['DFS requirements', 'TPC requirements', 'Channel access rules'],
        document_url: 'https://www.etsi.org/deliver/etsi_en/301800_301899/301893/',
        is_mandatory: true,
        is_active: true,
        created_by: systemUserId,
      },
    ];

    console.log('Seeding CEIR Technical Standards...');
    
    for (const standardData of standards) {
      const standard = standardsRepository.create(standardData);
      await standardsRepository.save(standard);
      console.log(`✅ Created technical standard: ${standardData.standard_reference}`);
    }

    console.log('CEIR Technical Standards seeding completed!');
  }
}
