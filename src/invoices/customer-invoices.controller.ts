import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON><PERSON>ipe,
  <PERSON>s,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { InvoicesService } from './invoices.service';
import { InvoicePdfService } from './invoice-pdf.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { Roles } from '../common/decorators/roles.decorator';
import { Permissions } from '../common/decorators/permissions.decorator';
import { RolesGuard } from '../common/guards/roles.guard';
import { Audit } from '../common/interceptors/audit.interceptor';
import { PermissionsGuard } from 'src/common/guards/permissions.guard';

@ApiTags('Customer Invoices')
@Controller('customer-invoices')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class CustomerInvoicesController {
  constructor(
    private readonly invoicesService: InvoicesService,
    private readonly invoicePdfService: InvoicePdfService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get customer invoices (filtered by user applications)' })
  @ApiResponse({ status: 200, description: 'Customer invoices retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by invoice status' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @UseGuards(PermissionsGuard)
  @Permissions('customer')
  @Permissions('view_invoices')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Viewed customer invoices',
  })
  async getCustomerInvoices(
    @Query('status') status?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Request() req?: any,
  ) {
    const userId = req.user.user_id;
    return this.invoicesService.getInvoicesByCustomer(userId, {
      status,
      page: page || 1,
      limit: limit || 10,
      search,
    });
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get customer invoice statistics' })
  @ApiResponse({ status: 200, description: 'Customer invoice statistics retrieved successfully' })
  @UseGuards(RolesGuard)
  // @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Viewed customer invoice statistics',
  })
  async getCustomerInvoiceStatistics(@Request() req: any) {
    const userId = req.user.user_id;
    return this.invoicesService.getInvoiceStatisticsByCustomer(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific customer invoice' })
  @ApiResponse({ status: 200, description: 'Customer invoice retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found or not accessible' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  // @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Viewed specific customer invoice',
  })
  async getCustomerInvoice(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const userId = req.user.user_id;
    console.log(`🔍 Getting invoice ${id} for customer: ${userId}`);
    
    return this.invoicesService.getInvoiceByCustomer(id, userId);
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get invoices for specific customer application' })
  @ApiResponse({ status: 200, description: 'Application invoices retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Application not found or not accessible' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Viewed customer application invoices',
  })
  async getCustomerApplicationInvoices(
    @Param('applicationId', ParseUUIDPipe) applicationId: string,
    @Request() req: any,
  ) {
    const userId = req.user.user_id;
    console.log(`🔍 Getting invoices for application ${applicationId} by customer: ${userId}`);
    
    return this.invoicesService.getApplicationInvoicesByCustomer(applicationId, userId);
  }

  @Get('application/:applicationId/status')
  @ApiOperation({ summary: 'Get invoice status for customer application' })
  @ApiResponse({ status: 200, description: 'Application invoice status retrieved successfully' })
  @ApiParam({ name: 'applicationId', description: 'Application ID' })
  @UseGuards(RolesGuard)
  @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Viewed customer application invoice status',
  })
  async getCustomerApplicationInvoiceStatus(
    @Param('applicationId', ParseUUIDPipe) applicationId: string,
    @Request() req: any,
  ) {
    const userId = req.user.user_id;
    console.log(`🔍 Getting invoice status for application ${applicationId} by customer: ${userId}`);
    
    return this.invoicesService.getApplicationInvoiceStatusByCustomer(applicationId, userId);
  }

  @Get(':id/download')
  @ApiOperation({ summary: 'Download customer invoice PDF' })
  @ApiResponse({ status: 200, description: 'Invoice PDF downloaded successfully' })
  @ApiResponse({ status: 404, description: 'Invoice not found or not accessible' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @UseGuards(RolesGuard)
  // @Roles('customer')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'CustomerInvoice',
    description: 'Downloaded customer invoice PDF',
  })
  async downloadCustomerInvoice(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
    @Res() res: any,
  ) {
    const userId = req.user.user_id;
    console.log(`📄 Downloading invoice PDF ${id} for customer: ${userId}`);

    try {
      // Get the invoice with ownership validation
      const invoice = await this.invoicesService.getInvoiceByCustomer(id, userId);

      // Get related application if available
      let application: any = null;
      if (invoice.entity_type === 'application' && invoice.entity_id) {
        try {
          const applicationDetails = await this.invoicesService.getApplicationDetailsForInvoice(invoice.entity_id);
          application = applicationDetails.application;
        } catch (err) {
          console.warn(`⚠️ Could not fetch application details for invoice ${id}:`, err);
        }
      }

      // Generate PDF
      const pdfBuffer = await this.invoicePdfService.generateInvoicePdf(invoice, application);

      // Check if we got actual PDF or HTML fallback
      const contentType = pdfBuffer.toString().startsWith('<!DOCTYPE html') ? 'text/html' : 'application/pdf';
      const fileExtension = contentType === 'text/html' ? 'html' : 'pdf';

      // Set response headers
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="invoice-${invoice.invoice_number}.${fileExtension}"`,
        'Content-Length': pdfBuffer.length,
      });
      res.send(pdfBuffer);
    } catch (error) {
      throw error;
    }
  }
}
