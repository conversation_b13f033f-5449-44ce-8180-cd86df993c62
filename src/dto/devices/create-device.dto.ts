import {
  IsDateString,
  <PERSON><PERSON>otEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Matches,
  IsEnum,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Example request body:
 * {
 *   "device_id": "123e4567-e89b-12d3-a456-************",
 *   "application_id": "123e4567-e89b-12d3-a456-************",
 *   "manufacturer_name": "Apple Inc.",
 *   "manufacturer_address": "One Apple Park Way, Cupertino, CA 95014, USA",
 *   "manufacturer_country": "United States",
 *   "brand_trade_name": "iPhone",
 *   "product_type_name": "Smartphone",
 *   "equipment_category_id": "123e4567-e89b-12d3-a456-************",
 *   "imei": "351756051523999",
 *   "device_type": "Mobile Phone",
 *   "model_name": "iPhone 15 Pro",
 *   "device_serial_number": "SN123456789ABC",
 *   "approval_status": "pending",
 *   "device_approval_number": "DEV-2024-001",
 *   "device_approval_date": "2024-01-15",
 *   "approval_notes": "Approved for use in Malawi telecommunications network"
 * }
 *
 * Note: created_by is optional and will be extracted from JWT token if not provided
 */

export class CreateDeviceDto {
  @ApiPropertyOptional({
    description: 'Device ID (UUID) - will be auto-generated if not provided',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Device ID must be a valid UUID if provided.' })
  device_id?: string;

  @ApiPropertyOptional({
    description: 'Application ID if device is being added as part of an application',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Application ID must be a valid UUID if provided.' })
  application_id?: string;

  @ApiProperty({
    description: 'Manufacturer name',
    example: 'Apple Inc.',
    maxLength: 100,
  })
  @IsString({ message: 'Manufacturer name must be a string.' })
  @Length(1, 100, { message: 'Manufacturer name must be between 1 and 100 characters' })
  @IsNotEmpty({ message: 'Manufacturer name is required.' })
  manufacturer_name: string;

  @ApiPropertyOptional({
    description: 'Manufacturer address',
    example: 'One Apple Park Way, Cupertino, CA 95014, USA',
  })
  @IsOptional()
  @IsString({ message: 'Manufacturer address must be a string.' })
  manufacturer_address?: string;

  @ApiProperty({
    description: 'Manufacturer country of origin',
    example: 'United States',
    maxLength: 100,
  })
  @IsString({ message: 'Manufacturer country must be a string.' })
  @Length(1, 100, { message: 'Manufacturer country must be between 1 and 100 characters' })
  @IsNotEmpty({ message: 'Manufacturer country is required.' })
  manufacturer_country: string;

  @ApiPropertyOptional({
    description: 'Brand or trade name',
    example: 'iPhone',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Brand/Trade name must be a string.' })
  @Length(1, 100, { message: 'Brand/Trade name must be between 1 and 100 characters' })
  brand_trade_name?: string;

  @ApiPropertyOptional({
    description: 'Product type or name',
    example: 'Smartphone',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Product type must be a string.' })
  @Length(1, 100, { message: 'Product type must be between 1 and 100 characters' })
  product_type_name?: string;


  @ApiPropertyOptional({
    description: 'Equipment model',
    example: 'HP EliteBook 840 G7',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Product type must be a string.' })
  @Length(1, 100, { message: 'Product type must be between 1 and 100 characters' })
  equipment_model?: string;


  @ApiPropertyOptional({
    description: 'Equipment category (text description)',
    example: 'Mobile Communication Device',
  })
  @IsOptional()
  @IsString({ message: 'Equipment category must be a string if provided.' })
  @Transform(({ value }) => value === '' ? undefined : value)
  equipment_category?: string;

  @ApiPropertyOptional({
    description: 'IMEI number - exactly 15 digits (International Mobile Equipment Identity). Optional for non-mobile devices. Format: AA-BBBBBB-CCCCCC-D (TAC-Serial-Check)',
    example: '351756051523999',
    pattern: '^\\d{15}$',
    minLength: 15,
    maxLength: 15,
  })
  @IsOptional()
  @IsString({ message: 'IMEI must be a string.' })
  @Matches(/^\d{15}$/, { message: 'IMEI must be exactly 15 digits' })
  imei?: string;

  @ApiProperty({
    description: 'Device approval status (required for frontend display)',
    enum: ['pending', 'approved', 'rejected', 'under_review', 'requires_documentation'],
    default: 'pending',
    example: 'pending',
  })
  @IsEnum(['pending', 'approved', 'rejected', 'under_review', 'requires_documentation'], { message: 'Approval status must be a valid status.' })
  @IsNotEmpty({ message: 'Approval status is required.' })
  approval_status: string = 'pending';

  @ApiPropertyOptional({
    description: 'Device approval number (assigned when approved)',
    example: 'MACRA-TA-2024-001',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Device approval number must be a string.' })
  @Length(1, 100, { message: 'Device approval number must be between 1 and 100 characters' })
  device_approval_number?: string;

  @ApiPropertyOptional({
    description: 'Device approval date',
    example: '2024-01-15',
    type: 'string',
    format: 'date',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Device approval date must be a valid ISO 8601 date string.' })
  device_approval_date?: string;

  @ApiPropertyOptional({
    description: 'Notes about the approval process or status',
    example: 'Approved for use in Malawi telecommunications network',
  })
  @IsOptional()
  @IsString({ message: 'Approval notes must be a string.' })
  approval_notes?: string;

  @ApiPropertyOptional({
    description: 'User ID who created this device record (can be extracted from JWT token if not provided)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided.' })
  created_by?: string;
}
