import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not, LessThan } from 'typeorm';
import { Task, TaskStatus, TaskPriority } from '../entities/tasks.entity';
import { User } from '../entities/user.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';
import { ApplicationsService } from '../applications/applications.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private readonly tasksRepository: Repository<Task>,
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    @Inject(forwardRef(() => ApplicationsService))
    private readonly applicationsService: ApplicationsService,
    private readonly notificationHelper: NotificationHelperService,
    private readonly usersService: UsersService,
  ) {}

  private readonly paginateConfig: PaginateConfig<Task> = {
    sortableColumns: ['created_at', 'updated_at', 'task_number', 'title', 'status', 'priority'],
    searchableColumns: ['task_number', 'title', 'description'],
    filterableColumns: {
      task_type: true,
      status: true,
      priority: true,
      assigned_to: true,
      created_by: true,
      entity_type: true,
      entity_id: true,
    },
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 20,
    maxLimit: 100,
    relations: ['assignee', 'assigner', 'creator'],
    select: [
      'task_id',
      'task_number',
      'title',
      'description',
      'task_type',
      'status',
      'priority',
      'entity_type',
      'entity_id',
      'assigned_to',
      'assigned_by',
      'assigned_at',
      'due_date',
      'completed_at',
      'review',
      'review_notes',
      'completion_notes',
      'created_at',
      'created_by',
      'updated_at',
      'updated_by',
      'assignee.user_id',
      'assignee.first_name',
      'assignee.last_name',
      'assignee.email',
      'assigner.user_id',
      'assigner.first_name',
      'assigner.last_name',
      'assigner.email',
      'creator.user_id',
      'creator.first_name',
      'creator.last_name',
      'creator.email',
    ],
  };

  /**
   * Helper method to update application assignment when task is assigned
   */
  private async updateApplicationAssignment(task: Task, assignedTo: string, assignedBy: string): Promise<void> {
    // Only update application if this is an application-related task
    if (task.entity_type === 'application' && task.entity_id) {
      try {
        await this.applicationsService.assignApplication(task.entity_id, assignedTo, assignedBy);
        // Also update application status to evaluation if not already
        const application = await this.applicationsService.findOne(task.entity_id);
        if (application.status !== 'evaluation') {
          await this.applicationsService.updateStatus(task.entity_id, 'evaluation', assignedBy);
        }
      } catch (error) {
        console.warn(`Failed to update application assignment for task ${task.task_id}:`, error);
        // Don't fail the task assignment if application update fails
      }
    }
  }

  /**
   * Helper method to send task assignment notification
   */
  private async sendTaskAssignmentNotification(task: Task, assignedBy: string): Promise<void> {
    try {
      // Check if task has an assignee
      if (!task.assigned_to) {
        console.warn(`No assignee found for task ${task.task_id}`);
        return;
      }

      // Get assignee details
      const assignee = await this.usersService.findById(task.assigned_to);
      if (!assignee) {
        console.warn(`Assignee not found for task ${task.task_id}`);
        return;
      }

      // Get application details if this is an application task
      let applicationNumber = 'N/A';
      let applicantName = 'N/A';
      if (task.entity_type === 'application' && task.entity_id) {
        try {
          const application = await this.applicationsService.findOne(task.entity_id);
          applicationNumber = application.application_number;
          applicantName = application.applicant?.name || 'N/A';
        } catch (error) {
          console.warn(`Failed to get application details for task ${task.task_id}:`, error);
        }
      }

      // Send notification
      await this.notificationHelper.notifyTaskAssignment(
        task.task_id,
        task.assigned_to!, // We already checked it's not undefined above
        assignee.email,
        task.title,
        task.description,
        assignedBy,
        `${assignee.first_name} ${assignee.last_name}`,
        applicationNumber,
        applicantName,
        task.priority,
        task.due_date?.toISOString()
      );
    } catch (error) {
      console.error(`Failed to send task assignment notification for task ${task.task_id}:`, error);
    }
  }

  async create(createTaskDto: CreateTaskDto, creatorId: string): Promise<Task> {
    // Validate that the creator exists
    if (!creatorId) {
      throw new BadRequestException('Creator ID is required');
    }

    const creator = await this.usersRepository.findOne({ where: { user_id: creatorId } });
    if (!creator) {
      throw new BadRequestException(`Creator with ID ${creatorId} not found`);
    }

    // Validate assigned_to user if provided
    if (createTaskDto.assigned_to) {
      const assignee = await this.usersRepository.findOne({ where: { user_id: createTaskDto.assigned_to } });
      if (!assignee) {
        throw new BadRequestException(`User with ID ${createTaskDto.assigned_to} not found`);
      }
    }
    // Generate unique task number with retry logic
    let taskNumber: string;
    let attempts = 0;
    const maxAttempts = 5;

    do {
      const taskCount = await this.tasksRepository.count();
      taskNumber = `TASK-${String(taskCount + 1 + attempts).padStart(6, '0')}`;

      // Check if this task number already exists
      const existingTask = await this.tasksRepository.findOne({
        where: { task_number: taskNumber }
      });

      if (!existingTask) {
        break; // Found a unique task number
      }

      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      // Fallback to timestamp-based task number
      taskNumber = `TASK-${Date.now().toString().slice(-6)}`;
    }

    const taskData: Partial<Task> = {
      task_type: createTaskDto.task_type,
      title: createTaskDto.title,
      description: createTaskDto.description,
      priority: createTaskDto.priority || TaskPriority.MEDIUM,
      status: createTaskDto.status || TaskStatus.PENDING,
      entity_type: createTaskDto.entity_type,
      entity_id: createTaskDto.entity_id,
      task_number: taskNumber,
      created_by: creatorId,
      assigned_by: creatorId, // Creator is also the assigner initially
      assigned_to: createTaskDto.assigned_to,
      metadata: createTaskDto.metadata,
    };

    if (createTaskDto.due_date) {
      taskData.due_date = new Date(createTaskDto.due_date);
    }

    if (createTaskDto.assigned_to) {
      taskData.assigned_at = new Date();
    }

    try {
      const task = this.tasksRepository.create(taskData);
      const savedTask = await this.tasksRepository.save(task);
      // Update application assignment if this is an application task and it's assigned
      if (createTaskDto.assigned_to) {
        try {
          await this.updateApplicationAssignment(savedTask, createTaskDto.assigned_to, creatorId);
        } catch (assignmentError) {
        }
      }
      return savedTask;
    } catch (error) {
      throw error;
    }
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Task>> {
    // Handle special case for unassigned tasks filter
    const modifiedQuery = { ...query };
    if (query.filter && 'assigned_to' in query.filter && query.filter.assigned_to === 'null') {
      // Create a custom config for unassigned tasks
      const config: PaginateConfig<Task> = {
        ...this.paginateConfig,
        where: { assigned_to: IsNull() },
      };
      // Remove assigned_to from filter to avoid conflict
      const { assigned_to, ...otherFilters } = query.filter;
      modifiedQuery.filter = Object.keys(otherFilters).length > 0 ? otherFilters : undefined;
      return paginate(modifiedQuery, this.tasksRepository, config);
    }

    return paginate(query, this.tasksRepository, this.paginateConfig);
  }

  async findUnassigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: IsNull() },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssigned(query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: Not(IsNull()) },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findAssignedToUser(userId: string, query: PaginateQuery): Promise<Paginated<Task>> {
    const config: PaginateConfig<Task> = {
      ...this.paginateConfig,
      where: { assigned_to: userId },
    };

    return paginate(query, this.tasksRepository, config);
  }

  async findOne(id: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { task_id: id },
      relations: ['assignee', 'assigner', 'creator', 'updater'],
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${id} not found`);
    }

    return task;
  }

  /**
   * Get task with basic navigation information
   * Frontend will handle the actual navigation logic based on entity_type and entity_id
   */
  async findOneWithNavigationInfo(id: string): Promise<{
    task: Task;
    canNavigateToEntity: boolean;
  }> {
    const task = await this.findOne(id);

    // Just check if the task has entity information for navigation
    const canNavigateToEntity = !!(task.entity_type && task.entity_id);

    return {
      task,
      canNavigateToEntity,
    };
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task> {
    const task = await this.findOne(id);

    // Update completion timestamp if status is changed to completed
    if (updateTaskDto.status === 'completed' && task.status !== 'completed') {
      task.completed_at = new Date();
    }

    Object.assign(task, updateTaskDto);
    return this.tasksRepository.save(task);
  }

  async assign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    if (task.assigned_to) {
      throw new BadRequestException('Task is already assigned to another user. Use reassign instead.');
    }

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set assignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
      task.review_notes = notes;
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    // Send notification to assigned user
    await this.sendTaskAssignmentNotification(savedTask, assignerId);

    return savedTask;
  }

  async reassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    const previousAssignee = task.assigned_to;

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set reassignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
      const reassignmentNote = `Reassigned from ${previousAssignee || 'unassigned'} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
      task.review_notes = reassignmentNote;
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    // Send notification to assigned user
    await this.sendTaskAssignmentNotification(savedTask, assignerId);

    return savedTask;
  }

  /**
   * Unified method to assign or reassign a task
   * Automatically determines whether to assign or reassign based on current state
   */
  async assignOrReassign(id: string, assignTaskDto: AssignTaskDto, assignerId: string): Promise<Task> {
    const task = await this.findOne(id);

    const isReassignment = !!task.assigned_to;
    const previousAssignee = task.assigned_to;

    task.assigned_to = assignTaskDto.assignedTo;
    task.assigned_by = assignerId;
    task.assigned_at = new Date();

    // Set due date if provided
    if (assignTaskDto.due_date) {
      task.due_date = new Date(assignTaskDto.due_date);
    }

    // Set priority if provided
    if (assignTaskDto.priority) {
      task.priority = assignTaskDto.priority;
    }

    // Set assignment/reassignment notes
    if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
      const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;

      if (isReassignment) {
        const reassignmentNote = `Reassigned from ${previousAssignee} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
        task.review_notes = reassignmentNote;
      } else {
        task.review_notes = notes;
      }
    }

    const savedTask = await this.tasksRepository.save(task);

    // Update application assignment if this is an application task
    await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);

    // Send notification to assigned user
    await this.sendTaskAssignmentNotification(savedTask, assignerId);

    return savedTask;
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id); // Verify task exists
    await this.tasksRepository.softDelete(id);
  }

  /**
   * Test task assignment email notification
   */
  async testAssignmentEmail(taskId: string, testerId: string): Promise<{ message: string }> {
    const task = await this.findOne(taskId);

    if (!task.assigned_to) {
      throw new BadRequestException('Task must be assigned to test email notification');
    }

    // Get assignee details
    const assignee = await this.usersService.findById(task.assigned_to);

    if (!assignee) {
      throw new NotFoundException('Assigned user not found');
    }

    // Send test notification
    await this.sendTaskAssignmentNotification(task, testerId);

    return {
      message: `Test assignment email sent to ${assignee.email} for task: ${task.title}`
    };
  }

  async getTaskStats(): Promise<{
    total: number;
    pending: number;
    in_progress: number;
    unassigned: number;
    assigned: number;
    completed: number;
    cancelled: number;
    on_hold: number;
    overdue: number;
  }> {
    const [total, pending, in_progress, unassigned, assigned, completed, cancelled, on_hold, overdue] = await Promise.all([
      this.tasksRepository.count(),
      this.tasksRepository.count({ where: { status: TaskStatus.PENDING } }),
      this.tasksRepository.count({ where: { status: TaskStatus.IN_PROGRESS } }),
      this.tasksRepository.count({ where: { assigned_to: IsNull() } }),
      this.tasksRepository.count({ where: { assigned_to: Not(IsNull()) } }),
      this.tasksRepository.count({ where: { status: TaskStatus.COMPLETED } }),
      this.tasksRepository.count({ where: { status: TaskStatus.CANCELLED } }),
      this.tasksRepository.count({ where: { status: TaskStatus.ON_HOLD } }),
      this.tasksRepository.count({
        where: {
          due_date: Not(IsNull()),
          status: Not(TaskStatus.COMPLETED),
        },
      }),
    ]);

    return {
      total,
      pending,
      in_progress,
      unassigned,
      assigned,
      completed,
      cancelled,
      on_hold,
      overdue,
    };
  }

  /**
   * Find existing task for an application
   */
  async findTaskForApplication(applicationId: string): Promise<Task | null> {
    const task = await this.tasksRepository.findOne({
      where: {
        entity_type: 'application',
        entity_id: applicationId,
      },
      relations: ['assignee', 'assigner', 'creator', 'updater'],
    });

    return task;
  }

  /**
   * Find all tasks related to an application
   */
  async findAllTasksForApplication(applicationId: string): Promise<Task[]> {
    const tasks = await this.tasksRepository.find({
      where: {
        entity_type: 'application',
        entity_id: applicationId,
      },
      relations: ['assignee', 'assigner', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });

    return tasks;
  }

  /**
   * Close all open tasks related to an application
   */
  async closeAllTasksForApplication(applicationId: string, closedBy: string, reason?: string): Promise<Task[]> {
    const defaultReason = reason || 'Application evaluation completed - tasks automatically closed';
    const result =  await this.closeAllTasks(applicationId, 'application',closedBy, defaultReason);        
    return [];
  }

  async closeAllTasks(entity_id: string, entity_type: string, closedBy: string, reason?: string): Promise<boolean> {
      // Find all open tasks for the application
    const openTasks = await this.tasksRepository.find({
      where: {
        entity_type: entity_type,
        entity_id: entity_id,
        status: 'pending', // Find all non-completed tasks
      },
      relations: ['assignee', 'assigner', 'creator', 'updater'],
    });
    if (openTasks.length === 0) {
      return true;
    }
    const closedTasks: Task[] = [];
    const defaultReason = reason || 'Tasks related to this entity have been completed';
    // Close each task
    for (const task of openTasks) {
      try {
        task.status = 'completed';
        task.completed_at = new Date();
        task.updated_by = closedBy;
        // Add completion notes
        const completionNote = `${defaultReason}. Closed automatically on ${new Date().toISOString()}.`;
        task.completion_notes = task.completion_notes
          ? `${task.completion_notes}\n\n${completionNote}`
          : completionNote;
        const savedTask = await this.tasksRepository.save(task);
        closedTasks.push(savedTask);
      } catch (error) {
      }
    }
    return true;
  }

  /**
   * Create task for application or reassign existing one
   */
  async createOrReassignApplicationTask(
    applicationId: string,
    createTaskDto: CreateTaskDto,
    assignTaskDto: AssignTaskDto,
    creatorId: string
  ): Promise<Task> {
    // Check if task already exists for this application
    const existingTask = await this.findTaskForApplication(applicationId);

    if (existingTask && existingTask.status !== 'completed') {
      // Update task details if provided
      if (createTaskDto.priority && createTaskDto.priority !== existingTask.priority) {
        existingTask.priority = createTaskDto.priority;
      }

      if (assignTaskDto.due_date) {
        existingTask.due_date = new Date(assignTaskDto.due_date);
      }

      // Save any updates to the task
      await this.tasksRepository.save(existingTask);

      // Reassign the existing task
      return this.assignOrReassign(existingTask.task_id, assignTaskDto, creatorId);
    } else {
      // Create new task with assignment
      const taskData = {
        ...createTaskDto,
        assigned_to: assignTaskDto.assignedTo,
      };

      const newTask = await this.create(taskData, creatorId);

      // The create method already handles assignment, but we need to set assignment details
      if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
        const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
        newTask.review_notes = notes;
        await this.tasksRepository.save(newTask);
      }

      // Send notification (create method doesn't send notifications for assigned tasks)
      if (newTask.assigned_to) {
        try {
          await this.sendTaskAssignmentNotification(newTask, creatorId);
        } catch (notificationError) {
          console.error(`⚠️ Failed to send task assignment notification for task ${newTask.task_id}:`, notificationError);
          // Don't fail the task creation if notification fails
        }
      }

      return newTask;
    }
  }
}