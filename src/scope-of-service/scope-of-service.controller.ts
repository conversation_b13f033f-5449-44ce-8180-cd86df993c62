import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ScopeOfServiceService } from './scope-of-service.service';
import { CreateScopeOfServiceDto } from '../dto/scope-of-service/create-scope-of-service.dto';
import { UpdateScopeOfServiceDto } from '../dto/scope-of-service/update-scope-of-service.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { ScopeOfService } from '../entities/scope-of-service.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Scope of Service')
@Controller('scope-of-service')
export class ScopeOfServiceController {
  constructor(private readonly scopeOfServiceService: ScopeOfServiceService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new scope of service' })
  @ApiBody({ type: CreateScopeOfServiceDto, description: 'Create scope of service DTO' })
  @ApiResponse({ status: 201, description: 'Scope of service created successfully', type: ScopeOfService })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  create(@Body() createDto: CreateScopeOfServiceDto, @Request() req: any) {
    return this.scopeOfServiceService.create(createDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all scope of services' })
  @ApiResponse({ status: 200, description: 'List of scope of services', type: [ScopeOfService] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findAll() {
    return this.scopeOfServiceService.findAll();
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get scope of service by application ID' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiResponse({ status: 200, description: 'Scope of service found', type: ScopeOfService })
  @ApiResponse({ status: 404, description: 'Scope of service not found' })
  @ApiResponse({ status: 400, description: 'Invalid application ID format' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  async findByApplication(@Param('applicationId') applicationId: string) {
    try {
      const result = await this.scopeOfServiceService.findByApplication(applicationId);
      return result;
    } catch (error) {
      console.error(`Error in findByApplication controller for ${applicationId}:`, error);
      throw error;
    }
  }

  @Post('application/:applicationId')
  @ApiOperation({ summary: 'Create or update scope of service for application' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiBody({ type: CreateScopeOfServiceDto, description: 'Scope of service data (without application_id)' })
  @ApiResponse({ status: 200, description: 'Scope of service created or updated', type: ScopeOfService })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  createOrUpdateForApplication(
    @Param('applicationId') applicationId: string,
    @Body() createDto: Omit<CreateScopeOfServiceDto, 'application_id'>,
    @Request() req: any
  ) {
    return this.scopeOfServiceService.createOrUpdate(applicationId, createDto, req.user.userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get scope of service by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'Scope of service UUID' })
  @ApiResponse({ status: 200, description: 'Scope of service found', type: ScopeOfService })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findOne(@Param('id') id: string) {
    return this.scopeOfServiceService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update scope of service by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiBody({ type: UpdateScopeOfServiceDto, description: 'Update scope of service DTO' })
  @ApiResponse({ status: 200, description: 'Scope of service updated', type: ScopeOfService })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  update(@Param('id') id: string, @Body() updateDto: UpdateScopeOfServiceDto, @Request() req: any) {
    return this.scopeOfServiceService.update(id, updateDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete scope of service by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiResponse({ status: 204, description: 'Scope of service deleted' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  remove(@Param('id') id: string) {
    return this.scopeOfServiceService.softDelete(id);
  }
}
