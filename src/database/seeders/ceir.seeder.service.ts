import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CeirEquipmentTypeCategories } from '../../ceir/entities/ceir-equipment-type-categories.entity';
import { CeirTechnicalStandards } from '../../ceir/entities/ceir-technical-standards.entity';
import { User } from '../../entities/user.entity';
import CeirEquipmentTypeCategoriesSeeder from './ceir-equipment-type-categories.seeder';
import CeirTechnicalStandardsSeeder from './ceir-technical-standards.seeder';

@Injectable()
export class CeirSeederService {
  constructor(
    @InjectRepository(CeirEquipmentTypeCategories)
    private ceirEquipmentTypeCategoriesRepository: Repository<CeirEquipmentTypeCategories>,
    @InjectRepository(CeirTechnicalStandards)
    private ceirTechnicalStandardsRepository: Repository<CeirTechnicalStandards>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async seedCeir(): Promise<void> {
    console.log('🌱 Seeding CEIR data...');

    // Create a data source-like object for the seeders
    const dataSource = {
      getRepository: (entity: any) => {
        if (entity === CeirEquipmentTypeCategories) {
          return this.ceirEquipmentTypeCategoriesRepository;
        }
        if (entity === CeirTechnicalStandards) {
          return this.ceirTechnicalStandardsRepository;
        }
        if (entity === User) {
          return this.userRepository;
        }
        throw new Error(`Repository not found for entity: ${entity.name}`);
      }
    };

    // Run equipment type categories seeder
    const equipmentCategoriesSeeder = new CeirEquipmentTypeCategoriesSeeder();
    await equipmentCategoriesSeeder.run(dataSource as any);

    // Run technical standards seeder
    const technicalStandardsSeeder = new CeirTechnicalStandardsSeeder();
    await technicalStandardsSeeder.run(dataSource as any);

    console.log('✅ CEIR data seeding completed.');
  }

  async seedAll(): Promise<void> {
    await this.seedCeir();
  }

  async clearAll(): Promise<void> {
    console.log('🗑️ Clearing CEIR data...');

    try {
      // Clear in reverse order due to potential foreign key constraints
      // Use query builder to delete all records
      await this.ceirTechnicalStandardsRepository
        .createQueryBuilder()
        .delete()
        .execute();
      console.log('✅ Cleared CEIR technical standards');

      await this.ceirEquipmentTypeCategoriesRepository
        .createQueryBuilder()
        .delete()
        .execute();
      console.log('✅ Cleared CEIR equipment type categories');

      console.log('🗑️ CEIR data cleared');
    } catch (error) {
      console.error('❌ Error clearing CEIR data:', error.message);
      throw error;
    }
  }
}
