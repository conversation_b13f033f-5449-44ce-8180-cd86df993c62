import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
  Index,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Address } from "./address.entity";
import { Contacts } from "./contacts.entity";
import { User } from "./user.entity";
import { Applicants } from "./applicant.entity";
import { BaseSoftDeleteEntity } from "../common/entities/base-soft-delete.entity";


@Entity('organizations')
export class Organization extends BaseSoftDeleteEntity {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  organization_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', unique: true })
  registration_number: string;

  @Column({ type: 'varchar' })
  website: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar', length: 20 })
  phone: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  fax?: string;

  @Column({ type: 'uuid', nullable: true })
  physical_address_id?: string;

  @Column({ type: 'uuid', nullable: true })
  postal_address_id?: string;

  @Column({ type: 'uuid', nullable: true })
  contact_id?: string;

  @Column({ type: 'date' })
  date_incorporation: Date;

  @Column({ type: 'varchar' })
  place_incorporation: string;



  @ManyToOne(() => Address)
  @JoinColumn({ name: 'physical_address_id' })
  physical_address?: Address;

  @ManyToOne(() => Address)
  @JoinColumn({ name: 'postal_address_id' })
  postal_address?: Address;

  @ManyToOne(() => Contacts)
  @JoinColumn({ name: 'contact_id' })
  contact?: Contacts;



  @BeforeInsert()
  generateId() {
    if (!this.organization_id) {
      this.organization_id = uuidv4();
    }
  }
}