import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  HttpStatus
} from '@nestjs/common';
import { StandardsService } from './standards.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { CreateShortcodeDto } from 'src/dto/shortcodes/create-shortcode.dto';
import { UpdateShortcodeDto } from 'src/dto/shortcodes/update-shortcode.dto';
import { Shortcode } from 'src/entities/shortcode.entity';
import { RolesGuard } from 'src/common/guards/roles.guard';

@ApiTags('Shortcode Application')
@Controller('standards')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class StandardsController {
  constructor(private readonly standardsService: StandardsService) {}

  // ==================== SHORTCODE ENDPOINTS ====================

  @Post('shortcodes')
  @Roles('customer', 'officer')
  @ApiOperation({
    summary: 'Create a new shortcode',
    description: 'Creates a new shortcode for standards management. Requires customer or officer role.'
  })
  @ApiBody({
    type: CreateShortcodeDto,
    description: 'Shortcode creation data including code, description, and status'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Shortcode created successfully',
    type: Shortcode
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors'
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Shortcode with this code already exists'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - requires customer or officer role'
  })
  async createShortcode(
    @Body() createShortcodeDto: CreateShortcodeDto,
    @Req() req: any
  ): Promise<Shortcode> {
    return this.standardsService.createShortcode(
      createShortcodeDto,
      req.user?.userId
    );
  }

  @Get('shortcodes')
  @Roles('customer', 'officer', 'admin', 'standards')
  @ApiOperation({
    summary: 'Get all shortcodes',
    description: 'Retrieves a list of all shortcodes. Available to customers, officers, admins, and standards personnel.'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by shortcode status',
    enum: ['active', 'inactive', 'pending', 'expired']
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search shortcodes by code or description'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of shortcodes retrieved successfully',
    type: [Shortcode]
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - requires customer, officer, admin, or standards role'
  })
  async findAllShortcodes(
    @Query('status') status?: string,
    @Query('search') search?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ): Promise<Shortcode[]> {
    // TODO: Implement filtering and pagination based on query parameters
    return this.standardsService.findAllShortcodes();
  }

  @Get('shortcodes/:id')
  @Roles('customer', 'officer', 'admin', 'standards')
  @ApiOperation({
    summary: 'Get shortcode by ID',
    description: 'Retrieves a specific shortcode by its unique identifier.'
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the shortcode',
    type: String
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shortcode retrieved successfully',
    type: Shortcode
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shortcode not found'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions'
  })
  async findOneShortcode(@Param('id') id: string): Promise<Shortcode> {
    return this.standardsService.findOneShortcode(id);
  }

  @Get('shortcodes/application/:applicationId')
  // @Roles('customer', 'officer', 'admin', 'standards')
  @ApiOperation({
    summary: 'Get shortcodes by application ID',
    description: 'Retrieves shortcodes associated with a specific application.'
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application UUID',
    type: String
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shortcodes retrieved successfully',
    type: [Shortcode]
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'No shortcodes found for this application'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions'
  })
  async findShortcodesByApplication(@Param('applicationId') applicationId: string): Promise<Shortcode[]> {
    return this.standardsService.findShortcodesByApplication(applicationId);
  }

  @Post('shortcodes/application/:applicationId')
  @Roles('customer', 'officer')
  @ApiOperation({
    summary: 'Create or update shortcode for application',
    description: 'Creates a new shortcode or updates existing shortcode for a specific application.'
  })
  @ApiParam({
    name: 'applicationId',
    description: 'Application UUID',
    type: String
  })
  @ApiBody({
    type: CreateShortcodeDto,
    description: 'Shortcode data (application_id will be set automatically)'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Shortcode created or updated successfully',
    type: Shortcode
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions'
  })
  async createOrUpdateShortcodeForApplication(
    @Param('applicationId') applicationId: string,
    @Body() createDto: Omit<CreateShortcodeDto, 'application_id'>,
    @Req() req: any
  ): Promise<Shortcode> {
    return this.standardsService.createOrUpdateShortcodeForApplication(
      applicationId,
      createDto,
      req.user?.userId
    );
  }

  @Patch('shortcodes/:id')
  @Roles('customer', 'officer', 'standards')
  @ApiOperation({
    summary: 'Update shortcode',
    description: 'Updates an existing shortcode. Requires admin, officer role.'
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the shortcode to update',
    type: String
  })
  @ApiBody({
    type: UpdateShortcodeDto,
    description: 'Shortcode update data'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shortcode updated successfully',
    type: Shortcode
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shortcode not found'
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or validation errors'
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Shortcode code already exists'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions'
  })
  async updateShortcode(
    @Param('id') id: string,
    @Body() updateShortcodeDto: UpdateShortcodeDto,
    @Req() req: any
  ): Promise<Shortcode> {
    return this.standardsService.updateShortcode(
      id,
      updateShortcodeDto,
      req.user?.userId,
      req.user?.role
    );
  }

  @Delete('shortcodes/:id')
  @Roles('admin', 'officer', 'standards')
  @ApiOperation({
    summary: 'Delete shortcode',
    description: 'Soft deletes a shortcode. Requires admin or super_admin role.'
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the shortcode to delete',
    type: String
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Shortcode deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Shortcode deleted successfully' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Shortcode not found'
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required'
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions'
  })
  async removeShortcode(@Param('id') id: string): Promise<{ message: string }> {
    await this.standardsService.removeShortcode(id);
    return { message: 'Shortcode deleted successfully' };
  }
}
