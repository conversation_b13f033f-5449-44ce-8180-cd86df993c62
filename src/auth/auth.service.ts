import { Injectable, UnauthorizedException, BadRequestException, Logger, InternalServerErrorException, Body, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../entities/user.entity';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto, ResetPasswordWithTokenDto } from '../dto/auth/forgot-password.dto';
import { RequestTwoFactorDto, TwoFactorDto } from '../dto/auth/two-factor.dto';
import * as speakeasy from "speakeasy";
import * as qrcode from "qrcode";
import * as bcrypt from "bcryptjs";

import { Request } from "express";
import {
  AuthConstants,
  EmailTemplates,
  EmailSubjects,
  TwoFactorAction,
  TwoFactorMessages,
  AuthMessages,
  AuthUtils
} from '../common/constants/auth.constants';
import { EmailService } from '../common/services/email.service';
import { DeviceInfoService } from '../common/services/device-info.service';
import { ErrorHandler, ErrorContext } from '../common/utils/error-handler.util';
import {
  JwtPayload,
  AuthResponse,
  TwoFactorCodeResult,
  TwoFactorSetupResult,
  LoginResult,
  RegisterResult,
  PasswordResetResult,
  isValidUserId,
  isValidEmail
} from '../common/types/auth.types';



@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emailService: EmailService,
    private deviceInfoService: DeviceInfoService,
  ) { }

  async validateUser(email: string, password: string): Promise<User | null> {
    this.logger.log(`Validating user: ${email}`);

    // First try to find active user
    let user = await this.usersService.findByEmail(email);
    this.logger.log(`Active user found: ${user ? 'YES' : 'NO'}`);

    // If no active user found, check for soft-deleted user (for reactivation)
    if (!user) {
      const deleted_user = await this.usersService.findByEmailIncludingDeleted(email);
      this.logger.log(`Soft-deleted user found: ${user ? 'YES' : 'NO'}`);
      if (!deleted_user) {
        return null;
      }
      user = deleted_user;
    }

    const isPasswordValid = await this.usersService.validatePassword(password, user.password);
    this.logger.log(`Password valid: ${isPasswordValid ? 'YES' : 'NO'}`);

    if (!isPasswordValid) {
      ErrorHandler.logWarning(this.logger, 'Invalid credentials provided', ErrorHandler.createActionContext(email, 'login'));
      throw new UnauthorizedException(AuthMessages.INVALID_CREDENTIALS);
    }
    return user;
  }


  async login(loginDto: LoginDto, req: Request): LoginResult {
    const { email } = loginDto;

    // Input validation
    if (!isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    const context = ErrorHandler.createActionContext(email, 'login');

    try {
      ErrorHandler.logInfo(this.logger, 'Login attempt started', context);

      const user = await this.validateUser(email, loginDto.password);
      if (!user) {
        ErrorHandler.logWarning(this.logger, 'Invalid credentials provided', context);
        throw new UnauthorizedException(AuthMessages.INVALID_CREDENTIALS);
      }

      // Check if user is deactivated (soft-deleted)
      if (user.deleted_at) {
        ErrorHandler.logInfo(this.logger, 'Deactivated user login attempt - requires recovery',
          ErrorHandler.createUserContext(user, 'login-recovery'));
        // Generate 2FA code for account recovery
        await this.generateTwoFactorCode(user.user_id, TwoFactorAction.LOGIN);
        // Return special response indicating account recovery needed
        return AuthUtils.createAuthResponse(user, '');
      }

      if (user.status !== 'active') {
        ErrorHandler.logWarning(this.logger, 'Inactive account login attempt',
          ErrorHandler.createUserContext(user, 'login', { status: user.status }));
        throw new UnauthorizedException(AuthMessages.ACCOUNT_INACTIVE);
      }

      // Check if email is verified
      if (!user.email_verified_at) {
        ErrorHandler.logWarning(this.logger, 'Unverified email login attempt',
          ErrorHandler.createUserContext(user, 'login'));
        throw new UnauthorizedException('Please verify your email address before logging in. Check your inbox for the verification link.');
      }

      await this.usersService.updateLastLogin(user.user_id);
      ErrorHandler.logInfo(this.logger, 'Last login updated successfully', ErrorHandler.createUserContext(user, 'login'));
      
      user.two_factor_enabled = (process.env.ENABLE_2FA || true ) as boolean; 
      const payload = this.createJwtPayload(user);
      const requires2FA = AuthUtils.requires2FA(user.two_factor_enabled, user.last_login);

      ErrorHandler.logInfo(this.logger, `Login flow - 2FA required: ${requires2FA}, User 2FA enabled: ${user.two_factor_enabled}`,
        ErrorHandler.createUserContext(user, 'login', { requires2FA, twoFactorEnabled: user.two_factor_enabled }));

      const accessToken = this.jwtService.sign(payload);
      const response = AuthUtils.createAuthResponse(user, accessToken);

      if (requires2FA) {
        ErrorHandler.logInfo(this.logger, '2FA required, sending OTP',
          ErrorHandler.createUserContext(user, 'login-2fa'));
        await this.generateTwoFactorCode(user.user_id, TwoFactorAction.LOGIN);
      } else {
        ErrorHandler.logInfo(this.logger, 'Login successful without 2FA',
          ErrorHandler.createUserContext(user, 'login'));
      }

      return response;

    } catch (error) {
      if (!(error instanceof UnauthorizedException)) {
        ErrorHandler.handleError(this.logger, error, 'Login process failed', context);
      }
      throw error;
    }
  }


  async register(registerDto: RegisterDto): RegisterResult {
    const context = ErrorHandler.createActionContext(registerDto.email, 'register');

    try {
      ErrorHandler.logInfo(this.logger, 'Registration attempt started', context);
      const user = await this.usersService.create(registerDto);
      // Generate email verification code using the 2FA system
      try {
        await this.generateTwoFactorCode(user.user_id, TwoFactorAction.REGISTER);
      } catch (twoFactorError) {
        // Don't fail registration if 2FA fails - user can still be created
        this.logger.warn(`2FA generation failed for user ${user.user_id}: ${twoFactorError.message}`);
        // Continue with registration even if 2FA fails
      }

      ErrorHandler.logInfo(this.logger, 'Registration successful',
        ErrorHandler.createUserContext(user, 'register'));
      // Return user data without access token - user needs to verify email first
      return AuthUtils.createAuthResponse(user, '');
    } catch (error) {
      console.error('Registration error:', error);
      if (!(error instanceof BadRequestException || error instanceof ConflictException || error.message.includes('duplicate'))) {
        ErrorHandler.handleError(this.logger, error, 'Registration process failed', context);
      }
      throw error;
    }
  }



  async validateJwtPayload(payload: JwtPayload): Promise<User | null> {
    return this.usersService.findByEmail(payload.email);
  }

  /**
   * Create JWT payload from user data
   */
  private createJwtPayload(user: User): JwtPayload {
    const payload = {
      email: user.email,
      sub: user.user_id,
      roles: user.roles?.map(role => role.name) || [],
    };

    // Validate payload has required fields
    if (!payload.email || !payload.sub) {
      this.logger.error('Invalid JWT payload - missing required fields', {
        email: !!payload.email,
        sub: !!payload.sub,
        userId: user.user_id
      });
      throw new Error('Invalid user data for JWT payload');
    }

    return payload;
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const user = await this.usersService.findByEmail(forgotPasswordDto.email);
    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent.' };
    }

    // Generate JWT token for password reset
    const resetToken = this.generatePasswordResetToken(user.user_id);

    // Send password reset email with JWT token
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    this.emailService.sendPasswordResetRequestEmail(
      user.email,
      {
        name: user.first_name,
        resetUrl: resetUrl
      }
    ).catch((error) => {
      console.error('Failed to send password reset email:', error);
      this.logger.error('Failed to send password reset email:', error);
    });

    return { message: 'If the email exists, a password reset link has been sent.' };
  }

  /**
   * Reset password, triggered with `forgotPassword`
   * @param email
   * @param code - Verified with verifyTwoFactorCode
   * @param newPassword - Verified with confirm password
   * @returns message
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): PasswordResetResult {
    const user = await this.usersService.findById(resetPasswordDto.user_id);
    if (!user) {
      throw new BadRequestException(AuthMessages.INVALID_RESET_CODE);
    }

    // Compare existing password and new password
    if (await this.usersService.validatePassword(resetPasswordDto.new_password, user.password)) {
      throw new BadRequestException(AuthMessages.PASSWORD_SAME_AS_CURRENT);
    }

    await this.usersService.updatePassword(user.user_id, resetPasswordDto.new_password);
    await this.usersService.clearTempTwoFactorCode(user.user_id);
    await this.usersService.clearTwoFactorCode(user.user_id);

    await this.emailService.sendPasswordResetConfirmationEmail(
      user.email,
      {
        userName: user.first_name,
        loginUrl: `${process.env.FRONTEND_URL}${AuthConstants.URL_PATTERNS.LOGIN_PATH}`
      }
    );

    return { message: AuthUtils.formatMessage(AuthMessages.PASSWORD_RESET_SUCCESS, { email: user.email }) };
  }

  /**
   * Generate JWT token for password reset
   * @param userId - User ID
   * @returns JWT token
   */
  private generatePasswordResetToken(userId: string): string {
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      sub: userId,
      type: 'password_reset',
      iat: now,
      // Include current timestamp for token validation
      timestamp: now
    };

    // Token expires in 5 minutes
    return this.jwtService.sign(payload, { expiresIn: '5m' });
  }

  /**
   * Verify password reset token and extract user ID
   * @param token - JWT token
   * @returns User ID and timestamp if token is valid
   */
  private async verifyPasswordResetToken(token: string): Promise<{ userId: string; timestamp: number }> {
    try {
      const payload = this.jwtService.verify(token);

      if (payload.type !== 'password_reset') {
        throw new BadRequestException('Invalid token type');
      }

      return {
        userId: payload.sub,
        timestamp: payload.timestamp
      };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new BadRequestException('Password reset token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new BadRequestException('Invalid password reset token');
      }
      throw new BadRequestException('Invalid password reset token');
    }
  }

  /**
   * Reset password using JWT token
   * @param resetPasswordWithTokenDto - Contains JWT token and new password
   * @returns Success message
   */
  async resetPasswordWithToken(resetPasswordWithTokenDto: ResetPasswordWithTokenDto): PasswordResetResult {
    // Verify and decode the JWT token
    const { userId } = await this.verifyPasswordResetToken(resetPasswordWithTokenDto.token);

    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Compare existing password and new password
    if (await this.usersService.validatePassword(resetPasswordWithTokenDto.new_password, user.password)) {
      throw new BadRequestException(AuthMessages.PASSWORD_SAME_AS_CURRENT);
    }

    // Update password
    await this.usersService.updatePassword(user.user_id, resetPasswordWithTokenDto.new_password);

    // Clear any existing 2FA codes
    await this.usersService.clearTempTwoFactorCode(user.user_id);
    await this.usersService.clearTwoFactorCode(user.user_id);

    // Send confirmation email
    this.emailService.sendPasswordResetConfirmationEmail(
      user.email,
      {
        userName: user.first_name,
        loginUrl: `${process.env.FRONTEND_URL}${AuthConstants.URL_PATTERNS.LOGIN_PATH}`
      }
    ).catch((error) => {
      console.error('Failed to send password reset confirmation email:', error);
      this.logger.error('Failed to send password reset confirmation email:', error);
    });

    return { message: AuthUtils.formatMessage(AuthMessages.PASSWORD_RESET_SUCCESS, { email: user.email }) };
  }

  /**
   * Verify if a password reset token is valid
   * @param token - JWT token to verify
   * @returns Validation result
   */
  async verifyResetToken(token: string): Promise<{ valid: boolean; message: string }> {
    try {
      if (!token) {
        return { valid: false, message: 'Token is required' };
      }

      const { userId } = await this.verifyPasswordResetToken(token);

      const user = await this.usersService.findById(userId);
      if (!user) {
        return { valid: false, message: 'User not found' };
      }

      return { valid: true, message: 'Token is valid' };
    } catch (error) {
      return { valid: false, message: error.message || 'Invalid token' };
    }
  }

  async setupTwoFactorAuth(@Body() requestTwoFactorDto: RequestTwoFactorDto): Promise<{ otpAuthUrl: string, qrCodeDataUrl: string, secret: string, message: string }> {
    const userId = requestTwoFactorDto.user_id;
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`First time 2FA setup failed: User ID ${userId} not found!`);
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }
    if (user.two_factor_enabled) {
      return {
        otpAuthUrl: '',
        qrCodeDataUrl: '',
        secret: '',
        message: AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_ALREADY_ENABLED, { email: user.email }),
      };
    }
    const generateCode = await this.generateTwoFactorCode(requestTwoFactorDto.user_id, TwoFactorAction.VERIFY);
    const otpAuthUrl = generateCode.otpAuthUrl;
    const secret = generateCode.secret;
    const qrCodeDataUrl = await qrcode.toDataURL(otpAuthUrl);

    return {
      otpAuthUrl,
      qrCodeDataUrl,
      secret: secret,
      message: AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_SETUP_SUCCESS, { email: user.email })
    };
  }

  /**
   *
   * @param userId
   * @param action  can be either `reset`, `login`, `verify` or `register`. URL defaults to `verify-2fa`
   * @returns message
   */
  /**
 * Generates a 2FA code and sends an email with the OTP link
 * @param userId ID of the user
 * @param action 'login' | 'reset' | 'verify' | 'register'
 */
  async generateTwoFactorCode(userId: string, action: TwoFactorAction): Promise<{
    message: string,
    otpAuthUrl: string,
    hashedToken: string,
    secret: string
  }> {
    // Input validation
    if (!isValidUserId(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`2FA attempt failed: User ID ${userId} not found`);
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }

    const secret = speakeasy.generateSecret({
      name: AuthConstants.TWO_FACTOR.ISSUER_NAME,
      length: AuthConstants.TWO_FACTOR.SECRET_LENGTH
    });
    if (!secret.otpauth_url) {
      throw new InternalServerErrorException('Failed to generate OTP URL');
    }

    const token = speakeasy.totp({ secret: secret.base32, encoding: 'base32' });
    const hashedToken = await bcrypt.hash(token, AuthConstants.TWO_FACTOR.CODE_HASH_ROUNDS);
    const expiresAt = AuthUtils.createExpiryDate();

    const emailTemplate = action === TwoFactorAction.RESET ? EmailTemplates.RESET : (action === TwoFactorAction.REGISTER ? EmailTemplates.REGISTER : EmailTemplates.TWO_FACTOR);
    const verifyUrl = EmailService.createVerificationUrl(userId, secret.base32, token, action, user.roles);
    const subject = action === TwoFactorAction.REGISTER ? EmailSubjects.REGISTER_EMAIL : EmailSubjects.VERIFY_OTP;

    try {
      if (action === TwoFactorAction.LOGIN) {
        await this.usersService.setTwoFactorCode(userId, hashedToken, expiresAt);
      } else {
        await this.usersService.setTwoFactorCodeTempReset(userId, secret.base32, hashedToken, expiresAt);
      }

      await this.emailService.send2FAEmail(
        user.email,
        emailTemplate,
        subject,
        {
          name: user.first_name,
          message: TwoFactorMessages[action],
          verifyUrl,
          otp: token,
        }
      );
      return {
        message: AuthMessages.TWO_FACTOR_CODE_SENT,
        otpAuthUrl: secret.otpauth_url,
        hashedToken,
        secret: secret.base32,
      };
    } catch (error) {
      this.logger.error(`2FA generation failed for ${user.email}`, error);
      throw new InternalServerErrorException('Failed to generate or send 2FA code');
    }
  }

  /**
   * 
   * @param twoFactorDto Contains user_id, unique and otp code
   * @param req Request data
   * @returns AuthResponse | { message: string }
   */
  async verifyTwoFactorCode(twoFactorDto: TwoFactorDto, req: Request): Promise<AuthResponse | { message: string }> {
    // First check if user exists including soft-deleted ones (for recovery)
    let user = await this.usersService.findById(twoFactorDto.user_id);
    if (!user) {
      user = await this.usersService.findByIdIncludingDeleted(twoFactorDto.user_id);
    }

    if (!user) {
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }

    // If user is soft-deleted, this is an account recovery verification
    const isAccountRecovery = !!user.deleted_at;

    // Try to decode the unique parameter in case it's URL encoded
    let decodedUnique = twoFactorDto.unique;
    try {
      decodedUnique = decodeURIComponent(twoFactorDto.unique);
    } catch (e) {
      // If decoding fails, use the original value
      decodedUnique = twoFactorDto.unique;
    }
    // Verify Two Factor Code first
    if (!user.two_factor_code) {
      this.logger.warn('User does not have two factor code/ OTP saved');
      throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_LINK);
    }
    // Determine if this is a login OTP (manual code entry) or email verification link
    const isManualLoginOTP = user.two_factor_enabled && user.email_verified_at && (!twoFactorDto.unique || twoFactorDto.unique === '');
    const isEmailVerificationLink = !isManualLoginOTP && twoFactorDto.unique && twoFactorDto.unique !== '';

    // Include requirement for token if path is verify-2FA and it's an email verification link
    if (req.originalUrl.includes('verify-2fa') && isEmailVerificationLink) {
      if (!user.two_factor_temp) {
        this.logger.warn('User does not have saved token, crucial for verifying 2FA', {current_user: user});
        throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_LINK);
      }
      if (user.two_factor_temp !== twoFactorDto.unique && user.two_factor_temp !== decodedUnique) {
        this.logger.warn(`Invalid temp unique token for user ${user.email}. Expected: ${user.two_factor_temp}, Received: ${twoFactorDto.unique}, Decoded: ${decodedUnique}`);
        throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_LINK);
      }
    }

    // For login OTP, we don't need to check two_factor_temp as it's stored differently
    if (isManualLoginOTP) {
      this.logger.log(`Login OTP verification for user ${user.email}`);
    }
    if (!user.two_factor_next_verification || user.two_factor_next_verification < new Date()) {
      if (user.two_factor_next_verification) {
        this.logger.warn(`Expired code. Expired on: `, user.two_factor_next_verification);
      }

      throw new BadRequestException(AuthMessages.EXPIRED_VERIFICATION_LINK);
    }
    const compareToken = await bcrypt.compare(twoFactorDto.code, user.two_factor_code);
    if (!compareToken) {
      throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_CODE);
    }

    // Determine the type of verification based on user state and context
    const isLoginOTP = user.two_factor_enabled && user.email_verified_at;
    const isEmailVerification = !user.email_verified_at; // User hasn't verified email yet

    try {
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
      await this.usersService.clearTwoFactorCode(user.user_id);

      // Handle account recovery
      if (isAccountRecovery) {
        this.logger.log(`Reactivating account for user: ${user.email}`);
        await this.usersService.reactivateUser(user.user_id);
        // Refresh user data after reactivation
        const reactivatedUser = await this.usersService.findById(user.user_id);
        if (!reactivatedUser) {
          throw new BadRequestException('Failed to reactivate account');
        }
        user = reactivatedUser;
      } else if (isLoginOTP) {
        // Simple login OTP verification
        await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
      } else if (isEmailVerification) {
        // Email verification only - do NOT automatically enable 2FA
        this.logger.log(`Email verification for user: ${user.email} - 2FA will remain optional`);
        await this.usersService.verifyEmail(user.user_id);
        // Clear the temporary 2FA code since verification is complete
        await this.usersService.clearTempTwoFactorCode(user.user_id);
      } else {
        // Explicit 2FA setup - enable 2FA and verify email if needed
        this.logger.log(`Explicit 2FA setup for user: ${user.email}`);
        await this.usersService.enableTwoFactor(user.user_id, expiresAt);
        this.logger.log('2FA activated for user')
        if (!user.email_verified_at) {
          await this.usersService.verifyEmail(user.user_id);
          this.logger.log('Also verified email');
        }
        await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
      }
    } catch (error) {
      this.logger.error('Failed to process two factor code', error);
    }

    // Ensure user is still valid after all operations
    if (!user) {
      throw new BadRequestException('User verification failed');
    }

    const payload = this.createJwtPayload(user);

    // Get device details for email
    const { ip, country, city, userAgent } = await this.deviceInfoService.getDeviceInfo(req);
    const subject = isAccountRecovery ? EmailSubjects.RECOVER_ACCOUNT : (isLoginOTP ? EmailSubjects.LOGIN_DETAILS : (isEmailVerification ? EmailSubjects.WELCOME_EMAIL : EmailSubjects.TWO_FACTOR_SETUP));
    const message = isAccountRecovery ? AuthMessages.RECOVER_ACCOUNT_MESSAGE : (isLoginOTP ? AuthMessages.LOGIN_NOTIFICATION_MESSAGE : (isEmailVerification ? AuthMessages.WELCOME_MESSAGE : AuthMessages.TWO_FACTOR_ENABLED_MESSAGE));

    // Send login alert email
    this.emailService.sendLoginAlertEmail(
      user.email,
      subject,
      {
        userName: `${user.first_name.trim()} ${user.last_name.trim()}`,
        loginUrl:`${process.env.FRONTEND_URL}${AuthConstants.URL_PATTERNS.LOGIN_PATH}`,
        ip:ip,
        country:country,
        city:city,
        userAgent:userAgent,
        message: message,
      }
    ).catch((error) => {
      console.error('Failed to send login alert email:', error);
      this.logger.error('Failed to send login alert email:', error);
    })

    const accessToken = this.jwtService.sign(payload);

    // Validate that token was generated successfully
    if (!accessToken || accessToken.trim() === '') {
      const tokenError = new Error('JWT token generation failed during 2FA verification');
      ErrorHandler.logError(this.logger, tokenError, 'Authentication token generation failed',
        ErrorHandler.createUserContext(user, 'verify-2fa', { payload }));
      throw tokenError;
    }

    const response = AuthUtils.createAuthResponse(user, accessToken);

    // Determine the appropriate response based on verification type
    let responseMessage: string;
    let userTwoFactorEnabled: boolean;

    if (isAccountRecovery) {
      responseMessage = 'Account successfully reactivated! Welcome back to MACRA services.';
      userTwoFactorEnabled = user.two_factor_enabled; // Preserve existing 2FA status
    } else if (isLoginOTP) {
      responseMessage = AuthMessages.OTP_VERIFIED;
      userTwoFactorEnabled = true; // User already has 2FA enabled
    } else if (isEmailVerification) {
      responseMessage = 'Email verified successfully! Your account is now active.';
      userTwoFactorEnabled = false; // Email verification doesn't enable 2FA
    } else {
      responseMessage = AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_ENABLED, { email: user.email });
      userTwoFactorEnabled = true; // Explicit 2FA setup enables 2FA
    }

    return {
      ...response,
      user: {
        ...response.user,
        two_factor_enabled: userTwoFactorEnabled,
      },
      message: responseMessage
    };
  }
  /**
   * Resend email verification for unverified users
   */
  async resendVerificationEmail(email: string): Promise<{ message: string }> {
    // Input validation
    if (!isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    try {
      const user = await this.usersService.findByEmail(email);

      // Don't reveal if email exists or not for security
      if (!user) {
        return { message: 'If the email exists and is not verified, a verification email has been sent.' };
      }

      // Only send verification email if user hasn't verified their email yet
      if (user.email_verified_at) {
        return { message: 'If the email exists and is not verified, a verification email has been sent.' };
      }

      // Generate new verification code
      await this.generateTwoFactorCode(user.user_id, TwoFactorAction.REGISTER);

      this.logger.log(`Verification email resent to ${email}`);
      return { message: 'If the email exists and is not verified, a verification email has been sent.' };

    } catch (error) {
      this.logger.error(`Failed to resend verification email to ${email}`, error);
      // Don't reveal internal errors to prevent information disclosure
      return { message: 'If the email exists and is not verified, a verification email has been sent.' };
    }
  }





}
