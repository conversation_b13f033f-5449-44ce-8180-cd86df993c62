import {
  Entity,
  Column,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsEmail, Is<PERSON>SO<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { User } from './user.entity';

export enum NotificationType {
  APPLICATION_STATUS = 'application_status',
  EVALUATION_ASSIGNED = 'evaluation_assigned',
  PAYMENT_CONFIRMATION = 'payment_confirmation',
  COMPLAINT_STATUS = 'complaint_status',
  COMPLAINT_RESPONSE = 'complaint_response',
  COMPLAINT_FOLLOWUP = 'complaint_followup',
  BREACH_REPORT_STATUS = 'breach_report_status',
  PAYMENT_DUE = 'payment_due',
  LICENSE_EXPIRY = 'license_expiry',
  SYSTEM_ALERT = 'system_alert',
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  BOUNCED = 'bounced',
  READ = 'read',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum RecipientType {
  CUSTOMER = 'customer',
  STAFF = 'staff',
  ADMIN = 'admin',
  SYSTEM = 'system',
}

@Entity('notifications')
export class Notifications {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  notification_id: string;

  @ApiProperty({
    description: 'Notification type',
    enum: NotificationType,
    example: NotificationType.EMAIL
  })
  @Column({
    type: 'varchar',
    length: 50,
  })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({
    description: 'Notification status',
    enum: NotificationStatus,
    example: NotificationStatus.SENT
  })
  @Column({
    type: 'varchar',
    length: 50,
    default: NotificationStatus.PENDING,
  })
  @IsEnum(NotificationStatus)
  status: NotificationStatus;

  @ApiProperty({
    description: 'Notification priority',
    enum: NotificationPriority,
    example: NotificationPriority.MEDIUM
  })
  @Column({
    type: 'varchar',
    length: 50,
    default: NotificationPriority.MEDIUM,
  })
  @IsEnum(NotificationPriority)
  priority: NotificationPriority;

  @ApiProperty({
    description: 'Recipient type',
    enum: RecipientType,
    example: RecipientType.CUSTOMER
  })
  @Column({
    type: 'varchar',
    length: 50,
  })
  @IsEnum(RecipientType)
  recipient_type: RecipientType;

  @ApiPropertyOptional({
    description: 'Recipient user ID',
    example: 'user-uuid-here'
  })
  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  recipient_id?: string;

  @ApiPropertyOptional({
    description: 'Recipient email address',
    example: '<EMAIL>'
  })
  @Column({ type: 'varchar', nullable: true })
  @IsOptional()
  @IsEmail()
  recipient_email?: string;

  @ApiPropertyOptional({
    description: 'Recipient phone number',
    example: '+************'
  })
  @Column({ type: 'varchar', nullable: true })
  @IsOptional()
  @IsString()
  recipient_phone?: string;

  @ApiProperty({
    description: 'Notification subject/title',
    example: 'Application Status Update'
  })
  @Column({ type: 'varchar', length: 255 })
  @IsString()
  subject: string;

  @ApiProperty({
    description: 'Notification message content',
    example: 'Your application has been approved.'
  })
  @Column({ type: 'text' })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'HTML content for email notifications'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  html_content?: string;

  @ApiPropertyOptional({
    description: 'Entity type this notification relates to',
    example: 'application'
  })
  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @IsString()
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'Entity ID this notification relates to',
    example: 'application-uuid-here'
  })
  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  entity_id?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata as JSON'
  })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsJSON()
  metadata?: any;

  @ApiPropertyOptional({
    description: 'External provider message ID'
  })
  @Column({ type: 'varchar', nullable: true })
  @IsOptional()
  @IsString()
  external_id?: string;

  @ApiPropertyOptional({
    description: 'Error message if notification failed'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  error_message?: string;

  @ApiPropertyOptional({
    description: 'Number of retry attempts'
  })
  @Column({ type: 'int', default: 0 })
  retry_count: number;

  @Column({ type: 'boolean', default: false })
  is_read: boolean;

  @ApiPropertyOptional({
    description: 'When notification was sent'
  })
  @Column({ type: 'timestamp', nullable: true })
  sent_at?: Date;

  @ApiPropertyOptional({
    description: 'When notification was delivered'
  })
  @Column({ type: 'timestamp', nullable: true })
  delivered_at?: Date;

  @Column({ type: 'text', nullable: true })
  action_url?: string;

  @Column({ type: 'timestamp', nullable: true })
  expires_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'timestamp', nullable: true })
  read_at?: Date;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'recipient_id' })
  recipient?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.notification_id) {
      this.notification_id = uuidv4();
    }
  }
}
