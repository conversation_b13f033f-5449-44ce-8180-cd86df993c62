import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsString, ArrayNotEmpty } from 'class-validator';

export class BatchValidateImeiDto {
  @ApiProperty({
    description: 'Array of IMEI numbers to validate',
    example: ['351756051523999', '356938035643809'],
    type: [String],
  })
  @IsArray({ message: 'IMEIs must be an array' })
  @ArrayNotEmpty({ message: 'IMEIs array cannot be empty' })
  @IsString({ each: true, message: 'Each IMEI must be a string' })
  imeis: string[];
}

export class BatchValidationDeviceInfo {
  @ApiProperty({
    description: 'Manufacturer name',
    example: 'Apple Inc.',
  })
  manufacturer: string;

  @ApiProperty({
    description: 'Model name',
    example: 'iPhone 15 Pro',
  })
  model_name: string;

  @ApiProperty({
    description: 'Device type',
    example: 'Mobile Phone',
  })
  device_type: string;

  @ApiProperty({
    description: 'Device serial number',
    example: 'F2LMDRXHFGK1',
  })
  device_serial_number: string;

  @ApiProperty({
    description: 'Type Allocation Code (first 8 digits of IMEI)',
    example: '35175605',
  })
  tac: string;
}

export class BatchValidationResult {
  @ApiProperty({
    description: 'IMEI number that was validated',
    example: '351756051523999',
  })
  imei: string;

  @ApiProperty({
    description: 'Whether the IMEI is valid',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Validation status',
    example: 'exists',
    enum: ['exists', 'valid', 'invalid', 'error'],
  })
  status: string;

  @ApiProperty({
    description: 'Validation message',
    example: 'Device found in database',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp when validation was performed',
    example: '2024-01-15T10:30:00.000Z',
  })
  validatedAt: string;

  @ApiPropertyOptional({
    description: 'Device information if found in database',
    type: BatchValidationDeviceInfo,
  })
  deviceInfo?: BatchValidationDeviceInfo;
}
