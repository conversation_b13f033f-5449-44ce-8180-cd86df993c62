import { DataSource } from 'typeorm';

export const createSeederDataSource = (): DataSource => {
  return new DataSource({
    type: process.env.DB_DRIVER as any || 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'macra_db',
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    entities: ['src/**/*.entity{.ts,.js}'],
    synchronize: false,
    logging: false,
    migrations: ['src/database/migrations/**/*.ts'],
    subscribers: ['src/database/subscribers/**/*.ts'],
  });
};
