import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Device } from 'src/entities/device.entity';
import { CreateDeviceDto } from 'src/dto/standards/create-device.dto';
import { UpdateDeviceDto } from 'src/dto/standards/update-device.dto';
import { DeviceApprovalStatus } from 'src/dto/standards/device-enums';
import { Shortcode } from 'src/entities/shortcode.entity';
import { CreateShortcodeDto } from 'src/dto/shortcodes/create-shortcode.dto';
import { UpdateShortcodeDto } from 'src/dto/shortcodes/update-shortcode.dto';
import { shortCodeNumbering, ShortcodeStatus } from 'src/dto/shortcodes/shortcode-enums';

@Injectable()
export class StandardsService {
  constructor(
    @InjectRepository(Device)
    private readonly deviceRepo: Repository<Device>,
    @InjectRepository(Shortcode)
    private readonly shortcodeRepo: Repository<Shortcode>,
  ) {}



  // Device methods
  async createDevice(dto: CreateDeviceDto, userId?: string): Promise<Device> {
    try {
      // Note: IMEI and device serial number validation removed as these fields are no longer required

      // Prepare device data with defaults
      const deviceData = {
        ...dto,
        approval_status: dto.approval_status || DeviceApprovalStatus.PENDING,
        created_by: dto.created_by || userId, // Use provided created_by or fallback to userId from JWT
      };

      // Note: created_by is now optional and can be null

      const entity = this.deviceRepo.create(deviceData);
      return this.deviceRepo.save(entity);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new ConflictException('Failed to create device. Please check for duplicate IMEI or serial number.');
    }
  }

  async findAllDevices(): Promise<Device[]> {
    return this.deviceRepo.find({
      relations: ['equipment_category', 'creator', 'updater', 'application'],
      order: { created_at: 'DESC' }
    });
  }

  async findOneDevice(id: string): Promise<Device> {
    const entity = await this.deviceRepo.findOne({
      where: { device_id: id },
      relations: ['equipment_category', 'creator', 'updater', 'application']
    });
    if (!entity) throw new NotFoundException(`Device with id ${id} not found`);
    return entity;
  }

  // Note: findDeviceByImei method removed as IMEI field is no longer available

  async updateDevice(id: string, dto: UpdateDeviceDto, userId?: string): Promise<Device> {
    const entity = await this.findOneDevice(id);

    // Note: IMEI and device serial number validation removed as these fields are no longer required

    // Prepare update data with optional updated_by
    const updateData = {
      ...dto,
      updated_by: dto.updated_by || userId, // Use provided updated_by or fallback to userId from JWT
    };

    const updated = this.deviceRepo.merge(entity, updateData);
    return this.deviceRepo.save(updated);
  }

  async removeDevice(id: string): Promise<void> {
    const entity = await this.findOneDevice(id);
    await this.deviceRepo.softRemove(entity);
  }

  //Shortcode methods
  async createShortcode(dto: CreateShortcodeDto, userId?: string): Promise<Shortcode> {
    console.log(`🚀 Creating new shortcode for category: ${dto.category}, length: ${dto.shortcode_length}`);

    try {
      // Validate category and length combination
      console.log('🔍 Validating category and length combination...');
      this.validateCategoryLengthCombination(dto.category, dto.shortcode_length);

      // Generate a shortcode according to numbering plan
      console.log('🎲 Generating shortcode according to numbering plan...');
      const generatedShortcode = await this.generateShortcode(dto.category, dto.shortcode_length);
      console.log(`✅ Generated shortcode: ${generatedShortcode}`);

      // Prepare shortcode data with generated shortcode
      const shortcodeData = {
        ...dto,
        shortcode: generatedShortcode,
        created_by: userId,
      };

      const entity = this.shortcodeRepo.create(shortcodeData);
      console.log('📝 Creating shortcode with data:', shortcodeData);
      const result = await this.shortcodeRepo.save(entity);

      console.log(`✅ Successfully created shortcode: ${generatedShortcode} (ID: ${result.shortcode_id})`);
      return result;

    } catch (error) {
      console.log(`❌ Failed to create shortcode:`, error.message);

      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }

      // Enhanced error messaging based on error type
      if (error.message.includes('numbering plan')) {
        throw new ConflictException(
          'Unable to create shortcode: The requested category and length combination is not supported by the current numbering plan. Please contact MACRA support for available options.'
        );
      }

      if (error.message.includes('available shortcodes')) {
        throw new ConflictException(
          'Unable to create shortcode: All shortcodes in the requested category are currently assigned. Please try a different category or contact MACRA support for assistance.'
        );
      }

      throw new ConflictException(
        'Failed to create shortcode due to an unexpected error. Please verify your request details and try again, or contact MACRA support if the problem persists.'
      );
    }
  }

  /**
   * Validate that the category and length combination is supported
   */
  private validateCategoryLengthCombination(category: string, length: number): void {
    console.log(`🔍 Validating category "${category}" with length ${length}`);

    const numberingPlan = shortCodeNumbering.find(plan => plan.length === length);
    if (!numberingPlan) {
      console.log(`❌ No numbering plan found for length ${length}`);
      throw new BadRequestException(
        `Shortcode length ${length} is not supported. Supported lengths are: ${shortCodeNumbering.map(p => p.length).join(', ')}. Please choose a valid length.`
      );
    }

    const categoryRanges = numberingPlan.categories.filter(cat => cat.category === category);
    if (categoryRanges.length === 0) {
      const availableCategories = [...new Set(numberingPlan.categories.map(cat => cat.category))];
      console.log(`❌ Category "${category}" not found in ${length}-digit plan`);
      throw new BadRequestException(
        `Category "${category}" is not available for ${length}-digit shortcodes. Available categories for ${length}-digit shortcodes are: ${availableCategories.join(', ')}.`
      );
    }

    console.log(`✅ Category and length combination is valid`);
  }

  /**
   * Generate a unique shortcode based on category and length according to numbering plan
   * Priority: 1. Given category & length -> 2. RESERVED in same length -> 3. Same category in next length -> 4. RESERVED in next length
   */
  private async generateShortcode(category: string, preferredLength: number): Promise<string> {
    console.log(`🎯 Starting shortcode generation strategy for category: ${category}, length: ${preferredLength}`);

    // Strategy 1: Try preferred category in preferred length
    console.log(`📍 Strategy 1: Trying ${category} in ${preferredLength}-digit`);
    let shortcode = await this.tryGenerateInCategoryAndLength(category, preferredLength);
    if (shortcode) {
      console.log(`✅ Strategy 1 successful: Generated ${shortcode}`);
      return shortcode;
    }

    // Strategy 2: Fallback to RESERVED category in same length
    console.log(`📍 Strategy 2: Fallback to RESERVED in ${preferredLength}-digit`);
    shortcode = await this.tryGenerateInCategoryAndLength('reserved', preferredLength);
    if (shortcode) {
      console.log(`✅ Strategy 2 successful: Generated ${shortcode} (RESERVED category)`);
      return shortcode;
    }

    // Strategy 3: Try same category in next available length
    const nextLength = preferredLength === 3 ? 4 : 3; // Switch between 3 and 4
    console.log(`📍 Strategy 3: Trying ${category} in ${nextLength}-digit`);
    shortcode = await this.tryGenerateInCategoryAndLength(category, nextLength);
    if (shortcode) {
      console.log(`✅ Strategy 3 successful: Generated ${shortcode} (${nextLength}-digit)`);
      return shortcode;
    }

    // Strategy 4: Final fallback to RESERVED in next length
    console.log(`📍 Strategy 4: Final fallback to RESERVED in ${nextLength}-digit`);
    shortcode = await this.tryGenerateInCategoryAndLength('reserved', nextLength);
    if (shortcode) {
      console.log(`✅ Strategy 4 successful: Generated ${shortcode} (RESERVED, ${nextLength}-digit)`);
      return shortcode;
    }

    console.log('❌ All generation strategies failed');
    throw new ConflictException(
      `No available shortcodes found for category "${category}" or fallback options. All shortcodes in the requested and alternative categories appear to be assigned. Please contact MACRA support to discuss additional options or shortcode reclamation.`
    );
  }

  /**
   * Try to generate a shortcode in a specific category and length
   */
  private async tryGenerateInCategoryAndLength(category: string, length: number): Promise<string | null> {
    // Find the numbering plan for the specified length
    const numberingPlan = shortCodeNumbering.find(plan => plan.length === length);
    if (!numberingPlan) {
      return null; // No numbering plan for this length
    }

    // Find all ranges for the specified category
    const categoryRanges = numberingPlan.categories.filter(cat => cat.category === category);
    if (categoryRanges.length === 0) {
      return null; // No ranges for this category in this length
    }

    // Try random generation first (faster when many codes available)
    const randomShortcode = await this.tryRandomGeneration(categoryRanges);
    if (randomShortcode) return randomShortcode;

    // If random generation fails, try sequential generation
    return this.trySequentialGeneration(categoryRanges);
  }

  /**
   * Try random generation within category ranges
   */
  private async tryRandomGeneration(ranges: Array<{category: string, start: number, end: number}>): Promise<string | null> {
    const maxAttempts = 1000; // Prevent infinite loops
    let attempts = 0;

    while (attempts < maxAttempts) {
      // Pick a random range from available ranges
      const randomRange = ranges[Math.floor(Math.random() * ranges.length)];

      // Generate a random shortcode within this range
      const randomCode = Math.floor(Math.random() * (randomRange.end - randomRange.start + 1)) + randomRange.start;
      const shortcode = randomCode.toString();

      // Check if this shortcode already exists
      const existingShortcode = await this.shortcodeRepo.findOne({
        where: { shortcode }
      });

      if (!existingShortcode) {
        return shortcode;
      }

      attempts++;
    }

    return null; // Random generation failed
  }

  /**
   * Try sequential generation within category ranges
   */
  private async trySequentialGeneration(ranges: Array<{category: string, start: number, end: number}>): Promise<string | null> {
    for (const range of ranges) {
      for (let code = range.start; code <= range.end; code++) {
        const shortcode = code.toString();

        const existingShortcode = await this.shortcodeRepo.findOne({
          where: { shortcode }
        });

        if (!existingShortcode) {
          return shortcode;
        }
      }
    }

    return null; // All codes in ranges are taken
  }



  /**
   * Get the default category for a shortcode based on numbering plan
   */
  private getDefaultCategoryForShortcode(shortcode: string, length: number): string | null {
    const code = parseInt(shortcode, 10);
    if (isNaN(code)) return null;

    // Find the numbering plan for the specified length
    const numberingPlan = shortCodeNumbering.find(plan => plan.length === length);
    if (!numberingPlan) return null;

    // Find which range this shortcode falls into
    for (const categoryRange of numberingPlan.categories) {
      if (code >= categoryRange.start && code <= categoryRange.end) {
        return categoryRange.category;
      }
    }

    return null;
  }

  async findAllShortcodes(): Promise<Shortcode[]> {
    console.log('📋 Retrieving all shortcodes with relations...');

    try {
      const shortcodes = await this.shortcodeRepo.find({
        relations: ['application', 'assignee', 'creator', 'updater'],
        order: { created_at: 'DESC' }
      });

      console.log(`✅ Retrieved ${shortcodes.length} shortcodes`);
      return shortcodes;
    } catch (error) {
      console.log('❌ Failed to retrieve shortcodes:', error.message);
      throw new ConflictException(
        'Unable to retrieve shortcodes at this time. Please try again later or contact MACRA support if the problem persists.'
      );
    }
  }

  async findOneShortcode(id: string): Promise<Shortcode> {
    console.log(`🔍 Looking for shortcode with ID: ${id}`);

    try {
      const entity = await this.shortcodeRepo.findOne({
        where: { shortcode_id: id },
        relations: ['application', 'assignee', 'creator', 'updater']
      });

      if (!entity) {
        console.log(`❌ Shortcode not found: ${id}`);
        throw new NotFoundException(
          `Shortcode with ID "${id}" was not found. Please verify the ID is correct or contact MACRA support if you believe this shortcode should exist.`
        );
      }

      console.log(`✅ Found shortcode: ${entity.shortcode} (Status: ${entity.status})`);
      return entity;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.log('❌ Database error while finding shortcode:', error.message);
      throw new ConflictException(
        'Unable to retrieve shortcode information at this time. Please try again later or contact MACRA support.'
      );
    }
  }

  async findShortcodeByCode(shortcode: string): Promise<Shortcode> {
    const entity = await this.shortcodeRepo.findOne({
      where: { shortcode },
      relations: ['application', 'assignee', 'creator', 'updater']
    });
    if (!entity) throw new NotFoundException(`Shortcode ${shortcode} not found`);
    return entity;
  }

  async findShortcodesByApplication(applicationId: string): Promise<Shortcode[]> {
    console.log(`🔍 Looking for shortcodes for application: ${applicationId}`);

    try {
      const shortcodes = await this.shortcodeRepo.find({
        where: { application_id: applicationId },
        relations: ['application', 'assignee', 'creator', 'updater'],
        order: { created_at: 'DESC' }
      });

      console.log(`✅ Found ${shortcodes.length} shortcodes for application ${applicationId}`);
      return shortcodes;
    } catch (error) {
      console.log('❌ Database error while finding shortcodes by application:', error.message);
      throw new ConflictException(
        'Unable to retrieve shortcode information at this time. Please try again later or contact MACRA support.'
      );
    }
  }

  async createOrUpdateShortcodeForApplication(
    applicationId: string,
    createDto: Omit<CreateShortcodeDto, 'application_id'>,
    userId?: string
  ): Promise<Shortcode> {
    console.log(`🚀 Creating or updating shortcode for application: ${applicationId}`);

    try {
      // Check if shortcode already exists for this application
      const existingShortcodes = await this.findShortcodesByApplication(applicationId);

      if (existingShortcodes.length > 0) {
        // Update existing shortcode
        const existingShortcode = existingShortcodes[0]; // Take the first one
        console.log(`📝 Updating existing shortcode: ${existingShortcode.shortcode_id}`);

        return this.updateShortcode(
          existingShortcode.shortcode_id,
          createDto as any, // Cast to UpdateShortcodeDto
          userId
        );
      } else {
        // Create new shortcode
        console.log(`✨ Creating new shortcode for application: ${applicationId}`);

        const fullCreateDto: CreateShortcodeDto = {
          ...createDto,
          application_id: applicationId
        };

        return this.createShortcode(fullCreateDto, userId);
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      console.log('❌ Error in createOrUpdateShortcodeForApplication:', error.message);
      throw new ConflictException(
        'Unable to save shortcode information at this time. Please try again later or contact MACRA support.'
      );
    }
  }

  async updateShortcode(id: string, dto: UpdateShortcodeDto, userId?: string, userRole?: string): Promise<Shortcode> {
    console.log(`🔄 Updating shortcode ${id} by user ${userId} with role ${userRole}`);

    const entity = await this.findOneShortcode(id);
    console.log(`📋 Found shortcode: ${entity.shortcode} (Status: ${entity.status}, Category: ${entity.category})`);

    // Role-based field restrictions for customer users
    if (userRole === 'customer') {
      console.log('🚫 Customer role detected - checking field restrictions');

      if (dto.shortcode && dto.shortcode !== entity.shortcode) {
        console.log(`❌ Customer attempted to change shortcode from ${entity.shortcode} to ${dto.shortcode}`);
        throw new ForbiddenException(
          'Access denied: Customers cannot modify the shortcode value. Please contact MACRA support if you need to change your shortcode.'
        );
      }

      if (dto.status && dto.status !== entity.status) {
        console.log(`❌ Customer attempted to change status from ${entity.status} to ${dto.status}`);
        throw new ForbiddenException(
          'Access denied: Customers cannot modify shortcode status. Status changes are managed by MACRA administrators based on compliance and regulatory requirements.'
        );
      }

      // Additional customer restrictions
      if (dto.category && dto.category !== entity.category) {
        console.log(`❌ Customer attempted to change category from ${entity.category} to ${dto.category}`);
        throw new ForbiddenException(
          'Access denied: Customers cannot modify shortcode category. Category assignments are determined by MACRA based on the numbering plan and regulatory requirements.'
        );
      }

      console.log('✅ Customer update request passed field restrictions');
    }

    // Check for shortcode conflicts if shortcode is being updated
    if (dto.shortcode && dto.shortcode !== entity.shortcode) {
      console.log(`🔍 Checking for shortcode conflicts: ${dto.shortcode}`);
      const existingShortcode = await this.shortcodeRepo.findOne({
        where: { shortcode: dto.shortcode }
      });
      if (existingShortcode) {
        console.log(`❌ Shortcode conflict detected: ${dto.shortcode} already exists`);
        throw new ConflictException(
          `Shortcode ${dto.shortcode} is already in use. Please choose a different shortcode or contact MACRA support if you believe this is an error.`
        );
      }
      console.log('✅ No shortcode conflicts found');
    }

    // Validate status transitions
    if (dto.status && dto.status !== entity.status) {
      console.log(`🔄 Status change requested: ${entity.status} → ${dto.status}`);
      this.validateStatusTransition(entity.status, dto.status, userRole);
    }

    // Prepare update data with optional updated_by
    const updateData = {
      ...dto,
      updated_by: dto.updated_by || userId,
    };

    // If shortcode is being inactivated, null the assigned_to field and revert to default category
    if (dto.status === ShortcodeStatus.INACTIVE) {
      console.log('🔄 Inactivating shortcode - clearing assignment and reverting category');
      (updateData as any).assigned_to = null;

      // Revert to default category based on numbering plan
      const defaultCategory = this.getDefaultCategoryForShortcode(entity.shortcode, entity.shortcode_length);
      if (defaultCategory) {
        console.log(`📂 Reverting category to default: ${defaultCategory}`);
        updateData.category = defaultCategory;
      }
    }

    // Validate assignment requirements for active status
    if (dto.status === ShortcodeStatus.ACTIVE && !updateData.assigned_to && !entity.assigned_to) {
      console.log('❌ Active shortcode requires assignment');
      throw new BadRequestException(
        'Active shortcodes must be assigned to an organization/ applicant.'
      );
    }

    const updated = this.shortcodeRepo.merge(entity, updateData);
    console.log(`💾 Saving updated shortcode with changes:`, Object.keys(dto));

    try {
      const result = await this.shortcodeRepo.save(updated);
      console.log(`✅ Successfully updated shortcode ${id}: ${result}`);
      return (result);
    } catch (error) {
      console.log(`❌ Failed to save shortcode update:`, error.message);
      if (error instanceof ConflictException || error instanceof ForbiddenException || error instanceof BadRequestException) {
        throw error;
      }
      let msg = 'An error occurred while saving your shortcode application';
      if (error.code === '23505') {
        msg = 'You might be attempting to submit a duplicate shortcode application.'
      }
      if (error.message.includes('violates foreign key constraint')) {
        msg = 'You are attempting to reference a non-existent applicant/ organization.'
      }
      throw new ConflictException(msg);
    }
  }

  /**
   * Validate status transitions based on business rules and user roles
   */
  private validateStatusTransition(currentStatus: string, newStatus: string, userRole?: string): void {
    console.log(`🔍 Validating status transition: ${currentStatus} → ${newStatus} for role: ${userRole}`);

    // Define valid transitions
    const validTransitions = {
      [ShortcodeStatus.INACTIVE]: [ShortcodeStatus.ACTIVE],
      [ShortcodeStatus.ACTIVE]: [ShortcodeStatus.INACTIVE],
    };

    // Check if transition is valid
    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      console.log(`❌ Invalid status transition attempted`);
      throw new BadRequestException(
        `Invalid status transition from "${currentStatus}" to "${newStatus}". Valid transitions from "${currentStatus}" are: ${validTransitions[currentStatus]?.join(', ') || 'none'}.`
      );
    }

    // Additional role-based restrictions (already handled above for customers)
    if (userRole === 'customer') {
      console.log(`❌ Customer role cannot change status`);
      throw new ForbiddenException(
        'Customers cannot change shortcode status. Status changes require MACRA administrator approval to ensure compliance with regulatory requirements.'
      );
    }

    console.log('✅ Status transition validation passed');
  }

  async removeShortcode(id: string, userRole?: string): Promise<void> {
    console.log(`🗑️ Attempting to remove shortcode ${id} by user with role: ${userRole}`);

    // Role-based restrictions for deletion
    if (userRole === 'customer') {
      console.log('❌ Customer attempted to delete shortcode');
      throw new ForbiddenException(
        'Access denied: Customers cannot delete shortcodes. Please contact MACRA support if you need to remove or deactivate a shortcode.'
      );
    }

    try {
      const entity = await this.findOneShortcode(id);

      // Check if shortcode is currently active
      if (entity.status === ShortcodeStatus.ACTIVE) {
        console.log(`⚠️ Attempting to delete active shortcode: ${entity.shortcode}`);
        throw new BadRequestException(
          `Cannot delete active shortcode "${entity.shortcode}". Please deactivate the shortcode first, then try deletion again. Active shortcodes must be properly decommissioned to maintain regulatory compliance.`
        );
      }

      console.log(`🗑️ Soft deleting shortcode: ${entity.shortcode}`);
      await this.shortcodeRepo.softRemove(entity);
      console.log(`✅ Successfully deleted shortcode: ${entity.shortcode}`);

    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException || error instanceof BadRequestException) {
        throw error;
      }
      console.log('❌ Failed to delete shortcode:', error.message);
      throw new ConflictException(
        'Unable to delete shortcode at this time. This may be due to existing references or database constraints. Please contact MACRA support for assistance.'
      );
    }
  }
}

