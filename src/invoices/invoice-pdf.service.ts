import { Injectable } from '@nestjs/common';
import { join } from 'path';
import * as fs from 'fs';

@Injectable()
export class InvoicePdfService {
  
  /**
   * Generate PDF buffer for an invoice
   */
  async generateInvoicePdf(invoice: any, application?: any): Promise<Buffer> {
    try {
      console.log(`📄 Generating PDF for invoice: ${invoice.invoice_number}`);
      
      // Generate HTML content
      const htmlContent = this.generateInvoiceHtml(invoice, application);
      
      // For now, we'll return a simple PDF placeholder
      // In production, you would use puppeteer or similar to generate actual PDF
      const pdfBuffer = await this.generatePdfFromHtml(htmlContent);
      
      console.log(`✅ PDF generated successfully for invoice: ${invoice.invoice_number}`);
      return pdfBuffer;
    } catch (error) {
      console.error(`❌ Error generating PDF for invoice ${invoice.invoice_number}:`, error);
      throw error;
    }
  }

  /**
   * Get MACRA logo as base64 string
   */
  private getMacraLogoBase64(): string {
    try {
      const logoPath = join(__dirname, '../templates/assets/macra-logo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      return `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('⚠️ Could not load MACRA logo:', error.message);
      return '';
    }
  }

  /**
   * Generate HTML content for the invoice
   */
  private generateInvoiceHtml(invoice: any, application?: any): string {
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const formatAmount = (amount: number, currency: string) => {
      return `${currency} ${Number(amount).toLocaleString()}`;
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Calculate totals
    const subtotal = Number(invoice.amount);
    const tax = 0; // Assuming no tax for now
    const total = subtotal + tax;

    // Get MACRA logo
    const logoBase64 = this.getMacraLogoBase64();

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Invoice INV-2025-001</title>
  <style>
    @page {
      size: A4;
      margin: 25mm;
    }

    @media print {
      body {
        margin: 0;
        padding: 0;
        background: white;
      }

      .invoice-box {
        box-shadow: none;
        border: none;
        padding: 0;
        margin: 0;
        width: 100%;
      }

      .totals {
        float: none;
        margin-left: auto;
      }

      .footer {
        page-break-after: avoid;
      }
    }

    :root {
      --primary: #dc2626;
      --light: #f9f9f9;
      --dark: #222;
    }

    body {
      font-family: 'Segoe UI', sans-serif;
      margin: 0;
      padding: 0;
      background: #fff;
      color: var(--dark);
    }

    .invoice-box {
      max-width: 800px;
      margin: auto;
      border: 1px solid #eee;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
      background: white;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .company {
      font-size: 24px;
      font-weight: bold;
      color: var(--primary);
    }

    .invoice-title {
      text-align: right;
    }

    .invoice-title h2 {
      margin: 0;
      color: var(--primary);
    }

    .details-row {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      gap: 40px;
    }

    .bill-to {
      flex: 1;
    }

    .bill-to h4 {
      margin-bottom: 8px;
      color: var(--primary);
    }

    .bill-to p {
      margin: 4px 0;
      font-size: 14px;
    }

    .qr-code {
      text-align: right;
    }

    .qr-code img {
      width: 120px;
      height: 120px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 30px;
      font-size: 14px;
    }

    th, td {
      padding: 10px;
      border: 1px solid #ddd;
    }

    th {
      background: var(--primary);
      color: white;
      text-align: left;
    }

    td.amount {
      text-align: right;
    }

    .totals {
      margin-top: 20px;
      float: right;
      width: 300px;
      font-size: 14px;
    }

    .totals div {
      display: flex;
      justify-content: space-between;
      padding: 6px 0;
    }

    .totals .total {
      font-weight: bold;
      border-top: 2px solid var(--primary);
      margin-top: 8px;
      padding-top: 8px;
    }

    .section {
      margin-top: 10px;
    }

    .section h4 {
      margin-bottom: 10px;
      color: var(--primary);
    }

    .footer {
      margin-top: 60px;
      font-size: 13px;
      text-align: center;
      color: #555;
    }

    @media (max-width: 600px) {
      .header, .details-row {
        flex-direction: column;
        align-items: flex-start;
      }

      .qr-code {
        text-align: left;
        margin-top: 20px;
      }

      .totals {
        float: none;
        margin: auto;
      }
    }
  </style>
</head>
<body>
  <div class="invoice-box">
    <div class="header">
      <div class="company">
        ${logoBase64 ? `
        <div class="logo">
            <img src="${logoBase64}" alt="MACRA Logo" />
        </div>
        ` : `
        <div class="logo-fallback">MACRA</div>
        `}
        <div class="company-info">
            <h1>MACRA</h1>
            <p>Malawi Communications Regulatory Authority</p>
        </div>
    </div>
      <div class="invoice-title">
        <h2>Invoice</h2>
        <p>#${invoice.invoice_number}</p>
        <p class="value">Issue Date: ${formatDate(invoice.issue_date)}</p>
      </div>
    </div>

    <div class="details-row">
      <div class="bill-to">
        <h3>Bill To</h3>
        <p><strong>Payer Name: ${invoice.client?.name || 'N/A'}</strong></p>
        <p>Payer E-mail: ${invoice.client?.email || ''}</p>
        <p>Phone Number: ${invoice.client?.phone || ''}</p>
        <p>Address: ${invoice.client?.address || ''}</p>
      </div>
      <div class="qr-code">
        <img src="https://api.qrserver.com/v1/create-qr-code/?data=https://macra.mw/pay/INV-2025-001&size=120x120" alt="QR Code" />
      </div>
    </div>

    <div class="section">
      <h4>Invoice Details</h4>
      ${application ? `<p><strong>Application:</strong> ${application.application_number || 'N/A'}</p>` : ''}
      <p class="value">Due Date: ${formatDate(invoice.due_date)}</p>
      <p>Status: <span class="status ${invoice.status.toLowerCase()}" style="color: var(--primary); font-weight: bold;">${invoice.status}</span></p>
    </div>

    <table>
      <thead>
        <tr>
          <th>Description</th>
          <th style="text-align:center;">Qty</th>
          <th style="text-align:right;">Unit Price</th>
          <th style="text-align:right;">Total</th>
        </tr>
      </thead>
      <tbody>
        ${invoice.items && invoice.items.length > 0 ? 
          invoice.items.map((item: any) => `
            <tr>
                <td>${item.description}</td>
                <td style="text-align: center;">${item.quantity || 1}</td>
                <td class="amount">${formatAmount(item.unit_price || item.amount || invoice.amount, invoice.currency)}</td>
                <td class="amount">${formatAmount((item.quantity || 1) * (item.unit_price || item.amount || invoice.amount), invoice.currency)}</td>
            </tr>
          `).join('') : `
            <tr>
                <td>${invoice.description}</td>
                <td style="text-align: center;">1</td>
                <td class="amount">${formatAmount(invoice.amount, invoice.currency)}</td>
                <td class="amount">${formatAmount(invoice.amount, invoice.currency)}</td>
            </tr>
          `
        }
      </tbody>
    </table>

    <div class="totals">
      <div><span>Subtotal:</span><span>${formatAmount(subtotal, invoice.currency)}</span></div>
      <div><span>Tax (16.5%):</span><span>${formatAmount(tax, invoice.currency)}</span></div>
      <div class="total"><span>Total:</span><span>${formatAmount(total, invoice.currency)}</span></div>
    </div>

    <!-- Notes -->
    ${invoice.notes ? `
    <div class="section">
      <h4>Notes</h4>
      <p>${invoice.notes}</p>
    </div>
    ` : ''}
        
    <div class="footer">
      <p>MACRA - Malawi Communications Regulatory Authority</p>
      <p>P.O. Box 261, Blantyre, Malawi | www.macra.mw</p>
      <p>Contact: +265 1 871 611 | Email: <EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
  }

  /**
   * Generate PDF from HTML content
   * For now, this returns HTML content that can be viewed in browser
   * TODO: Implement proper PDF generation with puppeteer once installed
   */
  private async generatePdfFromHtml(htmlContent: string): Promise<Buffer> {
    try {
      // Try to use puppeteer if available
      try {
        const puppeteer = require('puppeteer');
        console.log('📄 Using Puppeteer for PDF generation');

        const browser = await puppeteer.launch({
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

        const pdfBuffer = await page.pdf({
          format: 'A4',
          printBackground: true,
          margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
          }
        });

        await browser.close();
        return pdfBuffer;
      } catch (puppeteerError) {
        console.warn('⚠️ Puppeteer not available, falling back to HTML response:', puppeteerError.message);

        // Fallback: Return HTML content that can be viewed/printed in browser
        // Add print styles for better PDF output when user prints from browser
        const htmlWithPrintStyles = htmlContent.replace(
          '</head>',
          `
          <style media="print">
            @page { margin: 0.5in; }
            body { -webkit-print-color-adjust: exact; }
            .no-print { display: none !important; }
          </style>
          </head>`
        );

        return Buffer.from(htmlWithPrintStyles, 'utf-8');
      }
    } catch (error) {
      console.error('❌ Error generating PDF:', error);
      throw error;
    }
  }
}
