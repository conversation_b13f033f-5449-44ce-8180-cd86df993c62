import { DataSource } from 'typeorm';
import { LicenseCategories } from '../entities/license-categories.entity';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';

/**
 * Script to update Type Approval Certificate documents
 * This replaces the generic documents with the correct Type Approval specific documents
 */

const TYPE_APPROVAL_DOCUMENTS = [
  'ETSI Documents',
  'Test Reports from accredited Labs',
  'Technical Specifications',
  'Authorization Letter (Power of Attorney)',
  'Declaration of Conformity',
  'Any Copies of Approval from ITU Region 1'
];

export async function updateTypeApprovalDocuments(dataSource: DataSource): Promise<void> {
  console.log('🔄 Starting Type Approval documents update...');

  const licenseCategoryRepository = dataSource.getRepository(LicenseCategories);
  const documentRepository = dataSource.getRepository(LicenseCategoryDocument);

  try {
    // Find the Type Approval Certificate category
    const typeApprovalCategory = await licenseCategoryRepository.findOne({
      where: { name: 'Type Approval Certificate' }
    });

    if (!typeApprovalCategory) {
      console.error('❌ Type Approval Certificate category not found!');
      return;
    }

    console.log(`✅ Found Type Approval Certificate category: ${typeApprovalCategory.license_category_id}`);

    // Delete existing documents for Type Approval Certificate
    const deleteResult = await documentRepository.delete({
      license_category_id: typeApprovalCategory.license_category_id
    });

    console.log(`🗑️ Deleted ${deleteResult.affected} existing documents`);

    // Insert the correct Type Approval documents
    const documentsToCreate = TYPE_APPROVAL_DOCUMENTS.map(documentName => 
      documentRepository.create({
        license_category_id: typeApprovalCategory.license_category_id,
        name: documentName,
        is_required: true
      })
    );

    const savedDocuments = await documentRepository.save(documentsToCreate);
    console.log(`✅ Created ${savedDocuments.length} new Type Approval documents:`);
    
    savedDocuments.forEach(doc => {
      console.log(`   - ${doc.name}`);
    });

    // Verify the changes
    const verificationDocs = await documentRepository.find({
      where: { license_category_id: typeApprovalCategory.license_category_id },
      order: { name: 'ASC' }
    });

    console.log('\n📋 Final document list for Type Approval Certificate:');
    verificationDocs.forEach(doc => {
      console.log(`   ✓ ${doc.name} (Required: ${doc.is_required})`);
    });

    console.log('\n🎉 Type Approval documents update completed successfully!');

  } catch (error) {
    console.error('❌ Error updating Type Approval documents:', error);
    throw error;
  }
}

// If running this script directly
if (require.main === module) {
  // You would need to set up the DataSource connection here
  console.log('This script should be run through the NestJS application context');
  console.log('Please create a command or endpoint to execute this function');
}
