import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  Join<PERSON><PERSON><PERSON><PERSON>,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';

export enum DocumentType {
  CERTIFICATE_INCORPORATION = 'certificate_incorporation',
  MEMORANDUM_ASSOCIATION = 'memorandum_association',
  SHAREHOLDING_STRUCTURE = 'shareholding_structure',
  BUSINESS_PLAN = 'business_plan',
  FINANCIAL_STATEMENTS = 'financial_statements',
  TECHNICAL_PROPOSAL = 'technical_proposal',
  COVERAGE_PLAN = 'coverage_plan',
  NETWORK_DIAGRAM = 'network_diagram',
  EQUIPMENT_SPECIFICATIONS = 'equipment_specifications',
  INSURANCE_CERTIFICATE = 'insurance_certificate',
  TAX_CLEARANCE = 'tax_clearance',
  AUDITED_ACCOUNTS = 'audited_accounts',
  BANK_STATEMENT = 'bank_statement',
  CV_DOCUMENT = 'cv_document',
  PROOF_OF_PAYMENT = 'proof_of_payment',
  OTHER = 'other',
}

@Entity('documents')
export class Documents {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  document_id: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  document_type: string;

  @Column({ type: 'varchar'})
  file_name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  entity_type: string;

  @Column({ type: 'uuid', nullable:true })
  entity_id: string;
  
  @Column({ type: 'text' })
  file_path: string;

  @Column({ type: 'int', nullable: true })
  file_size: number;

  @Column({ type: 'varchar', length: 100 })
  mime_type: string;

  @Column({ type: 'boolean', default: false })
  is_required: boolean;

  @Column({ type: 'uuid', nullable: true })
  approved_by?: string;

  @Column({ type: 'timestamp', nullable: true })
  approved_at?: Date;

  @Column({ type: 'text', nullable: true })
  comment?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable:true })
  created_by: string;

  @Column({ type: 'varchar', default:'pending', nullable:true })
  status: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;


  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approver?: User;

  @BeforeInsert()
  generateId() {
    if (!this.document_id) {
      this.document_id = uuidv4();
    }
  }
}
