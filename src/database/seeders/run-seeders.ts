#!/usr/bin/env node

import 'reflect-metadata';
import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { createSeederDataSource } from './seeder.config';
import MainSeeder from './main.seeder';
import FixAddressForeignKeysSeeder from './fix-address-foreign-keys.seeder';

// Load environment variables
config();

async function runSeeding() {

  let dataSource: DataSource | undefined;

  try {
    // Create data source
    dataSource = createSeederDataSource();

    // Initialize the data source
    console.log('📡 Connecting to database...');
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // First, fix any foreign key constraint issues
    console.log('\n🔧 Fixing foreign key constraints...');
    const fixForeignKeysSeeder = new FixAddressForeignKeysSeeder();
    await fixForeignKeysSeeder.run(dataSource);

    // Run the main seeder
    console.log('\n🚀 Running seeders...');
    const mainSeeder = new MainSeeder();
    await mainSeeder.run(dataSource);

    console.log('\n🎉 All seeders completed successfully!');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    if (dataSource && dataSource.isInitialized) {
      console.log('\n📡 Closing database connection...');
      await dataSource.destroy();
      console.log('✅ Database connection closed');
    }
  }
}

// Run the seeding
runSeeding();

export { runSeeding };
