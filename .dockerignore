# ===================================================================
# MACRA Digital Portal Backend - Docker Ignore File
# ===================================================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Build outputs
dist/
build/
coverage/
*.tsbuildinfo

# Environment files
.env.local
.env.development
.env.test
.env.template
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# Test files
test/
*.test.ts
*.spec.ts
__tests__/
coverage/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Database files
*.sqlite
*.db

# Temporary files
tmp/
temp/

# Development tools
.eslintrc*
.prettierrc*
jest.config.*

# Scripts (exclude from runtime)
#scripts/

# Uploads directory (will be created in container)
uploads/

# Database seeders and migrations (not needed in runtime)
src/database/migrations/

# Backup files
*.backup
*.bak
