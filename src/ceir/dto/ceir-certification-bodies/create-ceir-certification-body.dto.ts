import {
  IsS<PERSON>,
  IsO<PERSON>al,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
  MaxLength,
  IsArray,
  IsIn,
  IsDateString,
  IsUrl,
  IsEmail,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { 
  CERTIFICATION_BODY_TYPES, 
  ACCREDITATION_STATUSES, 
  CERTIFICATION_SCOPES 
} from '../../entities/ceir-certification-bodies.entity';

export class CreateCeirCertificationBodyDto {
  @ApiProperty({
    description: 'Official name of the certification body',
    example: 'SGS Telecommunications Testing Laboratory',
    maxLength: 255
  })
  @IsString({ message: 'Organization name must be a string' })
  @MaxLength(255, { message: 'Organization name must not exceed 255 characters' })
  @IsNotEmpty({ message: 'Organization name is required' })
  organization_name: string;

  @ApiProperty({
    description: 'Short name or acronym',
    example: 'SGS-TTL',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Short name must be a string' })
  @MaxLength(100, { message: 'Short name must not exceed 100 characters' })
  short_name?: string;

  @ApiProperty({
    description: 'Type of certification body',
    enum: CERTIFICATION_BODY_TYPES,
    example: 'accredited_lab'
  })
  @IsString({ message: 'Body type must be a string' })
  @IsIn(CERTIFICATION_BODY_TYPES, { message: 'Body type must be one of the valid certification body types' })
  @IsNotEmpty({ message: 'Body type is required' })
  body_type: string;

  @ApiProperty({
    description: 'Unique registration or license number',
    example: 'CB-2024-001',
    maxLength: 100
  })
  @IsString({ message: 'Registration number must be a string' })
  @MaxLength(100, { message: 'Registration number must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Registration number is required' })
  registration_number: string;

  @ApiProperty({
    description: 'Country where the certification body is located',
    example: 'South Africa',
    maxLength: 100
  })
  @IsString({ message: 'Country must be a string' })
  @MaxLength(100, { message: 'Country must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Country is required' })
  country: string;

  @ApiProperty({
    description: 'Address ID reference',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Address ID must be a valid UUID' })
  address_id?: string;

  @ApiProperty({
    description: 'Contact information ID reference',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Contact ID must be a valid UUID' })
  contact_id?: string;

  @ApiProperty({
    description: 'Primary contact email',
    example: '<EMAIL>',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsEmail({}, { message: 'Primary email must be a valid email address' })
  @MaxLength(255, { message: 'Primary email must not exceed 255 characters' })
  primary_email?: string;

  @ApiProperty({
    description: 'Primary contact phone number',
    example: '+27-11-681-2500',
    maxLength: 50,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Primary phone must be a string' })
  @MaxLength(50, { message: 'Primary phone must not exceed 50 characters' })
  primary_phone?: string;

  @ApiProperty({
    description: 'Official website URL',
    example: 'https://www.sgs.com/telecommunications',
    maxLength: 500,
    required: false
  })
  @IsOptional()
  @IsUrl({}, { message: 'Website URL must be a valid URL' })
  @MaxLength(500, { message: 'Website URL must not exceed 500 characters' })
  website_url?: string;

  @ApiProperty({
    description: 'Accreditation body that accredited this organization',
    example: 'SANAS (South African National Accreditation System)',
    maxLength: 255,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Accreditation body must be a string' })
  @MaxLength(255, { message: 'Accreditation body must not exceed 255 characters' })
  accreditation_body?: string;

  @ApiProperty({
    description: 'Accreditation number',
    example: 'T0001',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Accreditation number must be a string' })
  @MaxLength(100, { message: 'Accreditation number must not exceed 100 characters' })
  accreditation_number?: string;

  @ApiProperty({
    description: 'Date when accreditation was granted',
    example: '2020-01-15',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Accreditation date must be a valid ISO 8601 date string' })
  accreditation_date?: string;

  @ApiProperty({
    description: 'Date when accreditation expires',
    example: '2025-01-15',
    required: false
  })
  @IsOptional()
  @IsDateString({}, { message: 'Accreditation expiry date must be a valid ISO 8601 date string' })
  accreditation_expiry_date?: string;

  @ApiProperty({
    description: 'Current accreditation status',
    enum: ACCREDITATION_STATUSES,
    example: 'active',
    default: 'active'
  })
  @IsString({ message: 'Accreditation status must be a string' })
  @IsIn(ACCREDITATION_STATUSES, { message: 'Accreditation status must be one of the valid statuses' })
  accreditation_status: string = 'active';

  @ApiProperty({
    description: 'Certification scopes this body is authorized for',
    type: 'array',
    items: { enum: [...CERTIFICATION_SCOPES] },
    example: ['rf_testing', 'sar_testing', 'emc_testing']
  })
  @IsArray({ message: 'Certification scopes must be an array' })
  @IsString({ each: true, message: 'Each certification scope must be a string' })
  @IsIn(CERTIFICATION_SCOPES, { each: true, message: 'Each certification scope must be a valid scope' })
  @IsNotEmpty({ message: 'At least one certification scope is required' })
  certification_scopes: string[];

  @ApiProperty({
    description: 'Equipment categories this body can certify',
    type: 'array',
    example: ['mobile_phone', 'smartphone', 'tablet', 'modem'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Authorized equipment categories must be an array' })
  @IsString({ each: true, message: 'Each equipment category must be a string' })
  authorized_equipment_categories?: string[];

  @ApiProperty({
    description: 'Technical standards this body is competent to test against',
    type: 'array',
    example: ['3GPP TS 51.010', 'ETSI EN 301 511', 'IEC 62209-1'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Competent standards must be an array' })
  @IsString({ each: true, message: 'Each standard must be a string' })
  competent_standards?: string[];

  @ApiProperty({
    description: 'Frequency bands this body can test',
    type: 'array',
    example: ['GSM 900', 'GSM 1800', 'UMTS 2100', 'LTE 800'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Authorized frequency bands must be an array' })
  @IsString({ each: true, message: 'Each frequency band must be a string' })
  authorized_frequency_bands?: string[];

  @ApiProperty({
    description: 'Whether this body is recognized by MACRA',
    example: true,
    default: false
  })
  @IsBoolean({ message: 'Is MACRA recognized must be a boolean' })
  is_macra_recognized: boolean = false;

  @ApiProperty({
    description: 'Whether this body can issue CEIR certificates',
    example: true,
    default: false
  })
  @IsBoolean({ message: 'Can issue CEIR certificates must be a boolean' })
  can_issue_ceir_certificates: boolean = false;

  @ApiProperty({
    description: 'Whether this certification body is currently active',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  is_active: boolean = true;

  @ApiProperty({
    description: 'Additional notes or comments',
    example: 'Specialized in mobile device testing with state-of-the-art facilities',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;

  @ApiProperty({
    description: 'User ID who is creating this certification body (optional, can be extracted from JWT)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided' })
  created_by?: string;
}
