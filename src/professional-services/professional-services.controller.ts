import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { ProfessionalServicesService } from './professional-services.service';
import { CreateProfessionalServicesDto } from '../dto/professional-services/create-professional-services.dto';
import { UpdateProfessionalServicesDto } from '../dto/professional-services/update-professional-services.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { ProfessionalServices } from '../entities/professional-services.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Professional Services')
@Controller('professional-services')
export class ProfessionalServicesController {
  constructor(private readonly professionalServicesService: ProfessionalServicesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new professional services record' })
  @ApiBody({ type: CreateProfessionalServicesDto, description: 'Create professional services DTO' })
  @ApiResponse({ status: 201, description: 'Professional services created successfully', type: ProfessionalServices })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  create(@Body() createDto: CreateProfessionalServicesDto, @Request() req: any) {
    return this.professionalServicesService.create(createDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all professional services' })
  @ApiResponse({ status: 200, description: 'List of professional services', type: [ProfessionalServices] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findAll() {
    return this.professionalServicesService.findAll();
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get professional services by application ID' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiResponse({ status: 200, description: 'Professional services found', type: ProfessionalServices })
  @ApiResponse({ status: 404, description: 'Professional services not found' })
  @ApiResponse({ status: 400, description: 'Invalid application ID format' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  async findByApplication(@Param('applicationId') applicationId: string) {
    try {
      const result = await this.professionalServicesService.findByApplication(applicationId);

      // Handle null result - return 404 instead of null
      if (result === null) {
        throw new NotFoundException(`Professional services not found for application ${applicationId}`);
      }

      return result;
    } catch (error) {
      console.error(`Error in findByApplication controller for ${applicationId}:`, error);
      throw error;
    }
  }

  @Post('application/:applicationId')
  @ApiOperation({ summary: 'Create or update professional services for application' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiBody({ type: CreateProfessionalServicesDto, description: 'Professional services data (without application_id)' })
  @ApiResponse({ status: 200, description: 'Professional services created or updated', type: ProfessionalServices })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  createOrUpdateForApplication(
    @Param('applicationId') applicationId: string, 
    @Body() createDto: Omit<CreateProfessionalServicesDto, 'application_id'>, 
    @Request() req: any
  ) {
    return this.professionalServicesService.createOrUpdate(applicationId, createDto, req.user.userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get professional services by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'Professional services UUID' })
  @ApiResponse({ status: 200, description: 'Professional services found', type: ProfessionalServices })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findOne(@Param('id') id: string) {
    return this.professionalServicesService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update professional services by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiBody({ type: UpdateProfessionalServicesDto, description: 'Update professional services DTO' })
  @ApiResponse({ status: 200, description: 'Professional services updated', type: ProfessionalServices })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  update(@Param('id') id: string, @Body() updateDto: UpdateProfessionalServicesDto, @Request() req: any) {
    return this.professionalServicesService.update(id, updateDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete professional services by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiResponse({ status: 204, description: 'Professional services deleted' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  remove(@Param('id') id: string) {
    return this.professionalServicesService.softDelete(id);
  }
}
