import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Licenses, LicenseStatus } from '../entities/licenses.entity';
import { Applications } from '../entities/applications.entity';
import { CreateLicenseDto } from '../dto/licenses/create-license.dto';
import { UpdateLicenseDto } from '../dto/licenses/update-license.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { PDFService } from '../common/services/pdf.service';
import { VerificationService } from '../common/services/verification.service';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class LicensesService {
  private readonly logger = new Logger(LicensesService.name);

  constructor(
    @InjectRepository(Licenses)
    private licensesRepository: Repository<Licenses>,
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    private pdfService: PDFService,
    private verificationService: VerificationService,
  ) {
    // Register Handlebars helpers when service is initialized
    this.pdfService.registerHelpers();
  }

  /**
   * Get MACRA logo as base64 data URL
   */
  private getMacraLogoDataUrl(): string {
    try {
      const logoPath = join(process.cwd(), 'src', 'templates', 'assets', 'macra-logo.png');
      const logoBuffer = readFileSync(logoPath);
      const base64Logo = logoBuffer.toString('base64');
      return `data:image/png;base64,${base64Logo}`;
    } catch (error) {
      this.logger.warn('Failed to load MACRA logo, using fallback', error);
      // Return a fallback SVG logo as data URL
      const fallbackSvg = `<svg width="80" height="80" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="45" stroke="#d32f2f" stroke-width="4" fill="white"/>
        <text x="50" y="55" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#d32f2f">MACRA</text>
      </svg>`;
      return `data:image/svg+xml;base64,${Buffer.from(fallbackSvg).toString('base64')}`;
    }
  }

  private readonly paginateConfig: PaginateConfig<Licenses> = {
    sortableColumns: ['license_number', 'issue_date', 'expiry_date', 'status', 'created_at'],
    searchableColumns: ['license_number'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['application', 'application.applicant', 'application.license_category', 'issuer', 'creator', 'updater'],
  };

  async findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<PaginatedResult<Licenses>> {
    // Check if user is a customer (has customer role)
    const isCustomer = userRoles?.includes('customer');

    // If user is a customer, filter by their licenses only
    if (isCustomer && userId) {
      // Create a custom query builder to filter by customer's applications
      const queryBuilder = this.licensesRepository
        .createQueryBuilder('license')
        .leftJoinAndSelect('license.application', 'application')
        .leftJoinAndSelect('application.applicant', 'applicant')
        .leftJoinAndSelect('application.license_category', 'license_category')
        .leftJoinAndSelect('license.issuer', 'issuer')
        .leftJoinAndSelect('license.creator', 'creator')
        .leftJoinAndSelect('license.updater', 'updater')
        .where('application.created_by = :userId', { userId });

      // Apply search filter if provided
      if (query.search) {
        queryBuilder.andWhere('license.license_number ILIKE :search', {
          search: `%${query.search}%`
        });
      }

      // Apply status filter if provided
      if (query.filter?.status) {
        queryBuilder.andWhere('license.status = :status', {
          status: query.filter.status
        });
      }

      // Apply license type filter if provided
      if (query.filter?.licenseType) {
        queryBuilder.andWhere('application.license_category.license_type_id = :licenseType', {
          licenseType: query.filter.licenseType
        });
      }

      // Apply date range filter if provided
      if (query.filter?.dateRange) {
        const now = new Date();
        let startDate: Date | null = null;
        let endDate: Date | null = null;
        const dateRangeValue = Array.isArray(query.filter.dateRange) ? query.filter.dateRange[0] : query.filter.dateRange;

        switch (dateRangeValue) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            break;
          case 'week':
            const weekStart = new Date(now);
            weekStart.setDate(now.getDate() - now.getDay());
            startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 7);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
          case 'quarter':
            const quarterStart = Math.floor(now.getMonth() / 3) * 3;
            startDate = new Date(now.getFullYear(), quarterStart, 1);
            endDate = new Date(now.getFullYear(), quarterStart + 3, 1);
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear() + 1, 0, 1);
            break;
          case 'expiring_30':
            startDate = now;
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 30);
            queryBuilder.andWhere('license.expiry_date BETWEEN :startDate AND :endDate', {
              startDate: startDate.toISOString().split('T')[0],
              endDate: endDate.toISOString().split('T')[0]
            });
            break;
          case 'expiring_90':
            startDate = now;
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 90);
            queryBuilder.andWhere('license.expiry_date BETWEEN :startDate AND :endDate', {
              startDate: startDate.toISOString().split('T')[0],
              endDate: endDate.toISOString().split('T')[0]
            });
            break;
        }

        // Apply date range filter for issue date (except for expiring filters)
        if (startDate && endDate && !dateRangeValue.startsWith('expiring_')) {
          queryBuilder.andWhere('license.issue_date BETWEEN :startDate AND :endDate', {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
          });
        }
      }

      // Apply sorting
      const sortByField = Array.isArray(query.sortBy) && query.sortBy.length > 0 ? query.sortBy[0] : 'created_at';
      const sortOrderField = Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] : 'DESC';
      const sortOrder = (typeof sortOrderField === 'string' ? sortOrderField.toUpperCase() : 'DESC') as 'ASC' | 'DESC';

      // Validate sortBy column to prevent SQL injection
      const allowedSortColumns = ['license_number', 'issue_date', 'expiry_date', 'status', 'created_at'];
      const safeSortBy = typeof sortByField === 'string' && allowedSortColumns.includes(sortByField) ? sortByField : 'created_at';

      queryBuilder.orderBy(`license.${safeSortBy}`, sortOrder);

      // Apply pagination
      const page = query.page || 1;
      const limit = Math.min(query.limit || 10, 100);
      const offset = (page - 1) * limit;

      queryBuilder.skip(offset).take(limit);

      // Get results and count
      const [data, totalItems] = await queryBuilder.getManyAndCount();

      // Build pagination response
      const totalPages = Math.ceil(totalItems / limit);
      const result = {
        data,
        meta: {
          itemsPerPage: limit,
          totalItems,
          currentPage: page,
          totalPages,
          sortBy: [[safeSortBy, sortOrder]] as [string, string][],
          searchBy: ['license_number'],
          search: query.search || '',
          filter: query.filter || {},
        },
        links: {
          first: '',
          previous: '',
          current: '',
          next: '',
          last: '',
        },
      };

      return PaginationTransformer.transform<Licenses>(result);
    }

    // For staff users, show all licenses
    const queryBuilder = this.licensesRepository
      .createQueryBuilder('license')
      .leftJoinAndSelect('license.application', 'application')
      .leftJoinAndSelect('application.applicant', 'applicant')
      .leftJoinAndSelect('application.license_category', 'license_category')
      .leftJoinAndSelect('license.issuer', 'issuer')
      .leftJoinAndSelect('license.creator', 'creator')
      .leftJoinAndSelect('license.updater', 'updater');

    // Apply search filter if provided
    if (query.search) {
      queryBuilder.andWhere('license.license_number ILIKE :search', {
        search: `%${query.search}%`
      });
    }

    // Apply status filter if provided
    if (query.filter?.status) {
      queryBuilder.andWhere('license.status = :status', {
        status: query.filter.status
      });
    }

    // Apply date range filter if provided
    if (query.filter?.dateRange) {
      const now = new Date();
      let startDate: Date | null = null;
      let endDate: Date | null = null;
      const dateRangeValue = Array.isArray(query.filter.dateRange) ? query.filter.dateRange[0] : query.filter.dateRange;

      switch (dateRangeValue) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
          endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 7);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
          break;
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          endDate = new Date(now.getFullYear(), quarterStart + 3, 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          endDate = new Date(now.getFullYear() + 1, 0, 1);
          break;
        case 'expiring_30':
          startDate = now;
          endDate = new Date(now);
          endDate.setDate(now.getDate() + 30);
          queryBuilder.andWhere('license.expiry_date BETWEEN :startDate AND :endDate', {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
          });
          break;
        case 'expiring_90':
          startDate = now;
          endDate = new Date(now);
          endDate.setDate(now.getDate() + 90);
          queryBuilder.andWhere('license.expiry_date BETWEEN :startDate AND :endDate', {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
          });
          break;
      }

      // Apply date range filter for issue date (except for expiring filters)
      if (startDate && endDate && !dateRangeValue.startsWith('expiring_')) {
        queryBuilder.andWhere('license.issue_date BETWEEN :startDate AND :endDate', {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0]
        });
      }
    }

    // Apply sorting
    const sortByField = Array.isArray(query.sortBy) && query.sortBy.length > 0 ? query.sortBy[0] : 'created_at';
    const sortOrderField = Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] : 'DESC';
    const sortOrder = (typeof sortOrderField === 'string' ? sortOrderField.toUpperCase() : 'DESC') as 'ASC' | 'DESC';

    // Validate sortBy column to prevent SQL injection
    const allowedSortColumns = ['license_number', 'issue_date', 'expiry_date', 'status', 'created_at'];
    const safeSortBy = typeof sortByField === 'string' && allowedSortColumns.includes(sortByField) ? sortByField : 'created_at';

    queryBuilder.orderBy(`license.${safeSortBy}`, sortOrder);

    // Apply pagination
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    // Get results and count
    const [data, totalItems] = await queryBuilder.getManyAndCount();

    // Build pagination response
    const totalPages = Math.ceil(totalItems / limit);
    const result = {
      data,
      meta: {
        itemsPerPage: limit,
        totalItems,
        currentPage: page,
        totalPages,
        sortBy: [[safeSortBy, sortOrder]] as [string, string][],
        searchBy: ['license_number'],
        search: query.search || '',
        filter: query.filter || {},
      },
      links: {
        first: '',
        previous: '',
        current: '',
        next: '',
        last: '',
      },
    };

    return PaginationTransformer.transform<Licenses>(result);
  }

  async findOne(id: string): Promise<Licenses> {
    const license = await this.licensesRepository.findOne({
      where: { license_id: id },
      relations: [
        'application',
        'application.applicant',
        'issuer',
        'creator',
        'updater'
      ],
    });

    if (!license) {
      throw new NotFoundException(`Licence with ID ${id} not found`);
    }

    return license;
  }

  async findByLicenseNumber(licenseNumber: string): Promise<Licenses> {
    const license = await this.licensesRepository.findOne({
      where: { license_number: licenseNumber },
      relations: [
        'application',
        'application.applicant',
        'issuer',
        'creator',
        'updater'
      ],
    });

    if (!license) {
      throw new NotFoundException(`Licence with number ${licenseNumber} not found`);
    }

    return license;
  }

  async create(createLicenseDto: CreateLicenseDto, createdBy: string): Promise<Licenses> {
    // Check if licence number already exists
    const existingLicense = await this.licensesRepository.findOne({
      where: { license_number: createLicenseDto.license_number },
    });

    if (existingLicense) {
      throw new ConflictException(`Licence with number ${createLicenseDto.license_number} already exists`);
    }

    // If application_id is provided and expiry_date is not calculated properly,
    // calculate expiry date based on license category validity
    let finalCreateDto = { ...createLicenseDto };

    if (createLicenseDto.application_id) {
      // Get the application with license category to calculate proper expiry date
      const application = await this.applicationsRepository.findOne({
        where: { application_id: createLicenseDto.application_id },
        relations: ['license_category', 'license_category.license_type'],
      });

      if (application && application.license_category && application.license_category.validity) {
        // Calculate expiry date based on license category validity
        const issueDate = new Date(createLicenseDto.issue_date);
        const expiryDate = new Date(issueDate);
        expiryDate.setFullYear(issueDate.getFullYear() + application.license_category.validity);

        finalCreateDto.expiry_date = expiryDate.toISOString().split('T')[0];

        // Set code from license type if available
        if (application.license_category.license_type && application.license_category.license_type.code) {
          finalCreateDto.code = application.license_category.license_type.code;
        }
        // Set code from license type if available
        if (application.license_category && application.license_category.name) {
          finalCreateDto.description = application.license_category.name;
        }
      }
    }

    const license = this.licensesRepository.create({
      ...finalCreateDto,
      created_by: createdBy,
    });

    const savedLicense = await this.licensesRepository.save(license);
    this.logger.log(`License created successfully: ${savedLicense.license_number}`);

    return this.findOne(savedLicense.license_id);
  }

  /**
   * Generate a unique license number in format LIC-YYYY-MM-NNN
   */
  private async generateLicenseNumber(): Promise<string> {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');

    // Find the highest sequence number for this year and month
    const prefix = `LIC-${year}-${month}-`;
    const existingLicenses = await this.licensesRepository
      .createQueryBuilder('license')
      .where('license.license_number LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('license.license_number', 'DESC')
      .limit(1)
      .getOne();

    let sequenceNumber = 1;
    if (existingLicenses) {
      const lastNumber = existingLicenses.license_number.split('-').pop();
      sequenceNumber = parseInt(lastNumber || '0', 10) + 1;
    }

    return `${prefix}${String(sequenceNumber).padStart(3, '0')}`;
  }

  /**
   * Create a license from an approved application
   */
  async createLicenseFromApplication(applicationId: string, approvedBy: string): Promise<Licenses> {
    // Get the application with all necessary relations
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations:  ['applicant', 'license_category', 'license_category.license_type', 'creator'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    if (!application.license_category) {
      throw new NotFoundException(`License category not found for application ${applicationId}`);
    }

    // Generate license number
    const licenseNumber = await this.generateLicenseNumber();

    // Calculate license validity dates using license category validity
    const issueDate = new Date();
    const expiryDate = new Date();
    const validityYears = application.license_category.validity || 1; // Default to 1 year if not specified
    expiryDate.setFullYear(issueDate.getFullYear() + validityYears);

    // Create license data from application
    const licenseData: CreateLicenseDto = {
      license_number: licenseNumber,
      application_id: applicationId,
      description: application.license_category.name,
      status: LicenseStatus.ACTIVE,
      code: application.license_category.license_type?.code ?? '',
      issue_date: issueDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      expiry_date: expiryDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      issued_by: approvedBy,
      conditions: 'Standard license conditions apply.',
    };

    try{
      // Create the license using the existing create method
      return await this.create(licenseData, approvedBy);
    } catch (error) {
      throw error;
    }
  }

  async update(id: string, updateLicenseDto: UpdateLicenseDto, updatedBy: string): Promise<Licenses> {
    const license = await this.findOne(id);

    // Check if license number is being updated and if it conflicts
    if (updateLicenseDto.license_number && updateLicenseDto.license_number !== license.license_number) {
      const existingLicense = await this.licensesRepository.findOne({
        where: { license_number: updateLicenseDto.license_number },
      });

      if (existingLicense) {
        throw new ConflictException(`Licence with number ${updateLicenseDto.license_number} already exists`);
      }
    }

    await this.licensesRepository.update(id, {
      ...updateLicenseDto,
      updated_by: updatedBy,
    });

    this.logger.log(`License updated successfully: ${license.license_number}`);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const license = await this.findOne(id);
    await this.licensesRepository.softDelete(id);
    this.logger.log(`License deleted successfully: ${license.license_number}`);
  }

  /**
   * Clear PDF template cache
   */
  clearTemplateCache(): void {
    this.pdfService.clearTemplateCache();
  }

  /**
   * Generate PDF for a license based on its type
   */
  async generateLicensePDF(licenseId: string): Promise<Buffer> {
    const license = await this.findOne(licenseId);
    
    // Generate verification data
    const verificationData = this.verificationService.generateQRCodeData(
      license.license_id,
      license.license_number
    );

    // Determine template based on license type and category
    let template = 'standards-licence'; // default
    if (license.code === 'postal_services') {
      template = 'postal-licence';
    } else if (license.code === 'standards_compliance') {
      // Check the license category name to distinguish between different types
      const categoryName = license.application?.license_category?.name?.toLowerCase();

      if (categoryName?.includes('short code') || categoryName?.includes('shortcode')) {
        template = 'short-code';
      } else {
        // For type approval certificates and other standards compliance
        template = 'standards-licence';
      }
    }

    // Prepare data for template
    const templateData = {
      licenseNumber: license.license_number,
      status: license.status,
      issueDate: license.issue_date,
      expiryDate: license.expiry_date,
      issuer: "Daud Suleman",
      conditions: license.conditions || `Authorized by the Malawi Communications Regulatory Authority (MACRA) for use in Malawi under section 6(2)(k) of the Communications Act, 2016. Subject to prevailing regulations and attached general operating conditions.`,
      code: license.code, // For short codes
      verificationUrl: verificationData.verificationUrl,
      verificationCode: verificationData.verificationCode,
      macraLogoDataUrl: this.getMacraLogoDataUrl(), // Add MACRA logo
    };

    // Add categories for postal services (if applicable)
    if (template === 'postal-licence') {
      // You would fetch license categories here based on the license
      // For now, we'll use a placeholder
      templateData['categories'] = [
        {
          name: 'Domestic Mail Services',
          description: 'Collection, processing, and delivery of domestic mail',
          authorizes: 'Provision of domestic postal services within Malawi'
        }
      ];
    }

    // Add category and additional data for short codes (if applicable)
    if (template === 'short-code') {
      templateData['category'] = {
        name: 'Short Code Allocation',
        description: 'Allocation of short codes for SMS and USSD services',
        authorizes: 'Use of allocated short codes for commercial services'
      };
      templateData['serviceDescription'] = license.application?.license_category?.description || 'SMS and USSD services using allocated short code';

      // Add applicant information for short code letters
      if (license.application?.applicant) {
        templateData['applicant'] = license.application.applicant;
      }
    }

    const filename = `${license.license_number}_license.pdf`;

    return this.pdfService.generatePDF({
      template,
      data: templateData,
      filename,
    });
  }

  /**
   * Get license statistics
   */
  async getLicenseStats(): Promise<any> {
    const stats = await this.licensesRepository
      .createQueryBuilder('license')
      .select('license.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('license.status')
      .getRawMany();

    return stats.reduce((acc, stat) => {
      acc[stat.status] = parseInt(stat.count);
      return acc;
    }, {});
  }

  /**
   * Get licenses by applicant
   */
  async findByApplicationId(applicationId: string): Promise<Licenses[]> {
    return this.licensesRepository.find({
      where: { application_id: applicationId },
      relations: ['application','application.applicant', 'issuer'],
      order: { created_at: 'DESC' },
    });
  }

  /**
   * Get licenses expiring soon
   */
  async findExpiringSoon(days: number = 30): Promise<Licenses[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.licensesRepository
      .createQueryBuilder('license')
      .where('license.expiry_date <= :futureDate', { futureDate })
      .andWhere('license.expiry_date >= :today', { today: new Date() })
      .andWhere('license.status = :status', { status: LicenseStatus.ACTIVE })
      .orderBy('license.expiry_date', 'ASC')
      .getMany();
  }
}
