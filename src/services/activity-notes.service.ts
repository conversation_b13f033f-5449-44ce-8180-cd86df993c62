import { Injectable, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, FindOneOptions } from 'typeorm';
import { ActivityNote, ActivityNoteStatus } from '../entities/activity-notes.entity';
import { CreateActivityNoteDto, UpdateActivityNoteDto, ActivityNoteQueryDto } from '../dto/activity-notes.dto';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { ApplicationsService } from '../applications/applications.service';

@Injectable()
export class ActivityNotesService {
  constructor(
    @InjectRepository(ActivityNote)
    private activityNotesRepository: Repository<ActivityNote>,
    @Inject(forwardRef(() => NotificationHelperService))
    private notificationHelperService: NotificationHelperService,
    @Inject(forwardRef(() => ApplicationsService))
    private applicationsService: ApplicationsService,
  ) {}

  async create(createDto: CreateActivityNoteDto, userId: string, skipNotification: boolean = false): Promise<ActivityNote> {
    const activityNote = this.activityNotesRepository.create({
      ...createDto,
      created_by: userId,
    });

    const savedNote = await this.activityNotesRepository.save(activityNote);

    // Send email notification based on entity type
    // Skip notification if this activity note is being created by the notification system itself
    if (!createDto.is_internal && !skipNotification) {
      try {
        if (createDto.entity_type === 'application') {
          await this.sendActivityNoteNotification(savedNote, userId);
        } else if (createDto.entity_type === 'user') {
          await this.sendGeneralMessageNotification(savedNote, userId);
        }
      } catch (error) {
        console.error('Failed to send activity note notification:', error);
        // Don't fail the note creation if email fails
      }
    }

    return savedNote;
  }

  async findAll(queryDto: ActivityNoteQueryDto = {}): Promise<ActivityNote[]> {
    const where: any = {
      status: ActivityNoteStatus.ACTIVE,
    };

    // Add query filters
    if (queryDto.entity_type) where.entity_type = queryDto.entity_type;
    if (queryDto.entity_id) where.entity_id = queryDto.entity_id;
    if (queryDto.note_type) where.note_type = queryDto.note_type;
    if (queryDto.status) where.status = queryDto.status;
    if (queryDto.category) where.category = queryDto.category;
    if (queryDto.step) where.step = queryDto.step;
    if (queryDto.priority) where.priority = queryDto.priority;
    if (queryDto.is_internal !== undefined) where.is_internal = queryDto.is_internal;
    if (queryDto.created_by) where.created_by = queryDto.created_by;

    const options: FindManyOptions<ActivityNote> = {
      where,
      relations: ['creator', 'updater'],
      order: { created_at: 'DESC' },
    };

    return await this.activityNotesRepository.find(options);
  }

  async findByEntity(entityType: string, entityId: string): Promise<ActivityNote[]> {
    return await this.findAll({
      entity_type: entityType,
      entity_id: entityId,
    });
  }

  async findByEntityAndStep(entityType: string, entityId: string, step: string): Promise<ActivityNote[]> {
    return await this.findAll({
      entity_type: entityType,
      entity_id: entityId,
      step: step,
    });
  }

  async findOne(id: string): Promise<ActivityNote> {
    const options: FindOneOptions<ActivityNote> = {
      where: { id },
      relations: ['creator', 'updater'],
    };

    const activityNote = await this.activityNotesRepository.findOne(options);
    if (!activityNote) {
      throw new NotFoundException(`Activity note with ID ${id} not found`);
    }

    return activityNote;
  }

  async update(id: string, updateDto: UpdateActivityNoteDto, userId: string): Promise<ActivityNote> {
    const activityNote = await this.findOne(id);

    // Check if user can update this note (only creator or admin)
    if (activityNote.created_by !== userId) {
      throw new ForbiddenException('You can only update your own notes');
    }

    Object.assign(activityNote, updateDto);
    activityNote.updated_by = userId;

    return await this.activityNotesRepository.save(activityNote);
  }

  async archive(id: string, userId: string): Promise<ActivityNote> {
    const activityNote = await this.findOne(id);

    // Check if user can archive this note
    if (activityNote.created_by !== userId) {
      throw new ForbiddenException('You can only archive your own notes');
    }

    activityNote.status = ActivityNoteStatus.ARCHIVED;
    activityNote.archived_at = new Date();
    activityNote.updated_by = userId;

    return await this.activityNotesRepository.save(activityNote);
  }

  async softDelete(id: string, userId: string): Promise<void> {
    const activityNote = await this.findOne(id);

    // Check if user can delete this note
    if (activityNote.created_by !== userId) {
      throw new ForbiddenException('You can only delete your own notes');
    }

    activityNote.status = ActivityNoteStatus.DELETED;
    activityNote.deleted_at = new Date();
    activityNote.updated_by = userId;

    await this.activityNotesRepository.save(activityNote);
  }

  async hardDelete(id: string, userId: string): Promise<void> {
    const activityNote = await this.findOne(id);

    // Check if user can delete this note
    if (activityNote.created_by !== userId) {
      throw new ForbiddenException('You can only delete your own notes');
    }

    await this.activityNotesRepository.remove(activityNote);
  }

  // Utility methods for specific use cases
  async createEvaluationComment(
    applicationId: string,
    step: string,
    comment: string,
    userId: string,
    metadata?: Record<string, any>
  ): Promise<ActivityNote> {
    return await this.create({
      entity_type: 'application',
      entity_id: applicationId,
      note: comment,
      note_type: 'evaluation_comment' as any,
      step: step,
      category: 'evaluation',
      metadata: metadata,
      is_internal: true,
    }, userId);
  }

  async createStatusUpdate(
    applicationId: string,
    statusChange: string,
    userId: string,
    metadata?: Record<string, any>
  ): Promise<ActivityNote> {
    return await this.create({
      entity_type: 'application',
      entity_id: applicationId,
      note: statusChange,
      note_type: 'status_update' as any,
      category: 'status',
      metadata: metadata,
      priority: 'high',
    }, userId);
  }

  /**
   * Send email notification for general messages (entity_type = 'user')
   */
  private async sendGeneralMessageNotification(activityNote: ActivityNote, userId: string): Promise<void> {
    try {
      // Get creator name for the note
      const createdBy = activityNote.creator ?
        `${activityNote.creator.first_name} ${activityNote.creator.last_name}` :
        'MACRA Team';

      // Extract additional emails from metadata
      const additionalEmails: string[] = activityNote.metadata?.additional_emails || [];

      if (additionalEmails.length === 0) {
        console.warn('No additional emails provided for general message');
        return;
      }

      console.log(`📧 Sending general message to ${additionalEmails.length} recipient(s): ${additionalEmails.join(', ')}`);

      // Send notifications to all provided emails
      for (const email of additionalEmails) {
        if (email && email.trim()) {
          try {
            // Use the notification helper service to send general message
            await this.notificationHelperService.sendEmailNotification({
              recipientEmail: email.trim(),
              recipientName: 'Valued User',
              subject: `Message from MACRA - ${createdBy}`,
              message: activityNote.note,
              htmlContent: this.generateGeneralMessageHtml(activityNote.note, createdBy),
              entityType: 'user',
              entityId: activityNote.entity_id,
              recipientType: 'STAFF' as any,
              createdBy: userId,
              sendEmail: true,
              createInApp: false, // Don't create in-app notifications for general messages
            });
            console.log(`✅ General message sent to: ${email}`);
          } catch (emailError) {
            console.error(`❌ Failed to send general message to ${email}:`, emailError);
            // Continue with other emails even if one fails
          }
        }
      }
    } catch (error) {
      console.error('Error in sendGeneralMessageNotification:', error);
      throw error;
    }
  }

  /**
   * Generate HTML content for general messages
   */
  private generateGeneralMessageHtml(message: string, senderName: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="cid:logo@macra" alt="MACRA Logo" style="height: 60px;">
            <h1 style="color: #dc2626; margin: 20px 0 10px 0; font-size: 24px;">MACRA Digital Portal</h1>
            <p style="color: #666; margin: 0; font-size: 16px;">Message from ${senderName}</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <p style="color: #333; line-height: 1.6; margin: 0; white-space: pre-wrap;">${message}</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              This message was sent from the MACRA Digital Portal by ${senderName}.
            </p>
            <p style="color: #666; font-size: 12px; margin: 10px 0 0 0;">
              © ${new Date().getFullYear()} Malawi Communications Regulatory Authority (MACRA)
            </p>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Send email notification to applicant when activity note is created
   */
  private async sendActivityNoteNotification(activityNote: ActivityNote, userId: string): Promise<void> {
    try {
      // Get application details
      const application = await this.applicationsService.findOne(activityNote.entity_id);

      if (!application || !application.applicant) {
        console.warn(`Application or applicant not found for activity note ${activityNote.id}`);
        return;
      }

      // Format note type and category for display
      const noteType = activityNote.note_type || 'general';
      const category = activityNote.category || 'general';
      const step = activityNote.step;

      // Get creator name for the note
      const createdBy = activityNote.creator ?
        `${activityNote.creator.first_name} ${activityNote.creator.last_name}` :
        'MACRA Team';

      // Extract additional emails from metadata
      const additionalEmails: string[] = activityNote.metadata?.additional_emails || [];

      // Send notification to applicant using the notification helper service
      await this.notificationHelperService.notifyActivityNote(
        activityNote.entity_id, // applicationId
        application.applicant.applicant_id, // applicantId
        application.applicant.email, // applicantEmail
        application.applicant.name, // applicantName
        application.application_number, // applicationNumber
        application.license_category?.license_type?.name || 'License', // licenseType
        activityNote.note, // noteContent
        noteType, // noteType
        category, // category
        step, // step
        createdBy, // createdBy
        activityNote.created_at.toLocaleDateString(), // createdDate
        userId // createdBy (user ID)
      );

      // Send notifications to additional emails if provided
      if (additionalEmails.length > 0) {
        console.log(`📧 Sending activity note notifications to ${additionalEmails.length} additional email(s): ${additionalEmails.join(', ')}`);

        for (const email of additionalEmails) {
          if (email && email.trim()) {
            try {
              await this.notificationHelperService.notifyActivityNote(
                activityNote.entity_id, // applicationId
                null, // applicantId (null for additional emails)
                email.trim(), // recipientEmail
                'Additional Recipient', // recipientName
                application.application_number, // applicationNumber
                application.license_category?.license_type?.name || 'License', // licenseType
                activityNote.note, // noteContent
                noteType, // noteType
                category, // category
                step, // step
                createdBy, // createdBy
                activityNote.created_at.toLocaleDateString(), // createdDate
                userId, // createdBy (user ID)
                true // isAdditionalRecipient flag
              );
              console.log(`✅ Additional notification sent to: ${email}`);
            } catch (emailError) {
              console.error(`❌ Failed to send notification to additional email ${email}:`, emailError);
              // Continue with other emails even if one fails
            }
          }
        }
      }

      console.log(`✅ Activity note notification sent for application ${application.application_number}`);
    } catch (error) {
      console.error('❌ Failed to send activity note notification:', error);
      throw error;
    }
  }
}
