import {
  <PERSON><PERSON><PERSON>,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsUUID,
  IsNotEmpty,
  MaxLength,
  IsArray,
  ArrayNotEmpty,
  <PERSON>I<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EQUIPMENT_CATEGORIES, FREQUENCY_BANDS } from '../../entities/ceir-equipment-type-categories.entity';

export class CreateCeirEquipmentCategoryDto {
  @ApiProperty({
    description: 'Equipment category type',
    enum: EQUIPMENT_CATEGORIES,
    example: 'mobile_phone'
  })
  @IsString({ message: 'Category type must be a string' })
  @IsIn(EQUIPMENT_CATEGORIES, { message: 'Category type must be one of the valid equipment categories' })
  @IsNotEmpty({ message: 'Category type is required' })
  category_type: string;

  @ApiProperty({
    description: 'Display name for the equipment category',
    example: 'Mobile Phone',
    maxLength: 100
  })
  @IsString({ message: 'Category name must be a string' })
  @MaxLength(100, { message: 'Category name must not exceed 100 characters' })
  @IsNotEmpty({ message: 'Category name is required' })
  category_name: string;

  @ApiProperty({
    description: 'Detailed description of the equipment category',
    example: 'Handheld mobile communication devices for voice and data services',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @ApiProperty({
    description: 'CEIR standard code for this equipment type',
    example: 'CEIR-MP-001',
    maxLength: 50
  })
  @IsString({ message: 'CEIR standard code must be a string' })
  @MaxLength(50, { message: 'CEIR standard code must not exceed 50 characters' })
  @IsNotEmpty({ message: 'CEIR standard code is required' })
  ceir_standard_code: string;

  @ApiProperty({
    description: 'Supported frequency bands',
    type: 'array',
    items: { enum: [...FREQUENCY_BANDS] },
    example: ['gsm_900', 'gsm_1800', 'umts_2100'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Supported frequency bands must be an array' })
  @IsString({ each: true, message: 'Each frequency band must be a string' })
  @IsIn(FREQUENCY_BANDS, { each: true, message: 'Each frequency band must be a valid frequency band' })
  supported_frequency_bands?: string[];

  @ApiProperty({
    description: 'Required technical standards for this category',
    type: 'array',
    example: ['3GPP TS 51.010', 'ETSI EN 301 511'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Required standards must be an array' })
  @IsString({ each: true, message: 'Each standard must be a string' })
  required_standards?: string[];

  @ApiProperty({
    description: 'Required test procedures',
    type: 'array',
    example: ['SAR Testing', 'EMC Testing', 'RF Performance Testing'],
    required: false
  })
  @IsOptional()
  @IsArray({ message: 'Required test procedures must be an array' })
  @IsString({ each: true, message: 'Each test procedure must be a string' })
  required_test_procedures?: string[];

  @ApiProperty({
    description: 'Maximum allowed transmit power in dBm',
    example: 33,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: 'Maximum transmit power must be a number' })
  @Min(-50, { message: 'Maximum transmit power must be at least -50 dBm' })
  @Max(50, { message: 'Maximum transmit power must not exceed 50 dBm' })
  max_transmit_power_dbm?: number;

  @ApiProperty({
    description: 'Whether this category requires SAR testing',
    example: true,
    default: false
  })
  @IsBoolean({ message: 'Requires SAR testing must be a boolean' })
  requires_sar_testing: boolean = false;

  @ApiProperty({
    description: 'Whether this category requires EMC testing',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Requires EMC testing must be a boolean' })
  requires_emc_testing: boolean = true;

  @ApiProperty({
    description: 'Whether this category requires RF performance testing',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Requires RF testing must be a boolean' })
  requires_rf_testing: boolean = true;

  @ApiProperty({
    description: 'Validity period for type approval in months',
    example: 60,
    default: 60
  })
  @IsNumber({}, { message: 'Approval validity months must be a number' })
  @Min(1, { message: 'Approval validity must be at least 1 month' })
  @Max(120, { message: 'Approval validity must not exceed 120 months' })
  approval_validity_months: number = 60;

  @ApiProperty({
    description: 'Whether this category is currently active',
    example: true,
    default: true
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  is_active: boolean = true;

  @ApiProperty({
    description: 'User ID who is creating this category (optional, can be extracted from JWT)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false
  })
  @IsOptional()
  @IsUUID('4', { message: 'Created by must be a valid UUID if provided' })
  created_by?: string;
}
