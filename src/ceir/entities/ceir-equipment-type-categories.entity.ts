import {
  <PERSON><PERSON>ty,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  Before<PERSON><PERSON>rt,
  OneToMany,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsUUID, IsString, IsOptional, IsBoolean, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../entities/user.entity';


// Equipment category constants for validation
export const EQUIPMENT_CATEGORIES = [
  'mobile_phone',
  'smartphone',
  'tablet',
  'modem',
  'router',
  'iot_device',
  'wearable',
  'satellite_terminal',
  'radio_equipment',
  'network_equipment',
  'bluetooth_device',
  'wifi_device',
  'nfc_device',
  'drone',
  'automotive_device',
] as const;

// Frequency band constants for validation
export const FREQUENCY_BANDS = [
  'gsm_900',
  'gsm_1800',
  'umts_2100',
  'lte_800',
  'lte_1800',
  'lte_2600',
  'wifi_2_4ghz',
  'wifi_5ghz',
  'bluetooth',
  'nfc_13_56mhz',
  'satellite_l_band',
  'satellite_ku_band',
] as const;

@Entity('ceir_equipment_type_categories')
export class CeirEquipmentTypeCategories {
  @ApiProperty({
    description: 'Unique identifier for the equipment type category',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'uuid',
    primary: true,
    unique: true,
  })
  @IsUUID()
  category_id: string;

  @ApiProperty({
    description: 'Equipment category type',
    example: 'mobile_phone'
  })
  @Column({ type: 'varchar', length: 50 })
  @IsString()
  category_type: string;

  @ApiProperty({
    description: 'Display name for the equipment category',
    example: 'Mobile Phone'
  })
  @Column({ type: 'varchar', length: 100 })
  @IsString()
  category_name: string;

  @ApiProperty({
    description: 'Detailed description of the equipment category',
    example: 'Handheld mobile communication devices for voice and data services'
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'CEIR standard code for this equipment type',
    example: 'CEIR-MP-001'
  })
  @Column({ type: 'varchar', length: 50, unique: true })
  @IsString()
  ceir_standard_code: string;

  @ApiProperty({
    description: 'Supported frequency bands',
    type: 'array',
    example: ['gsm_900', 'gsm_1800', 'umts_2100']
  })
  @Column({ type: 'simple-array', nullable: true })
  supported_frequency_bands?: string[];

  @ApiProperty({
    description: 'Required technical standards for this category',
    example: ['3GPP TS 51.010', 'ETSI EN 301 511']
  })
  @Column({ type: 'simple-array', nullable: true })
  required_standards?: string[];

  @ApiProperty({
    description: 'Required test procedures',
    example: ['SAR Testing', 'EMC Testing', 'RF Performance Testing']
  })
  @Column({ type: 'simple-array', nullable: true })
  required_test_procedures?: string[];

  @ApiProperty({
    description: 'Maximum allowed transmit power in dBm',
    example: 33
  })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  max_transmit_power_dbm?: number;

  @ApiProperty({
    description: 'Whether this category requires SAR testing',
    example: true
  })
  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  requires_sar_testing: boolean;

  @ApiProperty({
    description: 'Whether this category requires EMC testing',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  requires_emc_testing: boolean;

  @ApiProperty({
    description: 'Whether this category requires RF performance testing',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  requires_rf_testing: boolean;

  @ApiProperty({
    description: 'Validity period for type approval in months',
    example: 60
  })
  @Column({ type: 'integer', default: 60 })
  @IsNumber()
  approval_validity_months: number;

  @ApiProperty({
    description: 'Whether this category is currently active',
    example: true
  })
  @Column({ type: 'boolean', default: true })
  @IsBoolean()
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany('Device', (device: any) => device.equipment_category, { nullable: true })
  approved_devices?: any[];

  @BeforeInsert()
  generateId() {
    if (!this.category_id) {
      this.category_id = uuidv4();
    }
  }
}
