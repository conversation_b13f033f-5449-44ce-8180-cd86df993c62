import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { TasksService } from '../tasks/tasks.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { TaskType, TaskPriority, TaskStatus } from '../entities/tasks.entity';

@Injectable()
export class ApplicationTaskHelperService {
  constructor(
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    private tasksService: TasksService,
    private notificationHelper: NotificationHelperService,
  ) {}

  /**
   * Centralized method to handle task creation when application status changes to submitted
   * This should be called by any service that updates application status
   */
  async handleApplicationSubmission(
    applicationId: string,
    previousStatus: string,
    newStatus: string,
    updatedBy: string
  ): Promise<void> {

    // Only create task when status changes TO submitted (and wasn't already submitted)
    if( newStatus == 'pass_evaluation') {
      this.tasksService.closeAllTasks(applicationId, 'application', updatedBy, 'Application passed evaluation');
    }
    if( newStatus == 'approved') {
      this.tasksService.closeAllTasks(applicationId, 'application', updatedBy, 'Application approved');
    }

    if (newStatus === 'submitted') {
      // Check if task already exists to prevent duplicates
      const taskExists = await this.taskExistsForApplication(applicationId);
      if (taskExists) {
        return;
      }
      // Get the application with all necessary relations
      const application = await this.applicationsRepository.findOne({
        where: { application_id: applicationId },
        relations: ['applicant', 'license_category', 'license_category.license_type'],
      });

      if (!application) {
        return;
      }

      const applicantName = application.applicant  ? application.applicant.name : 'Unknown Applicant';
      const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';
      const taskData: CreateTaskDto = {
        title: `Review Application - ${application.application_number}`,
        description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
        task_type: TaskType.APPLICATION,
        priority: TaskPriority.MEDIUM,
        status: TaskStatus.PENDING,
        entity_type: 'application',
        entity_id: application.application_id,
      };
      await this.tasksService.create(taskData, updatedBy);
      // Send notification to applicant about submission
      if (application.applicant) {
        try {
          await this.notificationHelper.notifyApplicationStatus(
            application.application_id,
            application.applicant_id,
            application.applicant.email,
            application.application_number,
            'submitted',
            updatedBy,
            application.applicant.name,
            application.license_category?.license_type?.name || 'License'
          );
        } catch (notificationError) {}

      }

    }
  }

  /**
   * Check if a task already exists for an application to prevent duplicates
   */
  async taskExistsForApplication(applicationId: string): Promise<boolean> {
    try {
      // Use the tasks service's dedicated method to find task for this application
      const existingTask = await this.tasksService.findTaskForApplication(applicationId);
      const exists = !!existingTask;
      return exists;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create task with duplicate prevention
   */
  async createTaskWithDuplicateCheck(
    applicationId: string,
    previousStatus: string,
    newStatus: string,
    updatedBy: string
  ): Promise<void> {
    // Only create task when status changes TO submitted (and wasn't already submitted)
    if (newStatus === 'submitted' && previousStatus !== 'submitted') {
      // Check if task already exists to prevent duplicates
      const taskExists = await this.taskExistsForApplication(applicationId);
      
      if (taskExists) {
        console.log(`ℹ️ Task already exists for application: ${applicationId}, skipping creation`);
        return;
      }

      await this.handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy);
    }
  }
}
