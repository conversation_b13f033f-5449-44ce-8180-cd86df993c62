import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ActivityNotesService } from '../services/activity-notes.service';
import { CreateActivityNoteDto, UpdateActivityNoteDto, ActivityNoteQueryDto } from '../dto/activity-notes.dto';
import { ActivityNote } from '../entities/activity-notes.entity';

@Controller('activity-notes')
@UseGuards(JwtAuthGuard)
export class ActivityNotesController {
  constructor(private readonly activityNotesService: ActivityNotesService) {}

  @Post()
  async create(
    @Body() createDto: CreateActivityNoteDto,
    @Request() req: any,
  ): Promise<ActivityNote> {
    return await this.activityNotesService.create(createDto, req.user.user_id);
  }

  @Get()
  async findAll(@Query() queryDto: ActivityNoteQueryDto): Promise<ActivityNote[]> {
    return await this.activityNotesService.findAll(queryDto);
  }

  @Get('entity/:entityType/:entityId')
  async findByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
  ): Promise<ActivityNote[]> {
    return await this.activityNotesService.findByEntity(entityType, entityId);
  }

  @Get('entity/:entityType/:entityId/step/:step')
  async findByEntityAndStep(
    @Param('entityType') entityType: string,
    @Param('entityId') entityId: string,
    @Param('step') step: string,
  ): Promise<ActivityNote[]> {
    return await this.activityNotesService.findByEntityAndStep(entityType, entityId, step);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ActivityNote> {
    return await this.activityNotesService.findOne(id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateActivityNoteDto,
    @Request() req: any,
  ): Promise<ActivityNote> {
    return await this.activityNotesService.update(id, updateDto, req.user.user_id);
  }

  @Put(':id/archive')
  async archive(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<ActivityNote> {
    return await this.activityNotesService.archive(id, req.user.user_id);
  }

  @Delete(':id/soft')
  @HttpCode(HttpStatus.NO_CONTENT)
  async softDelete(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return await this.activityNotesService.softDelete(id, req.user.user_id);
  }

  @Delete(':id/hard')
  @HttpCode(HttpStatus.NO_CONTENT)
  async hardDelete(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return await this.activityNotesService.hardDelete(id, req.user.user_id);
  }

  // Specialized endpoints for evaluation workflow
  @Post('evaluation-comment')
  async createEvaluationComment(
    @Body() body: {
      applicationId: string;
      step: string;
      comment: string;
      metadata?: Record<string, any>;
    },
    @Request() req: any,
  ): Promise<ActivityNote> {
    return await this.activityNotesService.createEvaluationComment(
      body.applicationId,
      body.step,
      body.comment,
      req.user.user_id,
      body.metadata,
    );
  }

  @Post('status-update')
  async createStatusUpdate(
    @Body() body: {
      applicationId: string;
      statusChange: string;
      metadata?: Record<string, any>;
    },
    @Request() req: any,
  ): Promise<ActivityNote> {
    return await this.activityNotesService.createStatusUpdate(
      body.applicationId,
      body.statusChange,
      req.user.user_id,
      body.metadata,
    );
  }
}
