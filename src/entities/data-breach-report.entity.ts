import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { User } from './user.entity';
import { DataBreachPriority, DataBreachSeverity, DataBreachStatus } from 'src/data-breach/data-breach-report-constants';

@Entity('data_breach_reports')
export class DataBreachReport {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  report_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  report_number: string; // Pattern: BREACH-YYYY-XXX

  @Column({ type: 'uuid' })
  @IsUUID()
  reporter_id: string; // References User who submitted the report

  @Column({ type: 'varchar', nullable: true})
  respondent_reg_number: string // References Organization against whom the report is filed, by registration number

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({ type: 'varchar' })
  category: string;

  @Column({
    type: 'varchar',
    default: DataBreachSeverity.MEDIUM,
  })
  severity: string;

  @Column({
    type: 'varchar',
    default: DataBreachStatus.SUBMITTED,
  })
  status: string;

  @Column({ type: 'varchar', default: DataBreachPriority.MEDIUM })
  priority: string;

  @Column({ type: 'date' })
  @IsDateString()
  incident_date: Date;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  organization_involved: string; // This is a user input to provide officers with more organisation context

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  affected_data_types?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  contact_attempts?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  assigned_to?: string; // Data Protection Officer assigned to handle the report

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  resolution?: string; // Resolution details when report is resolved

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  internal_notes?: string; // Internal notes for staff

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  resolved_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'reporter_id' })
  reporter: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  // TO-DO: Add organization/ applicant relation here for respondent

  @OneToMany(() => DataBreachReportAttachment, (attachment) => attachment.report)
  attachments: DataBreachReportAttachment[];

  @OneToMany(() => DataBreachReportStatusHistory, (history) => history.report)
  status_history: DataBreachReportStatusHistory[];

  @BeforeInsert()
  generateId() {
    if (!this.report_id) {
      this.report_id = uuidv4();
    }
    if (!this.report_number) {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 999) + 1;
      this.report_number = `BREACH-${year}-${randomNum.toString().padStart(3, '0')}`;
    }
  }
}

// Attachment entity for data breach reports
@Entity('data_breach_report_attachments')
export class DataBreachReportAttachment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  attachment_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  report_id: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_name: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_path: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  file_type: string;

  @Column({ type: 'bigint' })
  file_size: number;

  @CreateDateColumn()
  uploaded_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  uploaded_by: string;

  // Relations
  @ManyToOne(() => DataBreachReport, (report) => report.attachments)
  @JoinColumn({ name: 'report_id' })
  report: DataBreachReport;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploaded_by' })
  uploader: User;

  @BeforeInsert()
  generateId() {
    if (!this.attachment_id) {
      this.attachment_id = uuidv4();
    }
  }
}

// Status history entity for tracking report status changes
@Entity('data_breach_report_status_history')
export class DataBreachReportStatusHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  history_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  report_id: string;

  @Column({
    type: 'enum',
    enum: DataBreachStatus,
  })
  @IsEnum(DataBreachStatus)
  status: DataBreachStatus;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  comment?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  created_by: string;

  // Relations
  @ManyToOne(() => DataBreachReport, (report) => report.status_history)
  @JoinColumn({ name: 'report_id' })
  report: DataBreachReport;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @BeforeInsert()
  generateId() {
    if (!this.history_id) {
      this.history_id = uuidv4();
    }
  }
}
