import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
  Inject,
  forwardRef
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Payment, PaymentStatus, PaymentType, Currency } from '../entities/payment.entity';
import { User, UserStatus } from '../entities/user.entity';
import { Applications } from '../entities/applications.entity';
import { Invoices } from '../entities/invoices.entity';
import { Documents } from '../entities/documents.entity';
import { CreatePaymentDto } from '../dto/payments/create-payment.dto';
import { UpdatePaymentDto } from '../dto/payments/update-payment.dto';
import { TasksService } from '../tasks/tasks.service';
import { UsersService } from '../users/users.service';
import { InvoicesService } from '../invoices/invoices.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { DocumentsService } from '../documents/documents.service';
import { Task, TaskType, TaskPriority } from '../entities/tasks.entity';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';


export interface PaymentFilters {
  status?: string;
  paymentType?: string;
  dateRange?: 'last-30' | 'last-90' | 'last-year';
  search?: string;
  userId?: string;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface PaymentQueryResult {
  payments: any[]; // Will include documents property
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}


export interface FileUploadResult {
  filename: string;
  originalname: string;
  path: string;
  size: number;
  mimetype: string;
}

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    @InjectRepository(Invoices)
    private invoicesRepository: Repository<Invoices>,
    @InjectRepository(Documents)
    private documentsRepository: Repository<Documents>,
    @InjectRepository(Task)
    private tasksRepository: Repository<Task>,
    @Inject(forwardRef(() => TasksService))
    private tasksService: TasksService,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    @Inject(forwardRef(() => InvoicesService))
    private invoicesService: InvoicesService,
    @Inject(forwardRef(() => NotificationHelperService))
    private notificationHelperService: NotificationHelperService,
    @Inject(forwardRef(() => DocumentsService))
    private documentsService: DocumentsService,
  ) {}

  /**
   * Helper method to fetch documents for payments
   */
  private async attachDocumentsToPayments(payments: Payment[]): Promise<any[]> {
    if (!payments || payments.length === 0) {
      return payments;
    }

    const paymentsWithDocuments = await Promise.all(
      payments.map(async (payment) => {
        try {
          const documents = await this.documentsRepository.find({
            where: {
              entity_type: 'payment',
              entity_id: payment.payment_id,
            },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
          });

          return {
            ...payment,
            documents: documents || [],
          };
        } catch (error) {
          console.error(`Error fetching documents for payment ${payment.payment_id}:`, error);
          return {
            ...payment,
            documents: [],
          };
        }
      })
    );

    return paymentsWithDocuments;
  }

  /**
   * Helper method to fetch documents for a single payment
   */
  private async attachDocumentsToPayment(payment: Payment): Promise<any> {
    try {
      const documents = await this.documentsRepository.find({
        where: {
          entity_type: 'payment',
          entity_id: payment.payment_id,
        },
        relations: ['creator', 'updater'],
        order: { created_at: 'DESC' },
      });

      return {
        ...payment,
        documents: documents || [],
      };
    } catch (error) {
      console.error(`Error fetching documents for payment ${payment.payment_id}:`, error);
      return {
        ...payment,
        documents: [],
      };
    }
  }

  /**
   * Create a new payment record
   */
  async createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment> {
    try {
      // Validate user exists
      const user = await this.usersRepository.findOne({
        where: { user_id: createPaymentDto.user_id }
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Validate creator exists
      const creator = await this.usersRepository.findOne({
        where: { user_id: createPaymentDto.created_by }
      });

      if (!creator) {
        throw new NotFoundException('Creator user not found');
      }

      // Check if invoice number already exists
      const existingPayment = await this.paymentsRepository.findOne({
        where: { invoice_number: createPaymentDto.invoice_number }
      });

      if (existingPayment) {
        throw new ConflictException('Invoice number already exists');
      }

      const payment = this.paymentsRepository.create(createPaymentDto);
      const savedPayment = await this.paymentsRepository.save(payment);

      // Create task for finance team to approve the payment if status is PENDING
      if (savedPayment.status === PaymentStatus.PENDING) {
        try {

        const invoice = await this.invoicesRepository
          .createQueryBuilder('invoice')
          .where('invoice.invoice_id = :invoiceId', { invoice_id:payment.invoice_id })
          .getOne();
          if (invoice) await this.createPaymentReviewTask(savedPayment, invoice)
        } catch (taskError) {
          console.error('⚠️ Failed to create payment approval task:', taskError);
          // Don't fail the payment creation if task creation fails
        }
      }

      return savedPayment;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create payment');
    }
  }

  /**
   * Get all payments with optional filters and pagination
   */
  async getPayments(
    filters: PaymentFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaymentQueryResult> {
    try {
      console.log('💳 PaymentsService.getPayments called with filters:', filters, 'pagination:', pagination);
      console.log('🔍 Filters breakdown - userId:', filters.userId, 'status:', filters.status, 'search:', filters.search);

      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const query = this.paymentsRepository.createQueryBuilder('payment')
        .leftJoinAndSelect('payment.user', 'user')
        .leftJoinAndSelect('payment.creator', 'creator')
        .leftJoinAndSelect('payment.updater', 'updater');

      // Apply filters
      if (filters.status) {
        query.andWhere('payment.status = :status', { status: filters.status });
      }

      if (filters.paymentType) {
        query.andWhere('payment.payment_type = :paymentType', { paymentType: filters.paymentType });
      }

      if (filters.userId) {
        query.andWhere('payment.user_id = :userId', { userId: filters.userId });
      }

      if (filters.dateRange) {
        const now = new Date();
        let startDate: Date;

        switch (filters.dateRange) {
          case 'last-30':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'last-90':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case 'last-year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date('1970-01-01');
        }

        query.andWhere('payment.issue_date >= :startDate', { startDate });
      }

      if (filters.search) {
        query.andWhere('(payment.invoice_number LIKE :search OR payment.description LIKE :search)', {
          search: `%${filters.search}%`
        });
      }

      // Get total count
      const total = await query.getCount();
      // Apply pagination and get results
      const payments = await query
        .orderBy('payment.created_at', 'DESC')
        .skip(skip)
        .take(limit)
        .getMany();

      // Attach documents to payments
      const paymentsWithDocuments = await this.attachDocumentsToPayments(payments);

      const result = {
        payments: paymentsWithDocuments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };

      return result;
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payments');
    }
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(paymentId: string): Promise<any> {
    try {
      const payment = await this.paymentsRepository.findOne({
        where: { payment_id: paymentId },
        relations: ['user', 'application', 'creator', 'updater']
      });

      if (!payment) {
        throw new NotFoundException('Payment not found');
      }

      // Attach documents to the payment
      const paymentWithDocuments = await this.attachDocumentsToPayment(payment);

      return paymentWithDocuments;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to fetch payment');
    }
  }

  /**
   * Update payment with automatic invoice status checking
   */
  async updatePayment(paymentId: string, updatePaymentDto: UpdatePaymentDto, updatedBy?: string): Promise<Payment| null> {
    const payment = await this.getPaymentById(paymentId);
    if( ! payment) return null;

    try {
      // Check if updating invoice number and if it conflicts
      if (updatePaymentDto.invoice_number && updatePaymentDto.invoice_number !== payment.invoice_number) {
        const existingPayment = await this.paymentsRepository.findOne({
          where: { invoice_number: updatePaymentDto.invoice_number }
        });

        if (existingPayment && existingPayment.payment_id !== paymentId) {
          throw new ConflictException('Invoice number already exists');
        }
      }

      // Validate updated_by user if provided
      if (updatePaymentDto.updated_by || updatedBy) {
        const updaterUserId = updatePaymentDto.updated_by || updatedBy;
        const updater = await this.usersRepository.findOne({
          where: { user_id: updaterUserId }
        });

        if (!updater) {
          throw new NotFoundException('Updater user not found');
        }
      }
      // Set updated_by if provided
      const updateData = {
        ...updatePaymentDto,
        updated_by: updatePaymentDto.updated_by || updatedBy,
      };

      await this.paymentsRepository.update(paymentId, updateData);
      const updatedPayment = await this.getPaymentById(paymentId);
      await this.handlePaymentApproval(updatedPayment, updatedBy);
      return updatedPayment;
    } catch (error) {
      console.error(`❌ Error in updatePayment for payment ${paymentId}:`, error);
      console.error('Error stack:', error.stack);
      console.error('Update data:', updatePaymentDto);

      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }

      // Log the specific error type and message for debugging
      console.error('Error type:', error.constructor.name);
      console.error('Error message:', error.message);

      throw new InternalServerErrorException(`Failed to update payment: ${error.message}`);
    }
  }

  /**
   * Handle payment approval - check if invoice should be marked as paid and send notifications
   */
  private async handlePaymentApproval(payment: Payment, updatedBy?: string): Promise<{
    invoice_fully_paid: boolean;
    total_paid_amount: number;
    remaining_amount: number;
    invoice_amount: number;
  } | null> {
    try {
      // Get the invoice for this payment
      if (!payment.invoice_id) {
        return null;
      }

      const invoice = await this.invoicesService.findOne(payment.invoice_id);
      if (!invoice) {
        return null;
      }

      // Get all paid payments for this invoice
      const allPayments = await this.paymentsRepository.find({
        where: {
          invoice_id: payment.invoice_id,
          status: 'approved'
        }
      });

      // Calculate total paid amount
      const totalPaidAmount = allPayments.reduce((sum, p) => Number(sum) + Number(p.amount), 0);
      const isFullyPaid = totalPaidAmount >= invoice.amount;
      const remainingAmount = Math.max(0, invoice.amount - totalPaidAmount);

      console.log("TotalPaidAmount===================:", totalPaidAmount)
      console.log("IsFullyPaid===================:", isFullyPaid)
      console.log("RemainingAmount===================:", remainingAmount)
    
      try {
        await this.tasksService.closeAllTasks(payment.payment_id, 'payment',( updatedBy??''), 'Payment approved');
      } catch(e) {}

      try {
        await this.sendPaymentConfirmationEmail(invoice, totalPaidAmount, updatedBy || payment.updated_by || 'system');
      } catch(e) {}

      // Check if total paid amount meets or exceeds invoice amount
      if (isFullyPaid) {
        // Mark invoice as paid
        try{
          await this.invoicesService.markAsPaid(invoice.invoice_id, updatedBy || payment.updated_by || 'system');
        } catch(e) {}
        // Create license approval task for Director General if invoice is for an application
        if (invoice.entity_type === 'application' && invoice.entity_id) {
          try {
            await this.createLicenseApprovalTask(invoice, updatedBy || payment.updated_by || 'system');
          } catch (taskError) {
            console.error(`⚠️ Failed to create license approval task for application ${invoice.entity_id}:`, taskError);
          }
        }
      }

      return {
        invoice_fully_paid: isFullyPaid,
        total_paid_amount: totalPaidAmount,
        remaining_amount: remainingAmount,
        invoice_amount: invoice.amount,
      };

    } catch (error) {
      // Don't throw error to prevent payment update from failing
      return null;
    }
  }

  /**
   * Helper method to find tasks by entity
   */
  private async findTasksByEntity(entityType: string, entityId: string, statuses: string[], taskType?: string): Promise<any[]> {
    const whereConditions: any = {
      entity_type: entityType,
      entity_id: entityId,
      status: In(statuses)
    };

    if (taskType) {
      whereConditions.task_type = taskType;
    }

    return await this.tasksRepository.find({
      where: whereConditions
    });
  }

  /**
   * Create license approval task for Director General when application invoice is fully paid
   */
  private async createLicenseApprovalTask(invoice: any, createdBy: string): Promise<void> {

    // Get application details
    const application = await this.applicationsRepository.findOne({
      where: { application_id: invoice.entity_id },
      relations: ['applicant', 'license_category', 'license_category.license_type']
    });
    if (!application) {
      return;
    }

    this.applicationsRepository.update(invoice.entity_id, {
      status: 'waiting_for_approval',
      updated_by: createdBy || 'system'
    });

    // Find Director General user
    const directorGeneral = await this.usersRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.name = :roleName', { roleName: 'director general' })
      .andWhere('user.status = :status', { status: 'active' })
      .getOne();

    const applicantName = application.applicant?.name || 'Unknown Applicant';
    const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';

    const taskTitle = `License Approval Required - ${application.application_number}`;
    const taskDescription = `Payment has been confirmed for application ${application.application_number} submitted by ${applicantName} for ${licenseTypeName} license.

      Invoice ${invoice.invoice_number} has been fully paid. Please review and approve the license for issuance.

      Application Details:
      - Application Number: ${application.application_number}
      - Applicant: ${applicantName}
      - License Type: ${licenseTypeName}
      - Invoice Amount: MWK ${invoice.amount.toLocaleString()}
      - Payment Status: Fully Paid

      Please review the application and approve the license for issuance.`;

    const createTaskDto: CreateTaskDto = {
      task_type: TaskType.APPLICATION,
      title: taskTitle,
      description: taskDescription,
      priority: TaskPriority.HIGH,
      entity_type: 'application',
      entity_id: application.application_id,
      assigned_to: directorGeneral ? directorGeneral.user_id : '',
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      metadata: {
        invoice_id: invoice.invoice_id,
        invoice_number: invoice.invoice_number,
        payment_confirmed: true,
        task_type: 'license_approval',
        requires_dg_approval: true
      }
    };
    const task = await this.tasksService.create(createTaskDto, createdBy);
    // Send notification to Director General
    if (directorGeneral && directorGeneral.email) {
      await this.notificationHelperService.notifyTaskAssignment(
        task.task_id,
        directorGeneral.user_id,
        directorGeneral.email,
        directorGeneral.phone || '',
        taskTitle,
        taskDescription,
        createdBy,
        `${directorGeneral.first_name} ${directorGeneral.last_name}`,
        application.application_number,
        applicantName,
        'HIGH'
      );
    }

  }

  /**
   * Send payment confirmation email to client
   * Send payment confirmation email to client
   */
  private async sendPaymentConfirmationEmail(invoice: any, totalPaidAmount: number, updatedBy: string): Promise<void> {
    try {
      if (!invoice.client || !invoice.client.email) {
        console.log(`⚠️ No client email found for invoice ${invoice.invoice_number}, skipping email notification`);
        return;
      }

      console.log(`📧 Sending payment confirmation email for invoice ${invoice.invoice_number} to ${invoice.client.email}`);

      // Create payment confirmation notification
      await this.notificationHelperService.notifyPaymentConfirmation(
        invoice.invoice_id,
        invoice.client.applicant_id || invoice.client_id,
        invoice.client.email,
        invoice.client.name || `${invoice.client.first_name} ${invoice.client.last_name}`,
        invoice.invoice_number,
        invoice.amount,
        totalPaidAmount,
        new Date().toLocaleDateString(),
        updatedBy
      );

      console.log(`✅ Payment confirmation email sent for invoice ${invoice.invoice_number}`);
    } catch (error) {
      console.error(`❌ Failed to send payment confirmation email for invoice ${invoice.invoice_number}:`, error);
      // Don't throw error to prevent payment update from failing
    }
  }

  /**
   * Delete payment
   */
  async deletePayment(paymentId: string): Promise<void> {
    try {
      const payment = await this.getPaymentById(paymentId);
      await this.paymentsRepository.remove(payment);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete payment');
    }
  }


  /**
   * Get payment statistics
   */
  async getPaymentStatistics(userId?: string) {
    try {
      const query = this.paymentsRepository.createQueryBuilder('payment');

      if (userId) {
        query.where('payment.user_id = :userId', { userId });
      }

      const [
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount,
        paidAmount,
        pendingAmount
      ] = await Promise.all([
        query.getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.APPROVED }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).getCount(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.OVERDUE }).getCount(),
        query.clone().select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.APPROVED }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
        query.clone().where('payment.status = :status', { status: PaymentStatus.PENDING }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
      ]);

      return {
        totalPayments,
        paidPayments,
        pendingPayments,
        overduePayments,
        totalAmount: parseFloat(totalAmount.total || '0'),
        paidAmount: parseFloat(paidAmount.total || '0'),
        pendingAmount: parseFloat(pendingAmount.total || '0'),
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch payment statistics');
    }
  }

  /**
   * Mark overdue payments
   */
  async markOverduePayments(): Promise<void> {
    try {
      await this.paymentsRepository
        .createQueryBuilder()
        .update(Payment)
        .set({ status: PaymentStatus.OVERDUE })
        .andWhere('status = :status', { status: PaymentStatus.PENDING })
        .execute();
    } catch (error) {
      throw new InternalServerErrorException('Failed to mark overdue payments');
    }
  }

  /**
   * Get payments by entity (polymorphic relationship)
   */
  async getPaymentsByEntity(
    entityType: string,
    entityId: string,
    pagination: PaginationOptions = {}
  ): Promise<PaymentQueryResult> {
    try {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const [payments, total] = await this.paymentsRepository.findAndCount({
        where: {
          entity_type: entityType,
          entity_id: entityId,
        },
        relations: ['user', 'creator'],
        skip,
        take: limit,
        order: { created_at: 'DESC' },
      });

      // Attach documents to payments
      const paymentsWithDocuments = await this.attachDocumentsToPayments(payments);

      return {
        payments: paymentsWithDocuments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to get payments by entity');
    }
  }

  /**
   * Create payment for a specific entity (polymorphic)
   */
  async createPaymentForEntity(
    entityType: string,
    entityId: string,
    paymentData: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>
  ): Promise<Payment> {
    const createPaymentDto: CreatePaymentDto = {
      ...paymentData,
      entity_type: entityType,
      entity_id: entityId,
    };

    return this.createPayment(createPaymentDto);
  }

  /**
   * Get payments filtered by customer (based on user's applications)
   */
  async getPaymentsByCustomer(userId: string, filters: any): Promise<any> {
    try {
      console.log(`🔍 Getting payments for customer: ${userId}`);

      // First, get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        console.log(`ℹ️ No applications found for user ${userId}`);
        return {
          data: [],
          meta: {
            itemsPerPage: filters.limit || 10,
            totalItems: 0,
            currentPage: filters.page || 1,
            totalPages: 0,
          },
        };
      }

      const applicationIds = userApplications.map(app => app.application_id);
      console.log(`📋 Found ${applicationIds.length} applications for user ${userId}`);

      // Build query for payments related to user's applications
      const queryBuilder = this.paymentsRepository
        .createQueryBuilder('payment')
        .leftJoinAndSelect('payment.user', 'user')
        .where('payment.entity_type = :entityType', { entityType: 'application' })
        .andWhere('payment.entity_id IN (:...applicationIds)', { applicationIds });

      // Apply filters
      if (filters.status) {
        const statuses = filters.status.split(',').map((s: string) => s.trim());
        queryBuilder.andWhere('payment.status IN (:...statuses)', { statuses });
      }

      if (filters.paymentType) {
        queryBuilder.andWhere('payment.payment_type = :paymentType', { paymentType: filters.paymentType });
      }

      if (filters.search) {
        queryBuilder.andWhere(
          '(payment.invoice_number LIKE :search OR payment.description LIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      if (filters.dateRange) {
        const now = new Date();
        let startDate: Date | undefined;

        switch (filters.dateRange) {
          case 'last-30':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'last-90':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case 'last-year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
        }

        if (startDate) {
          queryBuilder.andWhere('payment.created_at >= :startDate', { startDate });
        }
      }

      // Add pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const offset = (page - 1) * limit;

      queryBuilder
        .orderBy('payment.created_at', 'DESC')
        .skip(offset)
        .take(limit);

      const [payments, totalItems] = await queryBuilder.getManyAndCount();


      // Attach documents to payments
      const paymentsWithDocuments = await this.attachDocumentsToPayments(payments);

      return {
        data: paymentsWithDocuments,
        meta: {
          itemsPerPage: limit,
          totalItems,
          currentPage: page,
          totalPages: Math.ceil(totalItems / limit),
        },
      };
    } catch (error) {
      console.error(`❌ Error getting payments for customer ${userId}:`, error);
      throw new InternalServerErrorException(`Failed to get customer payments: ${error.message}`);
    }
  }

  /**
   * Get payment statistics for a specific customer
   */
  async getPaymentStatisticsByCustomer(userId: string): Promise<any> {
    try {
      console.log(`📊 Getting payment statistics for customer: ${userId}`);

      // Get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        return {
          totalPayments: 0,
          pendingPayments: 0,
          paidPayments: 0,
          overduePayments: 0,
          cancelledPayments: 0,
          totalAmount: 0,
          pendingAmount: 0,
          paidAmount: 0,
          overdueAmount: 0,
        };
      }

      const applicationIds = userApplications.map(app => app.application_id);

      // Get payment statistics
      const payments = await this.paymentsRepository
        .createQueryBuilder('payment')
        .where('payment.entity_type = :entityType', { entityType: 'application' })
        .andWhere('payment.entity_id IN (:...applicationIds)', { applicationIds })
        .getMany();

      const stats = {
        totalPayments: payments.length,
        pendingPayments: payments.filter(pay => pay.status === PaymentStatus.PENDING).length,
        paidPayments: payments.filter(pay => pay.status === PaymentStatus.APPROVED).length,
        overduePayments: payments.filter(pay => pay.status === PaymentStatus.OVERDUE).length,
        cancelledPayments: payments.filter(pay => pay.status === PaymentStatus.CANCELLED).length,
        totalAmount: payments.reduce((sum, pay) => sum + Number(pay.amount), 0),
        pendingAmount: payments.filter(pay => pay.status === PaymentStatus.PENDING).reduce((sum, pay) => sum + Number(pay.amount), 0),
        paidAmount: payments.filter(pay => pay.status === PaymentStatus.APPROVED).reduce((sum, pay) => sum + Number(pay.amount), 0),
        overdueAmount: payments.filter(pay => pay.status === PaymentStatus.OVERDUE).reduce((sum, pay) => sum + Number(pay.amount), 0),
      };
      return stats;
    } catch (error) {
      console.error(`❌ Error getting payment statistics for customer ${userId}:`, error);
      throw new InternalServerErrorException(`Failed to get customer payment statistics: ${error.message}`);
    }
  }

  /**
   * Get specific payment by customer (with ownership validation)
   */
  async getPaymentByCustomer(paymentId: string, userId: string): Promise<Payment> {
    try {
      console.log(`🔍 Getting payment ${paymentId} for customer: ${userId}`);

      // Get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        throw new NotFoundException(`Payment not found or not accessible`);
      }

      const applicationIds = userApplications.map(app => app.application_id);

      // Find the payment and validate ownership
      const payment = await this.paymentsRepository
        .createQueryBuilder('payment')
        .leftJoinAndSelect('payment.user', 'user')
        .where('payment.payment_id = :paymentId', { paymentId })
        .andWhere('payment.entity_type = :entityType', { entityType: 'application' })
        .andWhere('payment.entity_id IN (:...applicationIds)', { applicationIds })
        .getOne();

      if (!payment) {
        throw new NotFoundException(`Payment not found or not accessible`);
      }

      console.log(`✅ Found payment ${paymentId} for customer ${userId}`);
      return payment;
    } catch (error) {
      console.error(`❌ Error getting payment ${paymentId} for customer ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get customer payment: ${error.message}`);
    }
  }

  /**
   * Get payments for a specific application by customer (with ownership validation)
   */
  async getApplicationPaymentsByCustomer(applicationId: string, userId: string): Promise<Payment[]> {
    try {
      console.log(`🔍 Getting payments for application ${applicationId} by customer: ${userId}`);

      // First, validate that the application belongs to the user
      const application = await this.applicationsRepository.findOne({
        where: {
          application_id: applicationId,
          created_by: userId
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found or not accessible`);
      }

      // Get payments for this application
      const payments = await this.paymentsRepository.find({
        where: {
          entity_type: 'application',
          entity_id: applicationId,
        },
        relations: ['user'],
        order: { created_at: 'DESC' },
      });

      console.log(`✅ Found ${payments.length} payments for application ${applicationId}`);
      return payments;
    } catch (error) {
      console.error(`❌ Error getting application payments for customer ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get application payments: ${error.message}`);
    }
  }

  /**
   * Get payments for a specific invoice by customer (with ownership validation)
   */
  async getInvoicePaymentsByCustomer(invoiceId: string): Promise<Payment[]> {
    try {
      // First, validate that the invoice belongs to the user's application
      const invoice = await this.invoicesRepository.findOne({
        where: { invoice_id: invoiceId },
      });

      if (!invoice) {
        throw new NotFoundException(`Invoice not found`);
      }
      // Check if the invoice is linked to an application and if it belongs to the user
      if (invoice.entity_type === 'application' && invoice.entity_id) {
        const application = await this.applicationsRepository.findOne({
          where: {
            application_id: invoice.entity_id,
          },
        });

        if (!application) {
          throw new NotFoundException(`Invoice not found or not accessible`);
        }
      } else {
        throw new NotFoundException(`Invoice not found or not accessible`);
      }
      // Use query builder for more complex search
      const paymentsQuery = this.paymentsRepository.createQueryBuilder('payment')
        .where('payment.invoice_id = :invoiceId', { invoiceId })
        .orWhere('payment.invoice_number = :invoiceNumber', { invoiceNumber: invoice.invoice_number });

      const payments = await paymentsQuery
        .orderBy('payment.created_at', 'DESC')
        .getMany();
      return payments;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get invoice payments: ${error.message}`);
    }
  }

  /**
   * Upload proof of payment by customer (creates a new payment linked to invoice)
   * Note: File upload should be handled by documents controller, this method links the document to payment
   */
  async uploadProofOfPaymentByCustomer(invoiceId: string, userId: string, proofData: any, documentId?: string): Promise<any> {
    try {
      const invoice = await this.invoicesRepository
        .createQueryBuilder('invoice')
        .where('invoice.invoice_id = :invoiceId', { invoiceId })
        .getOne();

      if (!invoice) {
        throw new NotFoundException(`Invoice not found or not accessible`);
      }

      // Validate invoice has required fields
      if (!invoice.invoice_number || invoice.invoice_number.trim() === '') {
        throw new BadRequestException(`Invoice has invalid or missing invoice number`);
      }

      // Check if a specific payment_id was provided for update
      let existingPayment: Payment | null = null;

      if (proofData.payment_id) {
        // Update mode: Look for the specific payment by payment_id
        existingPayment = await this.paymentsRepository.findOne({
          where: {
            payment_id: proofData.payment_id,
            user_id: userId // Ensure user owns this payment
          }
        });

        if (existingPayment) {
          // Verify the payment belongs to this invoice
          if (existingPayment.invoice_id !== invoiceId && !existingPayment.invoice_number?.startsWith(invoice.invoice_number)) {
            throw new BadRequestException(`Payment ${proofData.payment_id} does not belong to invoice ${invoiceId}`);
          }
        } else {
          throw new NotFoundException(`Payment with ID ${proofData.payment_id} not found or not accessible`);
        }
      }

      let savedPayment: Payment;

      if (existingPayment) {
        // Update existing payment with proof of payment details
        console.log(`📝 Updating existing payment ${existingPayment.payment_id} with new proof of payment`);

        // Update payment details
        existingPayment.amount = proofData.amount || existingPayment.amount;
        existingPayment.payment_method = proofData.paymentMethod || existingPayment.payment_method;
        existingPayment.transaction_reference = proofData.transactionReference || existingPayment.transaction_reference;
        existingPayment.notes = proofData.notes || existingPayment.notes;
        existingPayment.proof_of_payment_notes = proofData.notes || null;
        existingPayment.proof_of_payment_uploaded_at = new Date();
        existingPayment.updated_by = userId;

        // Ensure the payment is linked to the correct invoice
        existingPayment.invoice_id = invoiceId;
        if (!existingPayment.invoice_number || existingPayment.invoice_number.includes('-PAY-')) {
          // If the payment doesn't have a proper invoice number or has a generated one, update it
          existingPayment.invoice_number = invoice.invoice_number;
        }

        savedPayment = await this.paymentsRepository.save(existingPayment);
        console.log(`✅ Updated existing payment: ${savedPayment.payment_id}`);
      } else {
        // Create a new payment record for this proof of payment
        const payment = new Payment();

        // Generate a unique invoice number for this payment (due to unique constraint)
        // But make it more readable and linked to the original invoice
        const baseInvoiceNumber = invoice.invoice_number?.trim();

        if (!baseInvoiceNumber) {
          throw new BadRequestException('Invoice has invalid or missing invoice number');
        }

        // Check how many payments already exist for this invoice to create a sequence
        const existingPaymentsCount = await this.paymentsRepository
          .createQueryBuilder('payment')
          .where('payment.invoice_id = :invoiceId', { invoiceId })
          .orWhere('payment.invoice_number LIKE :invoicePattern', { invoicePattern: `${baseInvoiceNumber}%` })
          .getCount();

        // Create a sequential payment number
        const paymentSequence = existingPaymentsCount + 1;
        const uniqueInvoiceNumber = `${baseInvoiceNumber}-P${paymentSequence.toString().padStart(2, '0')}`;

        // Double-check that this invoice number doesn't already exist
        const existingWithSameNumber = await this.paymentsRepository.findOne({
          where: { invoice_number: uniqueInvoiceNumber },
        });

        if (existingWithSameNumber) {
          // If by some chance it exists, add a timestamp suffix
          const timestamp = Date.now();
          payment.invoice_number = `${baseInvoiceNumber}-P${paymentSequence.toString().padStart(2, '0')}-${timestamp}`;
        } else {
          payment.invoice_number = uniqueInvoiceNumber;
        }

        payment.amount = proofData.amount || invoice.amount;
        payment.currency = Currency.MWK; // Default currency
        payment.status = PaymentStatus.PENDING;
        payment.payment_type = PaymentType.LICENSE_FEE; // Default payment type
        payment.description = `Payment for Invoice ${baseInvoiceNumber}`;
        payment.issue_date = new Date();
        payment.payment_method = proofData.paymentMethod || null;
        payment.transaction_reference = proofData.transactionReference || null;
        payment.notes = proofData.notes || null;
        payment.entity_type = 'application';
        payment.entity_id = invoice.entity_id;
        payment.invoice_id = invoiceId;
        payment.user_id = userId;
        payment.created_by = userId;

        // Set proof of payment fields
        payment.proof_of_payment_notes = proofData.notes || null;
        payment.proof_of_payment_uploaded_at = new Date();
        savedPayment = await this.paymentsRepository.save(payment);
      }

      // Link existing document to payment (document should be uploaded via documents controller)
      let savedDocument: Documents | null = null;
      if (documentId) {
        savedDocument = await this.documentsService.findOne(documentId);
        if (savedDocument) {
          // Update document to link it to the payment
          await this.documentsService.update(documentId, {
            entity_type: 'payment',
            entity_id: savedPayment.payment_id,
          }, userId);
        }
      }

      // Update payment with document reference
      if (savedDocument) {
        savedPayment.proof_of_payment_url = savedDocument.file_path;
        await this.paymentsRepository.save(savedPayment);
      }

      const isUpdate = existingPayment !== null;
      const actionMessage = isUpdate ? 'updated' : 'created';
      console.log(`✅ Proof of payment uploaded and payment ${actionMessage}: ${savedPayment.payment_id}`);

      // Create task for finance team to review the proof of payment
      try {
        await this.createPaymentReviewTask(savedPayment, invoice);
      } catch (taskError) {
        console.error('⚠️ Failed to create payment review task:', taskError);
        // Don't fail the payment upload if task creation fails
      }

      return {
        success: true,
        message: `Proof of payment uploaded successfully. Your payment is now under review.`,
        payment: savedPayment,
        document: savedDocument,
        isUpdate,
      };
    } catch (error) {
      console.error(`❌ Error uploading proof of payment for customer ${userId}:`, error);

      // Handle specific database constraint errors
      if (error.code === 'ER_DUP_ENTRY' || error.message?.includes('Duplicate entry')) {
        throw new ConflictException(`A payment with this invoice number already exists. Please try again.`);
      }

      if (error instanceof NotFoundException || error instanceof BadRequestException || error instanceof ConflictException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to upload proof of payment: ${error.message}`);
    }
  }

  /**
   * Create a task for finance team to review proof of payment
   */
  private async createPaymentReviewTask(payment: Payment, invoice: Invoices): Promise<void> {
    try {
      // Find finance users for task assignment
      const financeUsers = await this.usersService.findFinanceUsers();
      // Select a finance user (round-robin or random selection)
      let assignedUser: User | null = null;
      if (financeUsers.length > 0) {
        // Simple round-robin selection based on current time
        const userIndex = Date.now() % financeUsers.length;
        assignedUser = financeUsers[userIndex];
      } else {
      }

      const taskTitle = `Review Proof of Payment for invoice number - ${invoice.invoice_number}`;
      const taskDescription = `Review proof of payment for an Invoice.

      **Payment Details:**
        - Invoice Number: ${invoice.invoice_number}
        - Amount: MWK ${invoice.amount}
        - Payment Method: ${payment.payment_method || 'N/A'}
        - Transaction Reference: ${payment.transaction_reference || 'N/A'}
        - Payment Date: ${payment.created_at ? payment.created_at.toDateString() : 'N/A'}

        Please verify the payment details and approve or reject the payment.`;
      // Check if a task already exists for this application and reassign if needed
      // This ensures only one open task per application at a time
      if (assignedUser) {
        const createTaskDto: CreateTaskDto = {
          task_type: TaskType.PAYMENT_VERIFICATION,
          title: taskTitle,
          description: taskDescription,
          priority: TaskPriority.MEDIUM,
          entity_type: 'payment',
          entity_id: payment.payment_id,
          assigned_to: assignedUser.user_id,
          due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
          metadata: {
            payment_id: payment.payment_id,
            invoice_id: invoice.invoice_id,
            payment_type: 'proof_of_payment_review'
          }
        };
        // Use the first available finance user as the creator, or fall back to admin
        const systemCreatorId = financeUsers.length > 0 ? financeUsers[0].user_id : await this.getSystemCreatorId();
        await this.tasksService.create(createTaskDto, systemCreatorId);
      } else {
        // If no finance user available, create unassigned task
        const taskData: CreateTaskDto = {
          task_type: TaskType.PAYMENT_VERIFICATION,
          title: taskTitle,
          description: taskDescription,
          priority: TaskPriority.MEDIUM,
          entity_type: 'payment',
          entity_id: payment.payment_id,
          due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: {
            payment_id: payment.payment_id,
            invoice_id: invoice.invoice_id,
            payment_type: 'proof_of_payment_review'
          }
        };
        // Use admin user as creator for unassigned tasks
        const systemCreatorId = await this.getSystemCreatorId();
        await this.tasksService.create(taskData, systemCreatorId);
      }

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get a system user ID for creating tasks when no specific user is available
   * Falls back to admin user if no system user exists
   */
  private async getSystemCreatorId(): Promise<string> {
    try {
      // First try to find an admin user
      const adminUser = await this.usersService.findByEmail('<EMAIL>');
      if (adminUser) {
        console.log(`🤖 Using admin user as system creator: ${adminUser.email}`);
        return adminUser.user_id;
      }

      // <NAME_EMAIL>, find any administrator role user
      const adminUsers = await this.usersRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.roles', 'roles')
        .where('roles.name = :adminRole', { adminRole: 'administrator' })
        .andWhere('user.status = :activeStatus', { activeStatus: 'active' })
        .getMany();

      if (adminUsers.length > 0) {
        console.log(`🤖 Using administrator user as system creator: ${adminUsers[0].email}`);
        return adminUsers[0].user_id;
      }

      // Last resort: use any active user
      const anyUser = await this.usersRepository.findOne({
        where: { status: UserStatus.ACTIVE },
        order: { created_at: 'ASC' }
      });

      if (anyUser) {
        console.log(`🤖 Using first active user as system creator: ${anyUser.email}`);
        return anyUser.user_id;
      }

      throw new Error('No active users found to use as system creator');
    } catch (error) {
      console.error('❌ Error finding system creator:', error);
      throw new Error('Failed to find system creator for task creation');
    }
  }

}